import { Button } from '@repo/ui/components/ui/button';
import { BubbleMenu } from '@tiptap/react';
import { ArrowDownToLine, ArrowUpToLine, TableOfContents, Trash } from 'lucide-react';
import { useCallback, useEffect, useState } from 'react';

import type { MenuProps, ShouldShowProps } from './type';
import { getTableRowsCount, isFirstRowSelected, isRowGripSelected } from './utils';
import { Switch } from '@repo/ui/components/ui/switch';
import { TableHeader } from '@repo/editor-common';

export const TableRowMenu = ({ editor, appendTo }: MenuProps) => {
  const [forceUpdate, setForceUpdate] = useState(0);

  // 监听编辑器关键事件以确保菜单及时更新
  useEffect(() => {
    if (!editor) return;

    // 创建事件处理函数
    const handleUpdate = () => {
      setForceUpdate((prev) => prev + 1);
    };

    // 订阅编辑器事件
    editor.on('selectionUpdate', handleUpdate);
    editor.on('transaction', handleUpdate);
    editor.on('focus', handleUpdate);
    editor.on('blur', handleUpdate);

    // 清理函数
    return () => {
      editor.off('selectionUpdate', handleUpdate);
      editor.off('transaction', handleUpdate);
      editor.off('focus', handleUpdate);
      editor.off('blur', handleUpdate);
    };
  }, [editor]);

  const shouldShow = useCallback(
    ({ view, state, from }: ShouldShowProps) => {
      if (!state || !from) {
        return false;
      }

      return isRowGripSelected({ editor, view, state, from });
    },
    [editor],
  );

  const isFirstRow = isFirstRowSelected(editor.state.selection);

  const onAddRowBefore = useCallback(() => {
    editor.chain().focus().addRowBefore().run();
  }, [editor]);

  const onAddRowAfter = useCallback(() => {
    editor.chain().focus().addRowAfter().run();
  }, [editor]);

  const onToggleHeaderRow = useCallback(() => {
    editor.chain().focus().toggleHeaderRow().run();
  }, [editor]);

  const isHeaderRow = editor.isActive(TableHeader);

  const onDeleteRow = useCallback(() => {
    const rowsCount = getTableRowsCount(editor.state.selection);
    if (rowsCount === 1) {
      // 如果只有一行，删除整个表格
      editor.chain().focus().deleteTable().run();
    } else {
      // 否则只删除当前行
      editor.chain().focus().deleteRow().run();
    }
  }, [editor]);

  return (
    <BubbleMenu
      editor={editor}
      pluginKey="tableRowMenu"
      shouldShow={shouldShow}
      updateDelay={0}
      tippyOptions={{
        appendTo: appendTo.current!,
        placement: 'left',
        offset: [0, 0],
        popperOptions: {
          modifiers: [{ name: 'flip', enabled: false }],
        },
      }}
    >
      <div className="inline-flex flex-col gap-0.5 rounded-lg bg-white p-1.5 shadow-lg">
        {isFirstRow && (
          <div className="flex items-center justify-between py-1.5 px-3 text-left text-sm">
            <div className="flex items-center">
              <TableOfContents className="mr-2" size={16} />
              <span>Header row</span>
            </div>
            <Switch
              className="ml-2"
              size="small"
              checked={isHeaderRow}
              onCheckedChange={onToggleHeaderRow}
            />
          </div>
        )}
        <Button
          className="justify-start p-1.5 text-left text-sm rounded-md"
          onClick={onAddRowBefore}
          variant="ghost"
        >
          <ArrowUpToLine className="mr-2" size={16} />
          Add row before
        </Button>
        <Button
          className="justify-start p-1.5 text-left text-sm rounded-md"
          onClick={onAddRowAfter}
          variant="ghost"
        >
          <ArrowDownToLine className="mr-2" size={16} />
          Add row after
        </Button>
        <Button
          className="justify-start p-1.5 text-left text-sm rounded-md"
          onClick={onDeleteRow}
          variant="ghost"
        >
          <Trash className="mr-2" size={16} />
          Delete row
        </Button>
      </div>
    </BubbleMenu>
  );
};

TableRowMenu.displayName = 'TableRowMenu';

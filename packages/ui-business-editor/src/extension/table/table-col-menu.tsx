import { Button } from '@repo/ui/components/ui/button';
import { BubbleMenu } from '@tiptap/react';
import { ArrowLeftToLine, ArrowRightToLine, TableOfContents, Trash } from 'lucide-react';
import { useCallback, useEffect, useState } from 'react';
import { Switch } from '@repo/ui/components/ui/switch';
import { TableHeader } from '@repo/editor-common';

import type { MenuProps, ShouldShowProps } from './type';
import { getTableColumnsCount, isColumnGripSelected, isFirstColumnSelected } from './utils';

export const TableColMenu = ({ editor, appendTo }: MenuProps) => {
  const [forceUpdate, setForceUpdate] = useState(0);

  // 监听编辑器关键事件以确保菜单及时更新
  useEffect(() => {
    if (!editor) return;

    // 创建事件处理函数
    const handleUpdate = () => {
      setForceUpdate((prev) => prev + 1);
    };

    // 订阅编辑器事件
    editor.on('selectionUpdate', handleUpdate);
    editor.on('transaction', handleUpdate);
    editor.on('focus', handleUpdate);
    editor.on('blur', handleUpdate);

    // 清理函数
    return () => {
      editor.off('selectionUpdate', handleUpdate);
      editor.off('transaction', handleUpdate);
      editor.off('focus', handleUpdate);
      editor.off('blur', handleUpdate);
    };
  }, [editor]);

  const shouldShow = useCallback(
    ({ view, state, from }: ShouldShowProps) => {
      if (!state) {
        return false;
      }

      return isColumnGripSelected({ editor, view, state, from: from || 0 });
    },
    [editor],
  );

  const isFirstColumn = isFirstColumnSelected(editor.state.selection);

  const onAddColumnBefore = useCallback(() => {
    editor.chain().focus().addColumnBefore().run();
  }, [editor]);

  const onAddColumnAfter = useCallback(() => {
    editor.chain().focus().addColumnAfter().run();
  }, [editor]);

  const onToggleHeaderCol = useCallback(() => {
    editor.chain().focus().toggleHeaderColumn().run();
  }, [editor]);

  const isHeaderColumn = editor.isActive(TableHeader);

  const onDeleteColumn = useCallback(() => {
    const columnsCount = getTableColumnsCount(editor.state.selection);
    if (columnsCount === 1) {
      // 如果只有一列，删除整个表格
      editor.chain().focus().deleteTable().run();
    } else {
      // 否则只删除当前列
      editor.chain().focus().deleteColumn().run();
    }
  }, [editor]);

  return (
    <BubbleMenu
      editor={editor}
      pluginKey="tableColumnMenu"
      shouldShow={shouldShow}
      updateDelay={0}
      tippyOptions={{
        appendTo: appendTo.current!,
        offset: [0, 5],
        popperOptions: {
          modifiers: [{ name: 'flip', enabled: false }],
        },
      }}
    >
      <div className="inline-flex flex-col gap-0.5 rounded-lg bg-white p-1.5 shadow-lg">
        {isFirstColumn && (
          <div className="flex items-center justify-between py-1.5 px-3 text-left text-sm mr-2">
            <div className="flex items-center">
              <TableOfContents className="mr-2" size={16} />
              <span>Header column</span>
            </div>
            <Switch
              className="ml-2"
              size="small"
              checked={isHeaderColumn}
              onCheckedChange={onToggleHeaderCol}
            />
          </div>
        )}
        <Button
          className="justify-start p-1.5 text-left text-sm rounded-md"
          onClick={onAddColumnBefore}
          variant="ghost"
        >
          <ArrowLeftToLine className="mr-2" size={16} />
          Add column before
        </Button>
        <Button
          className="justify-start p-1.5 text-left text-sm rounded-md"
          onClick={onAddColumnAfter}
          variant="ghost"
        >
          <ArrowRightToLine className="mr-2" size={16} />
          Add column after
        </Button>
        <Button
          className="justify-start p-1.5 text-left text-sm rounded-md"
          onClick={onDeleteColumn}
          variant="ghost"
        >
          <Trash className="mr-2" size={16} />
          Delete column
        </Button>
      </div>
    </BubbleMenu>
  );
};

TableColMenu.displayName = 'TableColMenu';

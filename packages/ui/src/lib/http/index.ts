import { isErrorInfo, type RestErrorInfo } from './error';
import type { HTTPClientConfig, HTTPResponse } from './httpInterfaces';
import { streamReceiver } from './streamReceiver';

const defaultConfig: Required<HTTPClientConfig> = {
  errorReporter: {
    captureException: () => {
      // 服务端环境或无错误报告服务时的空实现
    },
  },
  notificationService: {
    showError: () => {
      // 服务端环境或无通知服务时的空实现
    },
  },
  navigationService: {
    redirectToLogin: () => {},
    redirectTo404: () => {},
  },
  traceProvider: {
    getTraceParent: () => {
      if (typeof window !== 'undefined') {
        return (window as any).__TRACE_PARENT__;
      }
      return undefined;
    },
  },
};

const isPlainObject = (obj: unknown) =>
  obj !== null && typeof obj === 'object' && Object.getPrototypeOf(obj) === Object.prototype;

type FetchOptionsWithoutBody = Omit<NonNullable<Parameters<typeof fetch>[1]>, 'body'>;

const handleFetchOptions = (
  options: {
    silent?: boolean;
    noRedirect?: boolean;
    body?: Record<string, unknown> | BodyInit | unknown[];
  } & FetchOptionsWithoutBody,
) => {
  const { body, ...restOptions } = options;
  const headers = new Headers(restOptions.headers);
  // 默认使用 application/json
  if (!headers.has('Content-Type')) {
    headers.set('Content-Type', 'application/json');
  }

  // 如果 body 是纯对象，并且 content-type 是 application/json，将 body 转换为 json 字符串
  const fetchBody =
    (isPlainObject(body) || Array.isArray(body)) &&
    headers.get('Content-Type')?.startsWith('application/json')
      ? JSON.stringify(body)
      : body;

  return { ...restOptions, headers, body: fetchBody as BodyInit };
};

async function handleErrorResponse(
  response: Response,
  options: {
    silent?: boolean;
    noRedirect?: boolean;
  },
  config: HTTPClientConfig,
): Promise<{ error: RestErrorInfo }> {
  const contentType = response.headers.get('Content-Type');

  if (contentType?.startsWith('application/json')) {
    const json = await response.json();
    if (isErrorInfo(json)) {
      if (json.status === 401 && !options.noRedirect) {
        // 跳转到登录页
        config.navigationService?.redirectToLogin();
        return { error: json };
      } else if (json.status === 404 && !options.noRedirect) {
        // TODO 所有请求 404 都要去 404 页面吗，如果是一些 delete 之类的感觉不用
        // config.navigationService?.redirectTo404();
        return { error: json };
      } else {
        if (!options.silent) {
          config.notificationService?.showError('Oops, something went wrong.', {
            description: `${json.code}: ${json.message}`,
            duration: 5000,
          });
        }
        // !client 的情况，不做处理
      }
    } else {
      config.errorReporter?.captureException(json);
    }
    console.error('callHTTP error:', json);
    return { error: json };
  }

  const text = await response.text();
  const isTimeout = text.includes('FUNCTION_INVOCATION_TIMEOUT');
  const errorInfo = {
    status: isTimeout ? 504 : response.status,
    code: isTimeout ? 'FunctionInvocationTimeout' : response.statusText,
    message: isTimeout ? 'Function invocation timeout.' : text,
  } satisfies RestErrorInfo;
  if (!options.silent) {
    config.notificationService?.showError('Oops, something went wrong.', {
      description: `${errorInfo.code}: ${errorInfo.message}`,
      duration: 5000,
    });
  }
  config.errorReporter?.captureException(errorInfo);
  console.error('callHTTP error:', text);
  return { error: errorInfo };
}

/**
 * 创建一个可配置的 HTTP 客户端
 * @param config HTTP 客户端配置
 * @returns HTTP 客户端函数
 */
function createCallHTTPInternal(config: HTTPClientConfig) {
  return async function callHTTP<T = any>(
    url: string,
    options: {
      silent?: boolean;
      noRedirect?: boolean;
      body?: Record<string, unknown> | BodyInit | unknown[];
    } & FetchOptionsWithoutBody = {},
  ): Promise<HTTPResponse<T>> {
    try {
      const handledOptions = handleFetchOptions(options);
      const response = await fetch(url, handledOptions);

      if (!response.ok) {
        return handleErrorResponse(
          response,
          {
            silent: options.silent,
            noRedirect: options.noRedirect,
          },
          config,
        );
      }

      // 成功的响应
      const contentType = response.headers.get('Content-Type');
      if (contentType?.startsWith('application/json')) {
        const data = await response.json();
        return { data };
      } else {
        const data = await response.text();
        return { data } as HTTPResponse<T>;
      }
    } catch (error) {
      // 处理网络错误等其他异常

      const errorInfo = {
        status: 500,
        code: error instanceof Error ? error.name : 'NetworkError',
        message: error instanceof Error ? error.message : 'Network error',
      } satisfies RestErrorInfo;
      // 上报 Sentry
      if (error instanceof Error && error.name === 'AbortError') {
        // 不上报 AbortError 到 Sentry, 来自用户的手动取消
        console.error('Request aborted by user:', error.message);
        return { error: errorInfo };
      }
      config.errorReporter?.captureException(error);
      if (!options.silent) {
        config.notificationService?.showError('Oops, something went wrong.', {
          description: `${errorInfo.code}: ${errorInfo.message}`,
          duration: 10000,
        });
      }

      return { error: errorInfo } as HTTPResponse<T>;
    }
  };
}

/**
 * 创建一个可配置的 HTTP 流式客户端
 * @param config HTTP 客户端配置
 * @returns HTTP 流式客户端函数
 */
// eslint-disable-next-line @typescript-eslint/no-explicit-any
function createCallHTTPStreamInternal(config: HTTPClientConfig) {
  return async function callHTTPStream<T = any>(
    url: string,
    options: {
      silent?: boolean;
      noRedirect?: boolean;
      body?: Record<string, unknown> | BodyInit;
      shouldContinueCheck?: () => boolean;
      onMessage: (data: T) => void;
    } & FetchOptionsWithoutBody,
  ): Promise<{ error?: RestErrorInfo }> {
    try {
      const handledOptions = handleFetchOptions(options);
      if (!options.shouldContinueCheck) {
        options.shouldContinueCheck = () => true;
      }
      const response = await fetch(url, handledOptions);

      if (!response.ok) {
        const text = await response.text();
        try {
          const json = JSON.parse(text);
          if (isErrorInfo(json) && !options.silent) {
            config.notificationService?.showError('Oops, something went wrong.', {
              description: `${json.code}: ${json.message}`,
              duration: 10000,
            });
            console.error('callHTTPStream error:', json);

            return { error: json };
          }
        } catch (error) {
          // 被 catch 说明 errorInfo 不是 json 格式，而是纯 text;
          // 走下面的处理
          console.error('http stream error:', error);
        }
        const isTimeout = text.includes('FUNCTION_INVOCATION_TIMEOUT');
        const errorInfo = {
          status: isTimeout ? 504 : 500,
          code: isTimeout ? 'FunctionInvocationTimeout' : 'UnknownError',
          message: isTimeout ? 'Function invocation timeout.' : text,
          detail: text,
        } satisfies RestErrorInfo;

        if (!options.silent) {
          config.notificationService?.showError('Oops, something went wrong.', {
            description: `${errorInfo.code}: ${errorInfo.message}`,
            duration: 10000,
          });
        }
        console.error('callHTTPStream error:', errorInfo);
        return { error: errorInfo };
      }

      const reader = response.body!.getReader();
      reader.closed.catch((err) => {
        console.error('stream closed with error', err);
      });
      await streamReceiver<T>(
        reader,
        options.shouldContinueCheck,
        options.onMessage,
        config.errorReporter,
      );

      return { error: undefined };
    } catch (error) {
      // 处理网络错误等其他异常
      const errorInfo = isErrorInfo(error)
        ? error
        : ({
            status: 500,
            code: error instanceof Error ? error.name : 'NetworkError',
            message: error instanceof Error ? error.message : 'Network error',
          } satisfies RestErrorInfo);

      if (error instanceof Error && error.name === 'AbortError') {
        // 不上报 AbortError, 来自用户的手动取消
        console.error('Request aborted by user:', error.message);
        return { error: errorInfo };
      }

      config.errorReporter?.captureException(error);
      console.error('callHTTPStream error:', error);
      if (!options.silent) {
        config.notificationService?.showError('Oops, something went wrong.', {
          description: `${errorInfo.code}: ${errorInfo.message}`,
          duration: 10000,
        });
      }
      return { error: errorInfo };
    }
  };
}

/**
 * 创建 HTTP 客户端
 * @param config 客户端配置
 * @returns HTTP 客户端函数
 */
export function createCallHTTP(config: HTTPClientConfig = {}) {
  const mergedConfig = { ...defaultConfig, ...config };
  return createCallHTTPInternal(mergedConfig);
}

/**
 * 创建 HTTP 流式客户端
 * @param config 客户端配置
 * @returns HTTP 流式客户端函数
 */
export function createCallHTTPStream(config: HTTPClientConfig = {}) {
  const mergedConfig = { ...defaultConfig, ...config };
  return createCallHTTPStreamInternal(mergedConfig);
}

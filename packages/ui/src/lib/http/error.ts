/**
 * REST API 错误信息接口
 */
export interface RestErrorInfo {
  /** HTTP 状态码 */
  status: number;
  /** 错误代码 */
  code: string;
  /** 错误消息 */
  message: string;
  /** 详细信息 */
  detail?: unknown;
}

/**
 * 检查对象是否为 RestErrorInfo
 */
export function isErrorInfo(obj: unknown): obj is RestErrorInfo {
  return (
    obj !== null &&
    typeof obj === 'object' &&
    'status' in obj &&
    'code' in obj &&
    'message' in obj &&
    typeof (obj as RestErrorInfo).status === 'number' &&
    typeof (obj as RestErrorInfo).code === 'string' &&
    typeof (obj as RestErrorInfo).message === 'string'
  );
}

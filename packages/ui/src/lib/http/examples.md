```typescript
/**
 * HTTP 客户端使用示例
 * 展示如何在不同环境中配置和使用 HTTP 客户端
 */

import { createHTTPClient, createCallHTTPStream } from './http';
import type { HTTPClientConfig } from './httpInterfaces';

// ================== 服务端环境配置示例 ==================

/**
 * 服务端环境配置（Node.js/Next.js API 路由）
 */
export const createServerHTTPClient = () => {
  const config: HTTPClientConfig = {
    errorReporter: {
      captureException: (error) => {
        // 服务端错误记录，可以使用 @sentry/node 或其他服务端日志系统
        console.error('Server HTTP Error:', error);
        // 例如: Sentry.captureException(error);
      },
    },
    notificationService: {
      showError: () => {
        // 服务端不需要显示通知
      },
    },
    navigationService: {
      redirectToLogin: () => {
        // 服务端不需要重定向
      },
      redirectTo404: () => {
        // 服务端不需要重定向
      },
    },
    traceProvider: {
      getTraceParent: () => {
        // 服务端可以从请求头或上下文获取 trace 信息
        return undefined;
      },
    },
  };

  return createHTTPClient(config);
};

// ================== 客户端环境配置示例 ==================

/**
 * 网页端环境配置（使用 toast 和 @sentry/react）
 */
export const createWebHTTPClient = () => {
  const config: HTTPClientConfig = {
    errorReporter: {
      captureException: (error) => {
        // 动态导入以避免服务端错误
        if (typeof window !== 'undefined') {
          import('@sentry/react').then((Sentry) => {
            Sentry.captureException(error);
          });
        }
      },
    },
    notificationService: {
      showError: (message, options) => {
        // 动态导入 toast 以避免服务端错误
        if (typeof window !== 'undefined') {
          import('@repo/ui/components/ui/sonner').then(({ toast }) => {
            toast(message, options);
          });
        }
      },
    },
    navigationService: {
      redirectToLogin: () => {
        if (typeof window !== 'undefined') {
          window.location.href = '/sign-in';
        }
      },
      redirectTo404: () => {
        if (typeof window !== 'undefined') {
          window.location.href = '/404';
        }
      },
    },
    traceProvider: {
      getTraceParent: () => {
        // 从全局变量获取 trace 信息
        if (typeof window !== 'undefined') {
          return (window as any).__TRACE_PARENT__;
        }
        return undefined;
      },
    },
  };

  return createHTTPClient(config);
};

/**
 * Next.js 环境配置（使用 @sentry/next）
 */
export const createNextHTTPClient = () => {
  const config: HTTPClientConfig = {
    errorReporter: {
      captureException: (error) => {
        // 根据环境选择合适的 Sentry 包
        if (typeof window === 'undefined') {
          // 服务端
          import('@sentry/nextjs').then((Sentry) => {
            Sentry.captureException(error);
          });
        } else {
          // 客户端
          import('@sentry/nextjs').then((Sentry) => {
            Sentry.captureException(error);
          });
        }
      },
    },
    notificationService: {
      showError: (message, options) => {
        // 只在客户端显示通知
        if (typeof window !== 'undefined') {
          import('@repo/ui/components/ui/sonner').then(({ toast }) => {
            toast(message, options);
          });
        }
      },
    },
    navigationService: {
      redirectToLogin: () => {
        if (typeof window !== 'undefined') {
          // 客户端重定向
          window.location.href = '/sign-in';
        }
        // 服务端可以通过 throw redirect() 或其他方式处理
      },
      redirectTo404: () => {
        if (typeof window !== 'undefined') {
          window.location.href = '/404';
        }
      },
    },
    traceProvider: {
      getTraceParent: () => {
        if (typeof window !== 'undefined') {
          return (window as any).__TRACE_PARENT__;
        }
        // 服务端可以从请求上下文获取
        return undefined;
      },
    },
  };

  return createHTTPClient(config);
};

// ================== 使用示例 ==================

/**
 * 基本使用示例
 */
export async function exampleUsage() {
  // 创建配置好的客户端
  const httpClient = createWebHTTPClient();
  const streamClient = createCallHTTPStream({
    notificationService: {
      showError: (message) => console.log('Error:', message),
    },
  });

  // 使用普通 HTTP 客户端
  const response = await httpClient('/api/user', {
    method: 'GET',
  });

  if (response.error) {
    console.error('请求失败:', response.error);
    return;
  }

  console.log('用户数据:', response.data);

  // 使用流式客户端
  await streamClient('/api/chat/stream', {
    method: 'POST',
    body: { message: 'Hello AI' },
    onMessage: (data) => {
      console.log('收到流式数据:', data);
    },
  });
}

// ================== 全局单例实例（可选） ==================

// 根据环境创建全局实例
const getGlobalHTTPClient = () => {
  if (typeof window === 'undefined') {
    // 服务端环境
    return createServerHTTPClient();
  } else {
    // 客户端环境
    return createWebHTTPClient();
  }
};

// 导出全局实例（向后兼容）
export const globalHTTPClient = getGlobalHTTPClient();
export const globalHTTPStreamClient = createCallHTTPStream();
```

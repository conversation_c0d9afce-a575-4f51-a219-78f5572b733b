import type { RestErrorInfo } from './error';

/**
 * 错误报告服务接口
 */
export interface ErrorReporter {
  /** 报告异常 */
  captureException(error: unknown): void;
}

/**
 * 通知服务接口
 */
export interface NotificationService {
  /** 显示错误通知 */
  showError(message: string, options?: { description?: string; duration?: number }): void;
}

/**
 * 导航服务接口
 */
export interface NavigationService {
  /** 跳转到登录页 */
  redirectToLogin(): void;
  /** 跳转到 404 页面 */
  redirectTo404(): void;
}

/**
 * 追踪提供者接口
 */
export interface TraceProvider {
  /** 获取追踪父级 */
  getTraceParent(): string | undefined;
}

/**
 * HTTP 客户端配置
 */
export interface HTTPClientConfig {
  /** 错误报告服务 */
  errorReporter?: ErrorReporter;
  /** 通知服务 */
  notificationService?: NotificationService;
  /** 导航服务，服务端客户端实现方式不同 */
  navigationService?: NavigationService;
  /** 追踪提供者 */
  traceProvider?: TraceProvider;
}

/**
 * HTTP 响应类型
 */
export type HTTPResponse<T> =
  | { data: T; error?: undefined }
  | { error: RestErrorInfo; data?: undefined };

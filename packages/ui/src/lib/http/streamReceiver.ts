import { isErrorInfo } from './error';
import type { ErrorReporter } from './httpInterfaces';

export async function streamReceiver<T>(
  stream: ReadableStreamDefaultReader<Uint8Array>,
  shouldContinueCheck: () => boolean,
  msgCallback: (data: T) => void,
  errorReporter?: ErrorReporter,
) {
  const decoder = new TextDecoder();
  const closedPromise = stream.closed.then(
    () => ({ done: true, value: null }) as const, // onFulfilled
    (err) => Promise.reject(err), // onRejected ⇒ 继续往外抛
  );

  let buffer = '';
  // eslint-disable-next-line no-constant-condition
  while (true) {
    const { done, value } = await Promise.race([
      stream.read(), // 读数据：也可能因出错而 reject
      closedPromise, // 流正常结束→resolve，出错→reject
    ]);
    if (done) {
      break;
    }
    if (!shouldContinueCheck()) {
      const abortError = new Error('Stream aborted: shouldContinueCheck returned false');
      abortError.name = 'AbortError';
      throw abortError;
    }
    const newContent = decoder.decode(value, { stream: true });

    tryParseError(newContent, errorReporter);

    buffer += newContent;

    if (buffer.includes('\n\n')) {
      const lines = buffer.split('\n\n');
      const dataLines = lines
        .filter((str) => str.startsWith('data: '))
        .map((str) => {
          const content = str.replace(/^data: /, '').replace(/\\\\n\\\\n/g, '\\n\\n');

          try {
            return JSON.parse(content);
          } catch (_error) {
            console.log('Unterminated string in JSON >>>', str);
            console.log('current chunk >>>', newContent);
            return null;
          }
        })
        .filter((line) => line !== null);

      dataLines.map((line: T) => {
        msgCallback(line);
      });
      // 如果最后一行不是空行，则说明最后一行的内容不完整，需要把最后内容继续放回 buffer
      if (lines[lines.length - 1] !== '') {
        buffer = lines[lines.length - 1];
      } else {
        buffer = '';
      }
    }
  }
}

function tryParseError(content: string, errorReporter?: ErrorReporter) {
  let parsedError: unknown;
  try {
    parsedError = JSON.parse(content);
  } catch (_error) {
    // 不做处理，正常情况下就是应该 JSON.parse 失败
    return;
  }

  if (parsedError) {
    if (isErrorInfo(parsedError)) {
      errorReporter?.captureException(new Error(`AI server error: ${parsedError.message}`));

      throw parsedError;
    }
  }
}

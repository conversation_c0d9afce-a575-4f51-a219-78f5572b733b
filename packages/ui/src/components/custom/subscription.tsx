import {
  SubscriptionDtoBillingIntervalEnum as SubscriptionBillingIntervalEnum,
  SubscriptionDtoProductTierEnum as SubscriptionProductTierEnum,
} from '@repo/api/generated-client/snake-case/index';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@repo/ui/components/ui/tabs';
import { cn } from '@repo/ui/lib/utils';
import { Check, CircleQuestionMark, Dot } from 'lucide-react';
import { Button } from '../ui/button';
import { HoverCard, HoverCardContent, HoverCardTrigger } from '../ui/hover-card';

interface SubscriptionProps {
  productTier: SubscriptionProductTierEnum;
  billingInterval: SubscriptionBillingIntervalEnum;
  onUpgrade?: (
    productTier: SubscriptionProductTierEnum,
    billingInterval: SubscriptionBillingIntervalEnum,
  ) => void;
}

const CheckItem = ({ children }: { children: React.ReactNode }) => {
  return (
    <div className="title flex items-baseline gap-2">
      <Check size={14} />
      <span className="flex-1 flex items-center gap-1">{children}</span>
    </div>
  );
};

const DotItem = ({ children }: { children: React.ReactNode }) => {
  return (
    <div className="flex items-baseline gap-2">
      <Dot size={14} />
      <span className="flex-1">{children}</span>
    </div>
  );
};

const AnnualMonthlyPlan = ({
  productTier,
  billingInterval,
  type,
  onUpgrade,
}: {
  productTier: SubscriptionProductTierEnum;
  billingInterval: SubscriptionBillingIntervalEnum;
  type: SubscriptionBillingIntervalEnum;
  onUpgrade?: (
    productTier: SubscriptionProductTierEnum,
    billingInterval: SubscriptionBillingIntervalEnum,
  ) => void;
}) => {
  const isAnnually = type === SubscriptionBillingIntervalEnum.yearly;
  const isMonthly = type === SubscriptionBillingIntervalEnum.monthly;
  const current =
    (isAnnually && billingInterval === SubscriptionBillingIntervalEnum.yearly) ||
    (isMonthly && billingInterval === SubscriptionBillingIntervalEnum.monthly);
  const currentPro = current && productTier === SubscriptionProductTierEnum.pro;
  const currentMax = current && productTier === SubscriptionProductTierEnum.max;
  const toUpgradeAnnuallyPro =
    current && type === 'monthly' && productTier === SubscriptionProductTierEnum.pro;
  const toUpgradeAnnuallyMax =
    current && type === 'monthly' && productTier === SubscriptionProductTierEnum.max;

  const isFree = productTier === SubscriptionProductTierEnum.unknownDefaultOpenApi;

  return (
    <div className="flex flex-col items-center justify-center">
      <div
        className={cn(
          'grid grid-cols-1 md:grid-cols-3 gap-4 w-full mx-auto max-w-[1200px]',
          !isFree && 'md:grid-cols-2 max-w-[800px]',
        )}
      >
        {isFree && (
          <div className="p-8 pt-5 bg-background border border-card-snips rounded-2xl">
            <div className="headline3 text-muted-foreground mb-1">Free</div>
            <div className="body text-caption mb-4">Try YouMind</div>
            <div className="display-medium">$0</div>
            <Button className="w-full my-6" disabled={isFree}>
              {isFree ? 'Your current plan' : 'Get Free'}
            </Button>
            <div className="flex flex-col gap-2">
              <CheckItem>
                2,000 credits per month
                <HoverCard>
                  <HoverCardTrigger>
                    <CircleQuestionMark size={14} className="text-caption" />
                  </HoverCardTrigger>
                  <HoverCardContent className="p-3">
                    <p className="body-strong flex items-baseline gap-1">
                      <CircleQuestionMark size={14} className="text-caption" />
                      With 2,000 credits, you can typically:
                    </p>
                    <div className="flex flex-col gap-1 body ml-2 my-2">
                      <DotItem>Chat around 20 times</DotItem>
                      <DotItem>Generate 10 images</DotItem>
                      <DotItem>Transcribe 10 minutes of audio</DotItem>
                      <DotItem>Parse 5 pages of Office/PDF</DotItem>
                    </div>
                    <p className="footnote text-caption">*Actual usage depends on your input.</p>
                  </HoverCardContent>
                </HoverCard>
              </CheckItem>
              <CheckItem>Unlimited access to small models</CheckItem>
              <CheckItem>Unlimited access to YouMind Browser Extension</CheckItem>
              <CheckItem>Limited access to AI capabilities, including:</CheckItem>
              <div className="ml-4 flex flex-col gap-2">
                <DotItem>Chat on Web, iOS, Extension</DotItem>
                <DotItem>AI Search, Research</DotItem>
                <DotItem>Image, Diagram, and Audio Generation</DotItem>
                <DotItem>AI write, edit, and create content</DotItem>
                <DotItem>AI translator</DotItem>
              </div>
              <CheckItem>Limited access to basic features:</CheckItem>
              <div className="ml-4 flex flex-col gap-2">
                <DotItem>100 times New snip</DotItem>
                <DotItem>Single file uploads up to 2MB</DotItem>
                <DotItem>Audio transcriptions up to 10 minutes</DotItem>
                <DotItem>Office/PDF parsing up to 5 pages</DotItem>
              </div>
            </div>
          </div>
        )}
        <div className="p-8 pt-5 bg-background border border-card-snips rounded-2xl">
          <div className="headline3 text-muted-foreground mb-1">Pro</div>
          <div className="body text-caption mb-4">For everyday productivity</div>
          <div className="flex items-baseline gap-3">
            <div className="display-medium">{isAnnually ? '$200' : '$20'}</div>
            <span className="body text-caption">{isAnnually ? 'per year' : 'per month'}</span>
          </div>
          {toUpgradeAnnuallyPro && (
            <p className="mt-4 px-5 py-3 body text-caption rounded-xl bg-muted">
              You are on a monthly billing plan. Pay annually to save 17%.
            </p>
          )}
          <Button
            className="w-full my-6"
            disabled={currentPro}
            onClick={() => onUpgrade?.(SubscriptionProductTierEnum.pro, type)}
          >
            {currentPro ? 'Your current plan' : 'Get Pro'}
          </Button>
          <div className="flex flex-col gap-2">
            <p className="title">Everything in Free, plus:</p>
            <div className="ml-4 flex flex-col gap-2">
              <CheckItem>10x usage for all AI features</CheckItem>
              <CheckItem>Enhanced AI Capabilities</CheckItem>
              <div className="ml-4 flex flex-col gap-2">
                <DotItem>Access to flagship models</DotItem>
                <DotItem>Access to agent mode</DotItem>
                <DotItem>Ability to use more OpenAl, Claude, Gemini models</DotItem>
                <DotItem>Enhanced Context Awareness</DotItem>
              </div>
              <CheckItem>Professional File Processing</CheckItem>
              <div className="ml-4 flex flex-col gap-2">
                <DotItem>Unlimited New snip</DotItem>
                <DotItem>Single file uploads up to 100MB (50x improvement)</DotItem>
                <DotItem>Supports large office and PDF parsing</DotItem>
                <DotItem>Audio transcriptions up to 120 minutes (12x improvement)</DotItem>
              </div>
              <CheckItem>Professional Creative Tools</CheckItem>
              <div className="ml-4 flex flex-col gap-2">
                <DotItem>Vector Graphics Editor</DotItem>
                <DotItem>More feature-rich voice generation</DotItem>
              </div>
            </div>
          </div>
        </div>
        <div className="p-8 pt-5 bg-background border border-card-snips rounded-2xl">
          <div className="headline3 text-muted-foreground mb-1">Max</div>
          <div className="body text-caption mb-4">Get the most out of YouMind</div>
          <div className="flex items-baseline gap-3">
            <div className="display-medium">{isAnnually ? '$1,000' : '$100'}</div>
            <span className="body text-caption">{isAnnually ? 'per year' : 'per month'}</span>
          </div>
          {toUpgradeAnnuallyMax && (
            <p className="mt-4 px-5 py-3 body text-caption rounded-xl bg-muted">
              You are on a monthly billing plan. Pay annually to save 17%.
            </p>
          )}
          <Button
            className="w-full my-6"
            disabled={currentMax}
            onClick={() => onUpgrade?.(SubscriptionProductTierEnum.max, type)}
          >
            {currentMax ? 'Your current plan' : 'Get Max'}
          </Button>
          <div className="flex flex-col gap-2">
            <p className="title">Everything in Pro, plus:</p>
            <div className="ml-4 flex flex-col gap-2">
              <CheckItem>10x usage than Pro</CheckItem>
              <CheckItem>Ultimate AI Performance</CheckItem>
              <div className="ml-4 flex flex-col gap-2">
                <DotItem>Full access to flagship models</DotItem>
                <DotItem>Full access to agent mode</DotItem>
              </div>
              <CheckItem>Professional File Processing</CheckItem>
              <div className="ml-4 flex flex-col gap-2">
                <DotItem>Single file uploads up to 500MB (250x improvement over Free)</DotItem>
                <DotItem>
                  Audio transcriptions up to 240 minutes (24x improvement over Free)
                </DotItem>
              </div>
              <CheckItem>Automated AI Assistant</CheckItem>
              <div className="ml-4 flex flex-col gap-2">
                <DotItem>Access to auto audio transcription</DotItem>
              </div>
              <CheckItem>Higher output limits for all tasks</CheckItem>
              <CheckItem>Early access to advanced YouMind features</CheckItem>
              <CheckItem>Priority access at high traffic times</CheckItem>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export const Subscription: React.FC<SubscriptionProps> = ({
  productTier,
  billingInterval,
  onUpgrade,
}) => {
  return (
    <>
      <Tabs defaultValue={billingInterval} className="w-full">
        <TabsList variant="pills" className="w-[300px] mx-auto mb-4">
          <TabsTrigger value={SubscriptionBillingIntervalEnum.yearly} className="pr-1 pl-3">
            Annually
            <span className="ml-1 text-[#A25AD9] bg-[rgba(162,90,217,0.12)] footnote rounded-full px-2 py-[3px]">
              2 months free
            </span>
          </TabsTrigger>
          <TabsTrigger value={SubscriptionBillingIntervalEnum.monthly}>Monthly</TabsTrigger>
        </TabsList>
        <TabsContent value={SubscriptionBillingIntervalEnum.yearly}>
          <AnnualMonthlyPlan
            type={SubscriptionBillingIntervalEnum.yearly}
            productTier={productTier}
            billingInterval={billingInterval}
            onUpgrade={onUpgrade}
          />
        </TabsContent>
        <TabsContent value={SubscriptionBillingIntervalEnum.monthly}>
          <AnnualMonthlyPlan
            type={SubscriptionBillingIntervalEnum.monthly}
            productTier={productTier}
            billingInterval={billingInterval}
            onUpgrade={onUpgrade}
          />
        </TabsContent>
      </Tabs>
      <p className="body text-caption mt-10 text-center">
        Prices exclude all taxes, levies and duties.
      </p>
    </>
  );
};

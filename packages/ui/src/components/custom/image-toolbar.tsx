import { Button, ButtonWithTooltip } from '@repo/ui/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@repo/ui/components/ui/dropdown-menu';
import { copyImageToClipboard } from 'copy-image-clipboard';
import { Clipboard, Download, Edit2, Ellipsis, Link, Maximize2 } from 'lucide-react';
import { useState } from 'react';
import { toast } from '../ui/sonner';

interface Props {
  className?: string;
  size?: number;
  onClick?: () => void;
}

export const InsertToThoughtIcon: React.FC<Props> = ({ size, className, onClick }: Props) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 16 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
      onClick={onClick}
      stroke="currentColor"
    >
      <path d="M4.66663 10.6667H9.33329" strokeWidth="1.33333" strokeLinecap="round" />
      <path d="M4.66663 7.33325H6.66663" strokeWidth="1.33333" strokeLinecap="round" />
      <path
        d="M13.3333 3.33325V3.33325C13.3333 2.22868 12.4379 1.33325 11.3333 1.33325H4C2.89543 1.33325 2 2.22868 2 3.33325V12.6666C2 13.7712 2.89543 14.6666 4 14.6666H11.3333C12.4379 14.6666 13.3333 13.7712 13.3333 12.6666V9.33325"
        strokeWidth="1.33333"
        strokeLinecap="round"
      />
      <path
        d="M14.6667 6.00008H9.33337M9.33337 6.00008L10.6667 4.66675M9.33337 6.00008L10.6667 7.33341"
        strokeWidth="1.33333"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export const SaveAsSnipIcon: React.FC<Props> = ({ size, className, onClick }: Props) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 16 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
      onClick={onClick}
      stroke="currentColor"
    >
      <path
        d="M6.66667 14H3.33333C2.97971 14 2.64057 13.8595 2.39052 13.6095C2.14048 13.3594 2 13.0203 2 12.6667V3.33333C2 2.97971 2.14048 2.64057 2.39052 2.39052C2.64057 2.14048 2.97971 2 3.33333 2H12.6667C13.0203 2 13.3594 2.14048 13.6095 2.39052C13.8595 2.64057 14 2.97971 14 3.33333V6.66667"
        strokeWidth="1.33333"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M11.3333 8.66675V14.0001M8.66663 11.3334H14"
        strokeWidth="1.33333"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

// 目前 Thought 宽度
const SVG_DISPLAY_WIDTH_IN_THOUGHT = 720;

export interface ImageToolbarProps {
  src: string;
  type?: 'svg' | 'png';
  alt?: string;
  width?: number | string;
  height?: number | string;
  viewImageEnabled?: boolean;
  downloadEnabled?: boolean;
  copyLinkEnabled?: boolean;
  copyToClipboardEnabled?: boolean;
  insertToThoughtEnabled?: boolean;
  editEnabled?: boolean;
  saveEnabled?: boolean;
  isSaving?: boolean;
  isNewBoard?: boolean;
  onViewImage?: (e: React.MouseEvent) => void;
  onCopyToClipboard?: (e: React.MouseEvent) => void;
  onCopyLink?: (e: React.MouseEvent) => void;
  onDownload?: (e: React.MouseEvent) => void;
  onInsert?: (e: React.MouseEvent) => void;
  onSave?: (e: React.MouseEvent) => void;
  onEdit?: (e: React.MouseEvent) => void;
}

export function ImageToolbar({
  src,
  type = 'png',
  alt = '',
  width,
  height,
  viewImageEnabled = true,
  downloadEnabled = true,
  copyLinkEnabled = true,
  copyToClipboardEnabled = true,
  insertToThoughtEnabled = true,
  saveEnabled = true,
  isSaving = false,
  isNewBoard = false,
  editEnabled = false,
  onViewImage,
  onCopyToClipboard,
  onCopyLink,
  onDownload,
  onInsert,
  onSave,
  onEdit,
}: ImageToolbarProps) {
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);

  const handleCopyLink = (e: React.MouseEvent) => {
    onCopyLink?.(e);
    copyImageLink(
      src,
      () => toast('Copied image link'),
      () => toast('Failed to copy image link'),
    );
  };

  const handleCopyToClipboard = (e: React.MouseEvent) => {
    onCopyToClipboard?.(e);
    if (type === 'png') {
      copyImage(
        src,
        () => toast('Copied image'),
        () => toast('Failed to copy image'),
      );
    } else if (type === 'svg') {
      copySVGImage(
        src,
        () => toast('Copied image'),
        () => toast('Failed to copy image'),
      );
    }
  };

  const handleDownload = async (e: React.MouseEvent) => {
    onDownload?.(e);
    try {
      const imageUrl = src;
      const response = await fetch(imageUrl);

      if (!response.ok) {
        throw new Error('Network response was not ok');
      }

      const blob = await response.blob();
      const blobUrl = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = blobUrl;

      const fileName = alt || imageUrl.split('/').pop() || 'downloaded-image';
      link.download = fileName;

      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      toast('Start downloading image');

      setTimeout(() => URL.revokeObjectURL(blobUrl), 100);
    } catch (err) {
      toast('Failed to download image');
      console.error('Failed to download image:', err);
    }
  };

  const handleViewImage = (e: React.MouseEvent) => {
    onViewImage?.(e);
  };

  const handleInsert = (e: React.MouseEvent) => {
    onInsert?.(e);
  };

  const handleSave = async (e: React.MouseEvent) => {
    onSave?.(e);
  };

  const handleEdit = (e: React.MouseEvent) => {
    onEdit?.(e);
  };

  return (
    <div
      className={`absolute right-4 top-3 z-[100] flex h-8 items-center rounded-full border border-muted bg-card px-2 shadow-md transition-opacity ${isDropdownOpen ? 'opacity-100' : 'opacity-0 group-hover:opacity-100'}`}
    >
      {type === 'svg' && !isNewBoard && editEnabled && (
        <ButtonWithTooltip
          tooltip={'Edit'}
          variant="icon"
          size="sm"
          className="text-muted-foreground"
          onClick={handleEdit}
        >
          <Edit2 />
        </ButtonWithTooltip>
      )}

      {type === 'png' && !isNewBoard && editEnabled && (
        <ButtonWithTooltip
          tooltip={'Edit'}
          variant="icon"
          size="sm"
          className="text-muted-foreground"
          onClick={handleEdit}
        >
          <Edit2 />
        </ButtonWithTooltip>
      )}

      {copyToClipboardEnabled && (
        <ButtonWithTooltip
          tooltip={'Copy to clipboard'}
          variant="icon"
          size="sm"
          className="text-muted-foreground"
          onClick={handleCopyToClipboard}
        >
          <Clipboard />
        </ButtonWithTooltip>
      )}
      {insertToThoughtEnabled && !isNewBoard && (
        <ButtonWithTooltip
          tooltip={'Insert'}
          variant="icon"
          size="sm"
          className="text-muted-foreground"
          onClick={handleInsert}
        >
          <InsertToThoughtIcon />
        </ButtonWithTooltip>
      )}
      {saveEnabled && !isNewBoard && (
        <ButtonWithTooltip
          tooltip={'Save'}
          variant="icon"
          size="sm"
          className="text-muted-foreground"
          disabled={isSaving}
          onClick={handleSave}
        >
          <SaveAsSnipIcon />
        </ButtonWithTooltip>
      )}
      <DropdownMenu modal={false} onOpenChange={setIsDropdownOpen}>
        <DropdownMenuTrigger asChild>
          <Button
            variant="icon"
            size="sm"
            className="text-muted-foreground"
            onClick={(e) => e.stopPropagation()}
          >
            <Ellipsis />
          </Button>
        </DropdownMenuTrigger>
        {/* @see https://github.com/radix-ui/primitives/issues/1839#issuecomment-1708479747 */}
        <DropdownMenuContent className="body z-[999]">
          {viewImageEnabled && (
            <DropdownMenuItem onClick={handleViewImage}>
              <Maximize2 size={16} />
              View image
            </DropdownMenuItem>
          )}
          {downloadEnabled && (
            <DropdownMenuItem onClick={handleDownload}>
              <Download size={16} />
              Download
            </DropdownMenuItem>
          )}
          {copyLinkEnabled && (
            <DropdownMenuItem onClick={handleCopyLink}>
              <Link size={16} />
              Copy link
            </DropdownMenuItem>
          )}
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
}

export async function copyImage(src: string, onSuccess: () => void, onError: () => void) {
  try {
    await copyImageToClipboard(src);
    onSuccess();
  } catch (err) {
    onError();
    console.error('Failed to copy image:', err);
  }
}

export async function copyImageLink(src: string, onSuccess: () => void, onError: () => void) {
  try {
    const fullUrl = new URL(src, window.location.origin).toString();
    await navigator.clipboard.writeText(fullUrl);
    onSuccess();
  } catch (err) {
    onError();
    console.error('Failed to copy image link:', err);
  }
}

const copySVGImage = async (src: string, onSuccess: () => void, onError: () => void) => {
  // const response = await fetch(src);
  // const svgBlob = await response.blob();
  // const clipboardItem = new ClipboardItem({ [svgBlob.type]: svgBlob });

  // console.log(svgBlob.type, svgBlob);

  // await navigator.clipboard.write([clipboardItem]);

  try {
    const response = await fetch(src);
    const svgText = await response.text();

    await navigator.clipboard.writeText(svgText);
    onSuccess();
  } catch (err) {
    onError();
    console.error('Failed to copy SVG image:', err);
  }
};

export function altForSVG(width: number, height: number) {
  const ratio = width / height;
  const displayWidth = Math.max(width, SVG_DISPLAY_WIDTH_IN_THOUGHT);
  const displayHeight = displayWidth / ratio;
  return JSON.stringify({ width: displayWidth, height: displayHeight });
}

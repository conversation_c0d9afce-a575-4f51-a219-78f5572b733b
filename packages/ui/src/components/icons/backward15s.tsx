import React from 'react';

interface Props {
  className?: string;
  size?: number;
}

export const Backward15sIcon: React.FC<Props> = ({ size, className }: Props) => {
  return (
    <svg
      width={size}
      height={size}
      className={className}
      viewBox="0 0 21 20"
      fill="currentColor"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path d="M10.8357 4.51341C14.2699 4.70187 16.9967 7.54569 16.9967 11.027C16.9967 14.6297 14.076 17.5504 10.4733 17.5504C6.87057 17.5504 3.94992 14.6297 3.94992 11.027C3.94992 9.67521 4.36125 8.38648 5.1176 7.30143C5.22362 7.14365 5.26341 6.95054 5.22841 6.7637C5.19341 6.57686 5.0864 6.41125 4.93046 6.30255C4.77452 6.19385 4.58212 6.15076 4.39471 6.18257C4.2073 6.21438 4.03988 6.31853 3.92854 6.4726C2.99653 7.80827 2.4979 9.39829 2.50028 11.027C2.50028 15.4303 6.07001 19 10.4733 19C14.8766 19 18.4463 15.4303 18.4463 11.027C18.4463 6.74513 15.0708 3.2515 10.8357 3.06196V1.18323C10.8357 1.01544 10.7194 0.951292 10.5758 1.03972L6.38422 3.61862C6.24071 3.70705 6.24071 3.85057 6.38422 3.93899L10.5758 6.5179C10.7194 6.60633 10.8357 6.54037 10.8357 6.37438V4.51341Z" />
      <path d="M8.19688 13.5638H9.27323L9.27359 8.85938L7.26367 9.025V9.84658H8.19688V13.5638Z" />
      <path d="M10.8867 8.92606L10.6192 11.5253L11.5843 11.5793C11.6184 11.4861 11.6793 11.4086 11.7673 11.347C11.8558 11.2853 11.9706 11.2545 12.1131 11.2545C12.3106 11.2545 12.4548 11.3277 12.5461 11.4742C12.6375 11.6209 12.6831 11.8213 12.6831 12.0765C12.6831 12.3182 12.644 12.4998 12.5654 12.6212C12.4867 12.7419 12.3475 12.8024 12.1482 12.8024C11.9779 12.8024 11.8416 12.756 11.7387 12.6625C11.6358 12.569 11.5843 12.4403 11.5843 12.2769L10.5301 12.3407L10.5268 12.3599C10.5141 12.7419 10.6707 13.0496 10.9965 13.2819C11.3227 13.5145 11.7061 13.6309 12.1482 13.6309C12.7045 13.6309 13.1122 13.4826 13.3714 13.1862C13.6301 12.8901 13.7599 12.5179 13.7599 12.07C13.7599 11.5687 13.6435 11.1817 13.4109 10.9088C13.1786 10.6359 12.8469 10.504 12.4157 10.5123C12.2666 10.5151 12.1184 10.5365 11.9746 10.5761C11.8398 10.6142 11.7286 10.6642 11.6416 10.7258L11.7594 9.76069H13.6037V8.92606H10.8867Z" />
    </svg>
  );
};

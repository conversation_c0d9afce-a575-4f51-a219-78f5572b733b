interface Props {
  className?: string;
  size: number;
}
export const Earth: React.FC<Props> = ({ size, className }: Props) => {
  return (
    <svg
      width={size}
      height={size}
      className={className}
      viewBox="0 0 14 14"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect width="14" height="14" rx="7" fill="#0A84FF" fillOpacity="0.16" />
      <g clipPath="url(#clip0_4971_24500)">
        <path
          d="M11.899 7.00051C11.899 4.29921 9.70754 2.10938 7.00423 2.10938C4.30088 2.10938 2.10938 4.29921 2.10938 7.00051C2.10938 9.49585 3.97933 11.5546 6.39532 11.8542C6.53151 11.8829 6.6974 11.8996 6.89498 11.8996C6.95044 11.8996 7.00393 11.8967 7.05559 11.8914C9.73523 11.8638 11.899 9.68477 11.899 7.00051ZM11.1991 7.06972C11.185 6.82567 11.1528 6.58679 11.1037 6.35464C11.108 6.27174 11.1092 6.1932 11.107 6.12019C11.1677 6.40405 11.1997 6.69855 11.1997 7.00051C11.1997 7.02363 11.1995 7.04668 11.1991 7.06972ZM2.80865 7.00051C2.80865 6.70405 2.83952 6.4148 2.89809 6.13573C3.07927 6.32951 3.50632 6.32494 3.61046 6.03541C3.79681 6.14646 4.04723 6.16669 4.04723 6.38867C4.04723 7.12128 4.07332 7.9067 4.73896 7.91883C4.75771 7.91908 5.11018 8.05241 5.27793 8.48746C5.33593 8.63783 5.56536 8.48746 5.81695 8.48746C5.94255 8.48746 5.81695 8.69906 5.81695 9.15663C5.81695 9.61243 6.79963 10.3143 6.79963 10.3143C6.79508 10.616 6.80747 10.8599 6.83258 11.0548C6.61076 11.0508 6.42383 11.0802 6.27697 11.1301C4.30647 10.786 2.80865 9.06814 2.80865 7.00051ZM8.03711 11.0649C8.01536 10.9584 7.9202 10.9001 7.74659 10.9457C7.88511 10.3558 7.95246 10.0254 8.24165 9.77444C8.66006 9.41174 8.29147 9.0084 7.97307 9.05594C7.72212 9.09382 7.88071 8.74521 7.65672 8.72593C7.43273 8.70719 7.14021 8.26166 6.81785 8.10836C6.64697 8.02721 6.47903 7.80973 6.21549 7.79999C5.98191 7.79093 5.64054 7.9975 5.64054 7.83827C5.64054 7.32538 5.58861 6.9594 5.57794 6.81325C5.56932 6.69582 5.50119 6.77369 5.81695 6.78128C5.9888 6.78589 5.90486 6.4361 6.07496 6.42244C6.24202 6.40922 6.64011 6.57883 6.74154 6.51123C6.83576 6.44828 7.43415 8.08198 7.43415 6.78127C7.43415 6.62694 7.35422 6.35861 7.43415 6.21245C7.75026 5.63486 8.04619 5.16412 8.01962 5.09526C8.00454 5.05649 7.69621 5.02448 7.44952 5.10726C7.36626 5.13506 7.476 5.26546 7.35642 5.29331C6.90839 5.39674 6.51255 5.1725 6.65117 4.96174C6.79311 4.74573 7.30742 4.86751 7.3525 4.43418C7.37844 4.18596 7.39994 3.89849 7.41432 3.68483C8.0173 3.77912 7.95092 2.90229 7.05435 2.80843C8.86817 2.82966 10.4052 4.00105 10.9692 5.62677C10.9407 5.60075 10.9075 5.58493 10.8692 5.58107C10.5981 4.90398 9.94014 5.39399 10.1633 5.99121C8.96743 6.91051 9.27355 7.55167 9.66646 7.91883C9.87321 8.11183 10.0703 8.4021 10.1987 8.61057C10.059 9.01789 10.7134 8.85478 11.0361 8.16354C10.6256 9.58725 9.48039 10.6998 8.03711 11.0649Z"
          fill="#0A84FF"
        />
      </g>
      <defs>
        <clipPath id="clip0_4971_24500">
          <rect width="9.8" height="9.8" fill="white" transform="translate(2.09961 2.09961)" />
        </clipPath>
      </defs>
    </svg>
  );
};

import React from 'react';

interface Props {
  className?: string;
  size?: number;
}

export const Forward15sIcon: React.FC<Props> = ({ size, className }: Props) => {
  return (
    <svg
      width={size}
      height={size}
      className={className}
      viewBox="0 0 21 20"
      fill="currentColor"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path d="M9.27343 13.5639H8.19707V9.84663H7.26387V9.02505L9.27379 8.85943V13.5639H9.27343ZM10.1106 4.51341C6.6764 4.70187 3.94964 7.54569 3.94964 11.027C3.94964 14.6297 6.87029 17.5504 10.473 17.5504C14.0757 17.5504 16.9964 14.6297 16.9964 11.027C16.9964 9.67521 16.585 8.38648 15.8287 7.30143C15.7227 7.14365 15.6829 6.95054 15.7179 6.7637C15.7529 6.57686 15.8599 6.41125 16.0158 6.30255C16.1718 6.19385 16.3642 6.15076 16.5516 6.18257C16.739 6.21438 16.9064 6.31853 17.0178 6.4726C17.9498 7.80827 18.4484 9.39829 18.446 11.027C18.446 15.4303 14.8763 19 10.473 19C6.06973 19 2.5 15.4303 2.5 11.027C2.5 6.74513 5.87548 3.2515 10.1106 3.06196V1.18323C10.1106 1.01544 10.2269 0.951292 10.3704 1.03972L14.5621 3.61862C14.7056 3.70705 14.7056 3.85057 14.5621 3.93899L10.3704 6.5179C10.2269 6.60633 10.1106 6.54037 10.1106 6.37438V4.51341ZM10.6194 11.5253L10.8869 8.92611H13.6039V9.76074H11.7596L11.6418 10.7258C11.7288 10.6642 11.84 10.6142 11.9748 10.5762C12.1186 10.5366 12.2668 10.5152 12.4159 10.5124C12.8471 10.504 13.1788 10.636 13.4111 10.9089C13.6437 11.1817 13.7601 11.5688 13.7601 12.07C13.7601 12.5179 13.6303 12.8901 13.3716 13.1862C13.1124 13.4827 12.7047 13.6309 12.1484 13.6309C11.7063 13.6309 11.3229 13.5146 10.9967 13.2819C10.6709 13.0496 10.5143 12.7419 10.527 12.3599L10.5303 12.3407L11.5845 12.2769C11.5845 12.4404 11.636 12.569 11.7389 12.6625C11.8418 12.756 11.9781 12.8024 12.1484 12.8024C12.3477 12.8024 12.4869 12.7419 12.5656 12.6212C12.6442 12.4998 12.6833 12.3183 12.6833 12.0765C12.6833 11.8214 12.6377 11.621 12.5463 11.4742C12.455 11.3278 12.3108 11.2546 12.1133 11.2546C11.9708 11.2546 11.856 11.2854 11.7675 11.347C11.6795 11.4086 11.6186 11.4862 11.5845 11.5793L10.6194 11.5253Z" />
    </svg>
  );
};

// 删除 img 标签父级的 p 标签
export const deleteImgParentP = (html: string): string => {
  try {
    // 使用DOMParser解析HTML
    const parser = new DOMParser();
    const doc = parser.parseFromString(html, 'text/html');

    // 查找所有img标签
    const images = doc.querySelectorAll('img');

    // 遍历所有img标签
    images.forEach((img) => {
      const parent = img.parentElement;

      // 检查父元素是否为p标签且只包含这个img元素
      if (parent?.tagName.toLowerCase() === 'p' && parent.childNodes.length === 1) {
        // 在p标签前插入img
        parent.parentNode?.insertBefore(img, parent);
        // 移除空的p标签
        parent.parentNode?.removeChild(parent);
      }
    });

    // 将DOM转回HTML字符串
    return doc.body.innerHTML;
  } catch (error) {
    return html;
  }
};

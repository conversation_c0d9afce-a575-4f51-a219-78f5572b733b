import {
  isApplePodcastUrl,
  isBilibiliUrl,
  isMediumUrl,
  isSpotifyUrl,
  isWeixinUrl,
  isWikipediaUrl,
  isXiaohongshuUrl,
  isXiaoyuzhouUrl,
  isYouTubeUrl,
} from '@repo/common/utilities/url';
import {
  SnipArticleVO,
  SnipStatusEnum,
  SnipTypeEnum,
  SnipVideoVO,
  SnipVO,
  SnipVoiceVO,
} from '../typings/snip';

export const isSnipFetching = (snip: SnipVO) => snip?.status === SnipStatusEnum.fetching;
export const isSnipFetchFailed = (snip: SnipVO) => snip?.status === SnipStatusEnum.fetchFailed;
export const isSnipImageTransfering = (snip: SnipVO) =>
  snip?.status === SnipStatusEnum.imageTransfering;
export const isSnipImageTransferFailed = (snip: SnipVO) =>
  snip?.status === SnipStatusEnum.imageTransferFailed;

export const isYouTube = (snip: SnipVO) =>
  !!(snip.type === SnipTypeEnum.video && isYouTubeUrl((snip as SnipVideoVO).webpage?.url));
export const isBilibili = (snip: SnipVO) =>
  !!(snip.type === SnipTypeEnum.video && isBilibiliUrl((snip as SnipVideoVO).webpage?.url));
export const isVideo = (snip: SnipVO) => isYouTube(snip) || isBilibili(snip);

export const isWikipedia = (snip: SnipVO) =>
  !!(snip.type === SnipTypeEnum.article && isWikipediaUrl((snip as SnipArticleVO).webpage?.url));
export const isMedium = (snip: SnipVO) =>
  !!(snip.type === SnipTypeEnum.article && isMediumUrl((snip as SnipArticleVO).webpage?.url));
export const isWeixin = (snip: SnipVO) =>
  !!(snip.type === SnipTypeEnum.article && isWeixinUrl((snip as SnipArticleVO).webpage?.url));
export const isXiaohongshu = (snip: SnipVO) =>
  !!(snip.type === SnipTypeEnum.article && isXiaohongshuUrl((snip as SnipArticleVO).webpage?.url));
export const isArticle = (snip: SnipVO) => isMedium(snip) || isWeixin(snip) || isXiaohongshu(snip);

export const isXiaoyuzhou = (snip: SnipVO) =>
  !!(snip.type === SnipTypeEnum.voice && isXiaoyuzhouUrl((snip as SnipVoiceVO).webpage?.url));
export const isApplePodcast = (snip: SnipVO) =>
  !!(snip.type === SnipTypeEnum.voice && isApplePodcastUrl((snip as SnipVoiceVO).webpage?.url));
export const isSpotify = (snip: SnipVO) =>
  !!(snip.type === SnipTypeEnum.voice && isSpotifyUrl((snip as SnipVoiceVO).webpage?.url));

export const isPodcast = (snip: SnipVO) =>
  isXiaoyuzhou(snip) || isApplePodcast(snip) || isSpotify(snip);

export const isChatGPT = (snip: SnipVO) =>
  !!(
    snip.type === SnipTypeEnum.article &&
    ((snip as SnipArticleVO).webpage?.url?.includes('https://chatgpt.com/c') ||
      (snip as SnipArticleVO).webpage?.url?.includes('https://chatgpt.com/share'))
  );

export const isPerplexity = (snip: SnipVO) =>
  !!(
    snip.type === SnipTypeEnum.article &&
    (snip as SnipArticleVO).webpage?.url?.includes('perplexity.ai/search')
  );

export const isDeepSeek = (snip: SnipVO) =>
  !!(
    snip.type === SnipTypeEnum.article &&
    (snip as SnipArticleVO).webpage?.url?.includes('https://chat.deepseek.com')
  );

export const isGemini = (snip: SnipVO) =>
  !!(
    snip.type === SnipTypeEnum.article &&
    (snip as SnipArticleVO).webpage?.url?.includes('https://gemini.google.com')
  );

export const isZhihuQuestion = (snip: SnipVO) =>
  !!(
    snip.type === SnipTypeEnum.article &&
    (snip as SnipArticleVO).webpage?.url?.includes('zhihu.com/question') &&
    (snip as SnipArticleVO).content?.raw?.startsWith('[{')
  );

export const isAIChat = (snip: SnipVO) => {
  const url = (snip as SnipArticleVO).webpage?.url || '';
  return !!(
    snip.type === SnipTypeEnum.article &&
    url.includes(`${process.env.NEXT_PUBLIC_YOUMIND_HOST!}/chats/`)
  );
};

export const isReddit = (snip: SnipVO) =>
  !!(
    snip.type === SnipTypeEnum.article &&
    (snip as SnipArticleVO).webpage?.url?.includes('reddit.com')
  );

export const isJike = (snip: SnipVO) =>
  !!(
    snip.type === SnipTypeEnum.article &&
    ((snip as SnipArticleVO).webpage?.url?.includes('https://m.okjike.com') ||
      (snip as SnipArticleVO).webpage?.url?.includes('https://web.okjike.com'))
  );

export const isInstagram = (snip: SnipVO) =>
  !!(
    snip.type === SnipTypeEnum.article &&
    (snip as SnipArticleVO).webpage?.url?.includes('https://instagram.com')
  );

export const isTwitter = (snip: SnipVO) =>
  !!(
    snip.type === SnipTypeEnum.article &&
    (snip as SnipArticleVO).webpage?.url?.includes('https://x.com')
  );

/**
 * e.g. 1 hr 10 min
 * e.g. 10 min
 * e.g. 30 sec
 */
export function formatSeconds(seconds: number) {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const remainingSeconds = seconds % 60;

  if (hours > 0) {
    return `${hours} hr ${minutes} min`;
  } else if (minutes > 0) {
    return `${minutes} min`;
  } else {
    return `${remainingSeconds} sec`;
  }
}

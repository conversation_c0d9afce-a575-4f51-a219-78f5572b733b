'use client';

import { use<PERSON><PERSON> } from 'jotai';
import { snipDetail<PERSON>tom } from './atoms';
import Article from './modules/article';
import { OnlineVideo } from './modules/online-video';
import Podcast from './modules/podcast';
import { SnipTypeEnum } from './typings/snip';

export function SnipContainer() {
  const [snip] = useAtom(snipDetailAtom);
  switch (snip?.type) {
    case SnipTypeEnum.video:
      return <OnlineVideo />;
    case SnipTypeEnum.article:
      return <Article />;

    case SnipTypeEnum.voice:
      return <Podcast />;
    // case SnipTypeEnum.SNIPPET:
    //   return <TextSnippet />;
    // case SnipTypeEnum.UNKNOWN_WEBPAGE:
    //   return <Unknown readonly={readonly} />;
    // case SnipTypeEnum.OTHER_WEBPAGE:
    //   return <Other />;
    // case SnipTypeEnum.IMAGE:
    //   return <Image />;
    // case SnipTypeEnum.PDF:
    //   return <PDF />;
    // case SnipTypeEnum.OFFICE:
    //   return <TextFileContent />;
    // case SnipTypeEnum.TEXT_FILE:
    //   return <TextFileContent />;
    default:
      return '待补充';
  }
}

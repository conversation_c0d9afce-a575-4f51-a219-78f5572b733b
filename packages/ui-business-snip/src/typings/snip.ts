import {
  ArticleDto,
  BlockContentDto,
  GetSnipResponseDtoClass,
  OtherWebpageDto,
  SnipFrom,
  SnipStatus,
  SnipType,
  StreamDataTypeEnum,
  VideoDto,
  VoiceDto,
} from '@repo/api/generated-client/snake-case/index';

export type SnipVO = GetSnipResponseDtoClass;
export type SnipVideoVO = VideoDto;
export type SnipArticleVO = ArticleDto;
export type SnipVoiceVO = VoiceDto;
export const SnipTypeEnum = SnipType;
export const SnipStatusEnum = SnipStatus;
export type BlockContentVO = BlockContentDto;
export type SnipOtherWebpageVO = OtherWebpageDto;
export const SnipFromEnum = SnipFrom;
export { StreamDataTypeEnum };

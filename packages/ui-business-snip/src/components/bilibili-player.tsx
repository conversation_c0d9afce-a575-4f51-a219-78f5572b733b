'use client';

import { VideoDto } from '@repo/api/generated-client/snake-case/index';
import { SafeParse } from '@repo/common/utilities/object';
import { useAtom } from 'jotai';
import React, { useEffect, useMemo, useState } from 'react';
import { snipDetailAtom } from '../atoms';
import { convertTimestampToSeconds } from '../hooks/scoped/useYouTubePlayer';

export const BilibiliPlayer: React.FC = () => {
  const [snip] = useAtom(snipDetailAtom);
  const { extra: e } = snip as VideoDto;

  const baseUrl = useMemo(() => {
    const extra = SafeParse(e || '');
    const { cid, bvid } = extra as {
      cid: string;
      bvid: string;
      aid: string;
    };
    if (!cid || !bvid) return '';
    return `//player.bilibili.com/player.html?bvid=${bvid}&cid=${cid}`;
  }, [snip!.extra]);

  const [seekTime, setSeekTime] = useState<number>(0);

  useEffect(() => {
    // 监听 youmind_seek_bilibili_iframe
    window.addEventListener('message', (event) => {
      if (event.data.type === 'youmind_seek_bilibili_iframe') {
        const timestamp = event.data.timestamp;
        const seconds = convertTimestampToSeconds(timestamp);
        setSeekTime(seconds);
      }
    });
  }, []);

  if (!baseUrl) return null;

  return (
    <div className="z-10">
      <iframe
        width={'100%'}
        height={378}
        src={`${baseUrl}&t=${seekTime}`}
        allowFullScreen
        sandbox="allow-top-navigation allow-same-origin allow-forms allow-scripts"
        className="rounded-xl"
      ></iframe>
    </div>
  );
};

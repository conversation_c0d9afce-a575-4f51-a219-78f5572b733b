import { Button } from '@repo/ui/components/ui/button';
import { useState } from 'react';
import {
  type EnrichedQuotedTweet,
  type EnrichedTweet,
  getMediaUrl,
  getMp4Video,
} from 'react-tweet';
import type { MediaAnimatedGif, MediaVideo } from 'react-tweet/api';

type Props = {
  tweet: EnrichedTweet | EnrichedQuotedTweet;
  media: MediaAnimatedGif | MediaVideo;
};

export const TweetMediaVideo = ({ media }: Props) => {
  const [playButton, setPlayButton] = useState(true);
  const [isPlaying, setIsPlaying] = useState(false);
  const [ended, setEnded] = useState(false);
  const mp4Video = getMp4Video(media);
  let timeout = 0;

  return (
    <>
      <video
        className="absolute top-0 bottom-0 left-0 object-cover object-center w-full h-full m-0"
        poster={getMediaUrl(media, 'small')}
        controls={!playButton}
        muted
        preload="none"
        tabIndex={playButton ? -1 : 0}
        onPlay={() => {
          if (timeout) window.clearTimeout(timeout);
          if (!isPlaying) setIsPlaying(true);
          if (ended) setEnded(false);
        }}
        onPause={() => {
          // When the video is seeked (moved to a different timestamp), it will pause for a moment
          // before resuming. We don't want to show the message in that case so we wait a bit.
          if (timeout) window.clearTimeout(timeout);
          timeout = window.setTimeout(() => {
            if (isPlaying) setIsPlaying(false);
            timeout = 0;
          }, 100);
        }}
        onEnded={() => {
          setEnded(true);
        }}
      >
        <source src={mp4Video.url} type={mp4Video.content_type} />
      </video>

      {playButton && (
        <Button
          variant="ghost"
          iconOnly
          // className="relative w-8 h-8 rounded-full"
          onClick={(e) => {
            const video = e.currentTarget.previousSibling as HTMLMediaElement;
            e.preventDefault();
            e.stopPropagation();
            setPlayButton(false);
            setIsPlaying(true);
            video.play();
            video.focus();
          }}
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width={`${32}`}
            height={`${32}`}
            viewBox="0 0 24 24"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
            className="lucide lucide-circle-play"
            fill="black"
            stroke="black"
          >
            <circle cx="12" cy="12" r="10" />
            <polygon points="10 8 16 12 10 16 10 8" fill="white" stroke="white" />
          </svg>
        </Button>
      )}

      {/* {ended && (
        <a
          href={tweet.url}
          className={clsx(s.anchor, s.viewReplies)}
          target="_blank"
          rel="noopener noreferrer"
        >
          View replies
        </a>
      )} */}
    </>
  );
};

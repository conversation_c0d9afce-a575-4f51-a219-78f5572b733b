/**
 * @see https://github.com/vercel/react-tweet
 */

import { Skeleton } from '@repo/ui/components/ui/skeleton';
import { cn } from '@repo/ui/lib/utils';
import type { ReactNode } from 'react';
import type { TweetProps } from 'react-tweet';
import type { Tweet as T } from 'react-tweet/api';
import { TweetDetail } from './tweet-detail';
import { TweetSource } from './tweet-source';

export const TweetContainer = ({
  className,
  children,
}: {
  className?: string;
  children: ReactNode;
}) => (
  <div className={cn('', className)}>
    <article>{children}</article>
  </div>
);

const TweetSkeleton = () => (
  <TweetContainer className="!mx-auto">
    <Skeleton style={{ height: '3rem', marginBottom: '0.75rem' }} />
    <Skeleton style={{ height: '6rem', margin: '0.5rem 0' }} />
    <Skeleton
      style={{
        height: '2rem',
      }}
    />
    <Skeleton
      style={{
        height: '2rem',
        borderRadius: '9999px',
        marginTop: '0.5rem',
      }}
    />
  </TweetContainer>
);

const TweetThumbnailSkeleton = () => (
  <TweetContainer className="!mx-auto">
    <Skeleton style={{ height: '3rem', marginBottom: '0.75rem' }} />
    <Skeleton style={{ height: '6rem', margin: '0.5rem 0' }} />
  </TweetContainer>
);

const TweetContent = ({
  tweet,
  components,
  thumbnail,
  isComment,
}: TweetProps & {
  tweet: T | null;
  thumbnail?: boolean;
  isComment?: boolean;
}) => {
  return thumbnail ? (
    <TweetSource tweet={tweet!} components={components} />
  ) : (
    <TweetDetail tweet={tweet!} components={components} isComment={isComment} />
  );
};

export const Tweet = ({
  ...props
}: TweetProps & {
  tweet: T | null;
  thumbnail?: boolean;
  isComment?: boolean;
}) => {
  const { tweet, thumbnail } = props;
  return tweet ? (
    <TweetContent {...props} />
  ) : thumbnail ? (
    <TweetThumbnailSkeleton />
  ) : (
    <TweetSkeleton />
  );
};

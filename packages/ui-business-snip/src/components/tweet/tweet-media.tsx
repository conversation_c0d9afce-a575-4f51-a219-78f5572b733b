import { cn } from '@repo/ui/lib/utils';
import { Fragment } from 'react';
import {
  type EnrichedQuotedTweet,
  type EnrichedTweet,
  getMediaUrl,
  type TwitterComponents,
} from 'react-tweet';
import type { MediaDetails } from 'react-tweet/api';

import { TweetMediaVideo } from './tweet-media-video';

const getSkeletonStyle = (media: MediaDetails, itemCount: number) => {
  let paddingBottom = 56.25; // default of 16x9

  // if we only have 1 item, show at original ratio
  if (itemCount === 1)
    paddingBottom = (100 / media.original_info.width) * media.original_info.height;

  // if we have 2 items, double the default to be 16x9 total
  if (itemCount === 2) paddingBottom = paddingBottom * 2;

  return {
    width: media.type === 'photo' ? undefined : 'unset',
    paddingBottom: `${paddingBottom}%`,
  };
};

type Props = {
  tweet: EnrichedTweet | EnrichedQuotedTweet;
  components?: TwitterComponents;
  quoted?: boolean;
};

export const TweetMedia = ({ tweet, quoted }: Props) => {
  const length = tweet.mediaDetails?.length ?? 0;

  return (
    <div
      className={cn('relative mt-3 overflow-hidden', {
        'rounded-md': !quoted,
      })}
    >
      <div
        className={cn('grid h-full w-full grid-cols-1 gap-2', {
          'grid grid-cols-2': length > 1,
          'grid grid-rows-2': length > 4,
        })}
      >
        {tweet.mediaDetails?.map((media, i) => (
          <Fragment key={media.media_url_https}>
            {media.type === 'photo' ? (
              <div
                key={media.media_url_https}
                // href={tweet.url}
                className={cn(
                  'relative flex h-full w-full items-center justify-center outline-none',
                  {
                    'row-span-2': length === 3 && i === 0,
                  },
                )}
                // target="_blank"
                // rel="noopener noreferrer"
              >
                <div className="block w-full pb-[56.25%]" style={getSkeletonStyle(media, length)} />
                <img
                  src={getMediaUrl(media, 'small')}
                  alt={media.ext_alt_text || 'Image'}
                  className="absolute bottom-0 left-0 top-0 m-0 h-full w-full object-cover object-center"
                  draggable
                />
              </div>
            ) : (
              <div
                key={media.media_url_https}
                className="relative flex h-full w-full items-center justify-center outline-none"
              >
                <div className="block w-full pb-[56.25%]" style={getSkeletonStyle(media, length)} />
                <TweetMediaVideo tweet={tweet} media={media} />
              </div>
            )}
          </Fragment>
        ))}
      </div>
    </div>
  );
};

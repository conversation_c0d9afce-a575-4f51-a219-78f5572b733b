import { enrichTweet, QuotedTweet, TweetInReplyTo, type TwitterComponents } from 'react-tweet';
import type { Tweet } from 'react-tweet/api';
import { TweetBody } from './tweet-body';
import { TweetContainer } from './tweet-container';
import { TweetHeader } from './tweet-header';
import { TweetMedia } from './tweet-media';

type Props = {
  tweet: Tweet;
  components?: TwitterComponents;
};

export const TweetSource = ({ tweet: t, components }: Props) => {
  const tweet = enrichTweet(t);
  return (
    <TweetContainer className="!mx-auto !my-0">
      <TweetHeader tweet={tweet} />
      {tweet.in_reply_to_status_id_str && <TweetInReplyTo tweet={tweet} />}
      <TweetBody tweet={tweet} />
      {tweet.mediaDetails?.length ? <TweetMedia tweet={tweet} components={components} /> : null}
      {tweet.quoted_tweet && <QuotedTweet tweet={tweet.quoted_tweet} />}
    </TweetContainer>
  );
};

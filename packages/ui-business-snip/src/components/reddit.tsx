import { SimpleLoading } from '@repo/ui/components/custom/simple-loading';
import { SnipArticleVO } from '../typings/snip';

const WIDTH = 464;

export const Reddit = ({ snip }: { snip: SnipArticleVO }) => {
  if (!snip.webpage?.url) return null;

  const url = new URL(snip.webpage.url);
  // 把域名替换为 embed.reddit.com
  url.hostname = 'embed.reddit.com';

  return (
    <div className="relative mx-auto px-4 pt-4" style={{ width: WIDTH }}>
      <iframe
        className="relative z-20 h-[calc(100vh-120px)] w-full"
        allowFullScreen
        src={url.toString()}
      />

      <div className="absolute inset-0 z-0 flex h-[25%] items-center justify-center">
        <SimpleLoading />
      </div>
    </div>
  );
};

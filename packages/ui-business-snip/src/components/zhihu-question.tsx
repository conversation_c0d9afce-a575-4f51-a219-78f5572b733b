'use client';

import { Button } from '@repo/ui/components/ui/button';
import { cn } from '@repo/ui/lib/utils';
import { SquareArrowOutUpRight, ThumbsUp } from 'lucide-react';
import { useEffect, useState } from 'react';
import { SnipArticleVO, SnipVO } from '../typings/snip';
import { ArticleContent } from './article-content';
import { OpenSource } from './open-source';

interface Answer {
  id: string;
  author_name: string;
  author_avatar: string;
  author_badge: string;
  content: string;
  plain: string;
  created_time: string;
  vote_count: number;
}

export type AnswerList = Answer[];

export interface ZhihuQuestionProps {
  snip: SnipArticleVO;
}

export default function ZhihuQuestion({ snip }: ZhihuQuestionProps) {
  const [answerList, setAnswerList] = useState<AnswerList>([]);

  useEffect(() => {
    try {
      if (!snip) {
        return;
      }
      const messages = snip.content;
      const thread = JSON.parse(messages.raw);
      setAnswerList(thread);
    } catch (error) {
      console.error('Failed to parse thread', error);
    }
  }, [snip]);

  return (
    <div className={cn('mx-auto max-w-[40em] px-[1em] pb-4 text-lg text-foreground')}>
      <div className="group mb-4 p-4 text-3xl font-medium leading-10 text-foreground">
        {snip.title}
        <OpenSource snip={snip as unknown as SnipVO} />
      </div>
      {answerList.map((answer, index) => {
        const html = answer.content || '';

        return (
          <div key={index}>
            <div className="mb-1 flex items-center gap-3 px-4">
              <img
                className="h-[38px] w-[38px] rounded-[2px] object-cover"
                src={answer.author_avatar}
                alt="author avatar"
              />
              <div className="text-sm">
                <div className="font-medium text-foreground">{answer.author_name}</div>
                <div className="text-muted-foreground">{answer.author_badge}</div>
              </div>
            </div>

            <ArticleContent source={html} className="zhihu-content" />

            <div className="pl-4">
              {answer.created_time ? (
                <div className="text-sm text-muted-foreground">{answer.created_time}</div>
              ) : null}

              <div className="mt-2 flex items-center gap-2">
                {answer.vote_count ? (
                  <a href={`https://www.zhihu.com/answer/${answer.id}`} target="_blank">
                    <button
                      className="flex h-8 items-center justify-center gap-1 rounded-[3px] px-[10px] text-sm text-muted-foreground hover:opacity-70"
                      style={{
                        color: 'rgb(23, 114, 246)',
                        backgroundColor: 'oklch(0.581722 0.213797 259.318 / 0.1)',
                      }}
                    >
                      <ThumbsUp size={16} />
                      {answer.vote_count}
                    </button>
                  </a>
                ) : null}
                <a href={`https://www.zhihu.com/answer/${answer.id}`} target="_blank">
                  <Button
                    variant="outline"
                    size="icon"
                    className="h-8 w-8 rounded-full border-none text-muted-foreground"
                  >
                    <SquareArrowOutUpRight size={16} />
                  </Button>
                </a>
              </div>

              {index !== answerList.length - 1 && <div className="my-5 h-[1px] w-full bg-border" />}
            </div>
          </div>
        );
      })}
    </div>
  );
}

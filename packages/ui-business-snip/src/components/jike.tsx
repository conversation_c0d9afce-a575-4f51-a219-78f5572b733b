import { SimpleLoading } from '@repo/ui/components/custom/simple-loading';
import { SnipArticleVO } from '../typings/snip';

export const Jike = ({ snip }: { snip: SnipArticleVO }) => {
  if (!snip.webpage?.url) return null;

  const url = new URL(snip.webpage.url);

  return (
    <div className="relative mx-auto px-4 pt-4">
      <iframe
        className="relative z-20 h-[calc(100vh-120px)] w-full"
        allowFullScreen
        src={url.toString()}
      />

      <div className="absolute inset-0 z-0 flex h-[25%] items-center justify-center">
        <SimpleLoading />
      </div>
    </div>
  );
};

@import "./code.css";

.ym-reader-container {
  margin: 0 auto;
  font-size: 16px;
  max-width: 44em;
  line-height: 24px;
  white-space: normal;
}

.ym-reader-container ol {
  list-style: decimal;
}

.ym-reader-container mark {
  background-color: rgba(255, 204, 0, 0.24);
}

.ym-reader-content pre {
  background-color: #1f2937;
  color: #e5e7eb;
  overflow-x: auto;
  white-space: pre;
  padding: 24px 2em;
}

.ym-reader-content pre > code {
  padding: 0;
  margin: 0;
  white-space: pre;
  background: transparent;
  width: max-content;
}

.ym-reader-content {
  font-size: 16px;
  line-height: 24px;
  padding: 0px 16px;

  word-break: break-all;
}

@media print {
  .ym-reader-content p,
  .ym-reader-content code,
  .ym-reader-content pre,
  .ym-reader-content blockquote,
  .ym-reader-content ul,
  .ym-reader-content ol,
  .ym-reader-content li,
  .ym-reader-content figure,
  .ym-reader-content .wp-caption {
    margin: 0 0 10px !important;
    padding: 0 !important;
  }
}

.ym-reader-content h1,
.ym-reader-content h2,
.ym-reader-content h3,
.ym-reader-content h4,
.ym-reader-content h5 {
  font-weight: bold;
}

.ym-reader-content h1 {
  font-size: 2em;
  line-height: 1.25em;
}

.ym-reader-content h2 {
  font-size: 1.5em;
  line-height: 1.4em;
}

.ym-reader-content h3 {
  font-size: 1.25em;
  line-height: 1.5em;
}

.ym-reader-content h4 {
  font-size: 1em;
  line-height: 1.75em;
}

.ym-reader-content h5 {
  font-size: 1em;
  line-height: 1.85em;
}

.ym-reader-content a:link {
  text-decoration: underline;
  font-weight: normal;
}

.ym-reader-content a:link,
.ym-reader-content a:link:hover,
.ym-reader-content a:link:active {
  color: var(--link-foreground);
}

.ym-reader-content a:visited {
  color: var(--visited-link-foreground);
}

.ym-reader-content * {
  max-width: 100%;
  height: auto;
  margin: revert;
}

.zhihu-content .ym-reader-content * {
  margin: initial;
}

.zhihu-content .ym-reader-content * {
  font-size: 15px;
}

.ym-reader-content p,
.ym-reader-content section,
.ym-reader-content code,
.ym-reader-content blockquote,
.ym-reader-content ul,
.ym-reader-content ol,
.ym-reader-content li,
.ym-reader-content figure,
.ym-reader-content .wp-caption {
  padding: 10px 0px;
  border-radius: 5px;
}

.ym-reader-content section {
  margin: 16px 0;
}

.ym-reader-content section > p:first-child {
  margin: 0;
  padding: 0;
}

.ym-reader-content li > p,
.ym-reader-content li > code,
.ym-reader-content li > blockquote,
.ym-reader-content li > figure,
.ym-reader-content li > .wp-caption {
  padding: 0px;
  margin: 0px;
}

.ym-reader-content.chat-content p,
.ym-reader-content.chat-content section,
.ym-reader-content.chat-content code,
.ym-reader-content.chat-content blockquote,
.ym-reader-content.chat-content ul,
.ym-reader-content.chat-content ol,
.ym-reader-content.chat-content li,
.ym-reader-content.chat-content figure,
.ym-reader-content.chat-content .wp-caption {
  padding: 0px 0px;
  margin: 6px 0px;
  border-radius: 5px;
}

.ym-reader-content figure {
  margin: 0;
}

.ym-reader-content li {
  margin-bottom: 0;
}

.ym-reader-content li > ul,
.ym-reader-content li > ol {
  margin-bottom: -10px;
}

.ym-reader-content p > img:only-child,
.ym-reader-content p > a:only-child > img:only-child,
.ym-reader-content .wp-caption img,
.ym-reader-content figure img {
  display: block;
}

.ym-reader-content img[ym-reader-center] {
  margin-inline: auto;
}

.ym-reader-content .caption,
.ym-reader-content figcaption {
  font-size: 0.8em;
  line-height: 1.48em;
  font-style: italic;
  /* 左右居中 */
  text-align: center;
  margin-top: 12px;
}

.ym-reader-content blockquote {
  padding: 0;
  padding-inline-start: 16px;
  border-inline-start-width: 0.25em;
  border-inline-start-color: hsl(var(--muted));
  margin: 1.6em 0;
}

.ym-reader-content strong {
  font-weight: 700;
}

.ym-reader-content ul,
.ym-reader-content ol {
  padding: 0;
}

.ym-reader-content ul {
  padding-inline-start: 30px;
  list-style: disc;
}

.ym-reader-content ol {
  padding-inline-start: 30px;
  list-style: decimal;
}

.ym-reader-content table,
.ym-reader-content th,
.ym-reader-content td {
  border: 1px solid currentColor;
  border-collapse: collapse;
  padding: 6px;
  vertical-align: top;
}

.ym-reader-content table {
  margin: 5px;
}

/* Visually hide (but don't display: none) screen reader elements */
.ym-reader-content .visually-hidden,
.ym-reader-content .visuallyhidden,
.ym-reader-content .sr-only {
  display: inline-block;
  width: 1px;
  height: 1px;
  margin: -1px;
  overflow: hidden;
  padding: 0;
  border-width: 0;
}

/* Hide elements with common "hidden" class names */
.ym-reader-content .hidden,
.ym-reader-content .invisible {
  display: none;
}

/* Enforce wordpress and similar emoji/smileys aren't sized to be full-width,
 * see bug 1399616 for context. */
.ym-reader-content img.wp-smiley,
.ym-reader-content img.emoji {
  display: inline-block;
  border-width: 0;
  /* height: auto is implied from `.ym-reader-content *` rule. */
  width: 1em;
  margin: 0 0.07em;
  padding: 0;
}

/* Provide extra spacing for images that may be aided with accompanying element such as <figcaption> */
.ym-reader-block-img:not(:last-child) {
  margin-block-end: 12px;
}

.ym-reader-wide-table {
  overflow-x: auto;
  display: block;
}

.ym-reader-content code {
  padding: 3px 5px;
  margin: 0px 4px;
}

.ym-reader-content img {
  margin: auto;
  max-width: 100%;
  height: auto;
}

.ym-reader-content.chat-content {
  font-size: 16px;
  line-height: 24px;
}

.ym-reader-content.chat-content ol {
  margin-left: 20px;
  list-style: decimal;
}

.ym-reader-content.chat-content ul {
  margin-left: 20px;
}

.ym-reader-content.chat-content img {
  margin: 0;
  max-width: 62%;
  height: auto;
  border-radius: 12px;
}

.perplexity-chat .ym-reader-content a {
  margin-left: 4px;
  /* 去掉下划线 */
  font-size: 12px;
  opacity: 0.7;
}

.perplexity-chat .ym-reader-content a:hover {
  opacity: 1;
}

.ym-reader-content.chat-content .not-prose {
  border-left: 2px solid hsl(var(--muted));
  color: hsl(var(--caption));
  padding-left: 8px;
  /* margin-left: -16px; */
  margin-bottom: 20px;
  font-size: 14px;
  line-height: 1.5;
}

'use client';

import clsx from 'clsx';
import React, { useEffect, useMemo, useRef } from 'react';

import './index.css';
import { useSnipContext } from '../../context';
import { deleteImgParentP } from '../../utils/html';

export interface ArticleContentProps extends React.HTMLAttributes<HTMLDivElement> {
  title?: string;
  extractTimestamp?: boolean;
  source: string;
  type?: 'chat' | 'article';
  onHashLinkClick?: (href: string) => void;
}

const MemoizedContent = React.memo(
  React.forwardRef<HTMLDivElement, { source: string; type: 'chat' | 'article' }>(
    ({ source, type }, ref) => {
      const containerRef = useRef<HTMLDivElement>(null);
      const { components } = useSnipContext();
      const ImagePreview = components.ImagePreview;

      useEffect(() => {
        if (containerRef.current) {
          const links = containerRef.current.getElementsByTagName('a');
          for (const link of links) {
            link.setAttribute('target', '_blank');
          }
        }
      }, [source, containerRef]);

      const cleanSource = useMemo(() => {
        return deleteImgParentP(source);
      }, [source]);

      // 解析HTML并替换img标签为ImagePreview组件
      const processedContent = useMemo(() => {
        // 如果环境不支持DOM，如服务器端渲染环境
        if (typeof document === 'undefined') {
          return (
            <div
              className={clsx('ym-reader-content text-foreground', {
                'chat-content': type === 'chat',
              })}
              dangerouslySetInnerHTML={{
                __html: cleanSource,
              }}
            />
          );
        }

        try {
          // 使用DOMParser解析HTML
          const parser = new DOMParser();
          const doc = parser.parseFromString(cleanSource, 'text/html');

          // 查找所有图片元素
          const imgElements = doc.querySelectorAll('img');

          // 如果没有图片，直接返回原始HTML
          if (imgElements.length === 0) {
            return (
              <div
                className={clsx('ym-reader-content text-foreground', {
                  'chat-content': type === 'chat',
                })}
                dangerouslySetInnerHTML={{
                  __html: cleanSource,
                }}
              />
            );
          }

          // 使用更简单的方法：直接将HTML字符串分成文本和图片的片段
          const content: React.ReactNode[] = [];
          const tempHtml = cleanSource;
          let imgIndex = 0;
          const allImageSources: string[] = [];

          // 正则表达式匹配图片标签
          const imgRegex = /<img[^>]+>/gi;
          let match;
          let lastIndex = 0;

          // 预先收集所有图片 src
          while ((match = imgRegex.exec(tempHtml)) !== null) {
            const imgTag = match[0];
            const tempDiv = document.createElement('div');
            tempDiv.innerHTML = imgTag;
            const img = tempDiv.querySelector('img');
            if (img) {
              allImageSources.push(img.getAttribute('src') || '');
            }
          }
          // 重置正则表达式状态
          imgRegex.lastIndex = 0;
          lastIndex = 0;

          while ((match = imgRegex.exec(tempHtml)) !== null) {
            // 添加图片前的HTML
            const beforeImg = tempHtml.substring(lastIndex, match.index);
            if (beforeImg.trim()) {
              content.push(
                <div
                  key={`content-${content.length}`}
                  className={clsx('ym-reader-content text-foreground', {
                    'chat-content': type === 'chat',
                  })}
                  dangerouslySetInnerHTML={{ __html: beforeImg }}
                />,
              );
            }

            // 解析图片标签获取属性
            const imgTag = match[0];
            const tempDiv = document.createElement('div');
            tempDiv.innerHTML = imgTag;
            const img = tempDiv.querySelector('img');

            if (img) {
              const src = img.getAttribute('src') || '';
              const alt = img.getAttribute('alt') || '';
              const widthAttr = img.getAttribute('width');
              const heightAttr = img.getAttribute('height');

              const width = widthAttr
                ? widthAttr.includes('%')
                  ? widthAttr // 保留百分比字符串
                  : parseInt(widthAttr, 10) // 解析像素值
                : undefined;
              const height = heightAttr
                ? heightAttr.includes('%')
                  ? heightAttr // 保留百分比字符串
                  : parseInt(heightAttr, 10) // 解析像素值
                : undefined;

              // 添加ImagePreview组件
              content.push(
                <ImagePreview
                  key={`img-${imgIndex}`}
                  src={src}
                  alt={alt}
                  width={width}
                  height={height}
                  imageList={allImageSources}
                  currentIndex={imgIndex}
                  showLoadingOnError
                  insertToThoughtEnabled={false}
                />,
              );
            }

            lastIndex = match.index + match[0].length;
            imgIndex++;
          }

          // 添加最后一段HTML
          const afterLastImg = tempHtml.substring(lastIndex);
          if (afterLastImg.trim()) {
            content.push(
              <div
                key={`content-${content.length}`}
                className={clsx('ym-reader-content text-foreground', {
                  'chat-content': type === 'chat',
                })}
                dangerouslySetInnerHTML={{ __html: afterLastImg }}
              />,
            );
          }

          return <>{content}</>;
        } catch (error) {
          console.error('Error processing HTML content:', error);
          // 出错时回退到原始渲染
          return (
            <div
              className={clsx('ym-reader-content text-foreground', {
                'chat-content': type === 'chat',
              })}
              dangerouslySetInnerHTML={{
                __html: cleanSource,
              }}
            />
          );
        }
      }, [cleanSource, type]);

      return (
        <div ref={containerRef}>
          <div
            ref={ref}
            className={clsx('ym-reader-content-container', {
              'chat-content-container': type === 'chat',
            })}
          >
            {processedContent}
          </div>
        </div>
      );
    },
  ),
);
MemoizedContent.displayName = 'MemoizedContent';

export const ArticleContent = React.forwardRef<HTMLDivElement, ArticleContentProps>(
  ({ source, type = 'article', onHashLinkClick, ...props }, ref) => {
    const containerRef = useRef<HTMLDivElement>(null);

    useEffect(() => {
      if (containerRef?.current) {
        containerRef.current.addEventListener('click', (e) => {
          const targetLink = (e.target as HTMLElement)?.closest('a');
          if (!targetLink) return;
          if (targetLink.getAttribute('href')?.startsWith('#')) {
            e.preventDefault();
            onHashLinkClick?.(targetLink.getAttribute('href')!);
            return;
          }
          e.preventDefault();
          window.open(targetLink.href, '_blank');
        });
      }
    }, [containerRef]);

    const propsWithoutTitle = useMemo(() => {
      const { title, ...rest } = props;
      return rest;
    }, [props]);

    return (
      <div className="ym-reader-container relative" ref={ref} {...propsWithoutTitle}>
        <MemoizedContent ref={containerRef} source={source} type={type} />
      </div>
    );
  },
);

ArticleContent.displayName = 'ArticleContent';

.ym-reader-content code {
  @apply rounded-sm bg-neutral-200 font-mono text-foreground caret-white dark:bg-[#292C33];
  font-family: source-code-pro, Menlo, Monaco, "Courier New", Courier, monospace !important;
  font-size: 0.8em !important;
  line-height: 1.6em !important;

  &::selection {
    @apply bg-white/30;
  }
}

.ym-reader-content pre {
  @apply my-8 rounded border border-black bg-[#292C33] text-[#ACB2BE] caret-white;
  font-family: source-code-pro, Menlo, Monaco, "Courier New", Courier, monospace !important;
  font-size: 0.8em !important;
  line-height: 1.6em !important;

  code {
    @apply m-0 p-0 text-inherit shadow-none;
    background: transparent !important;
  }

  .hljs-comment,
  .hljs-quote {
    @apply text-neutral-400;
  }

  .hljs-variable,
  .hljs-template-variable,
  .hljs-attribute,
  .hljs-tag,
  .hljs-name,
  .hljs-regexp,
  .hljs-link,
  .hljs-name,
  .hljs-selector-id,
  .hljs-selector-class {
    @apply text-red-300;
  }

  .hljs-number,
  .hljs-meta,
  .hljs-built_in,
  .hljs-builtin-name,
  .hljs-literal,
  .hljs-type,
  .hljs-params {
    @apply text-orange-300;
  }

  .hljs-string,
  .hljs-symbol,
  .hljs-bullet {
    @apply text-lime-300;
  }

  .hljs-title,
  .hljs-section {
    @apply text-yellow-300;
  }

  .hljs-keyword,
  .hljs-selector-tag {
    @apply text-teal-300;
  }

  .hljs-emphasis {
    font-style: italic;
  }

  .hljs-strong {
    font-weight: 700;
  }
}

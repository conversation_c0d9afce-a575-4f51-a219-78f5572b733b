'use client';

import { Input } from '@repo/ui/components/ui/input';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@repo/ui/components/ui/tooltip';
import { cn } from '@repo/ui/lib/utils';
import { useAtom } from 'jotai';
import { EditIcon, Loader2 } from 'lucide-react';
import { memo, useEffect, useState } from 'react';
import { useSnipContext } from '../../context';
import {
  isDetectingSpeakersAtom,
  isEditingSpeakerLabelAtom,
  isFormattingTranscriptAtom,
  updatingSpeakerAtom,
} from './atoms';
import { useTranscriptSpeaker } from './hooks';
import { safeDecodeURIComponent } from './utils';

interface EditableSpeakerLabelProps {
  speaker: string;
}

export const EditableSpeakerLabel = memo(function EditableSpeakerLabel({
  speaker,
}: EditableSpeakerLabelProps) {
  const [isEditingSpeakerLabel, setIsEditingSpeakerLabel] = useAtom(isEditingSpeakerLabelAtom);
  const [localLoading, setLocalLoading] = useState(false);
  const { readonly } = useSnipContext();

  const [isFormattingTranscript] = useAtom(isFormattingTranscriptAtom);
  const [updatingSpeaker] = useAtom(updatingSpeakerAtom);
  const [isDetectingSpeakers] = useAtom(isDetectingSpeakersAtom);

  const { handleSpeakerChanged } = useTranscriptSpeaker();

  useEffect(() => {
    if (updatingSpeaker === false && localLoading === true) {
      setLocalLoading(false);
    }
  }, [updatingSpeaker, localLoading]);

  return (
    <div
      className={cn(
        'group flex items-center rounded-sm text-muted-foreground',
        !isEditingSpeakerLabel &&
          !localLoading &&
          !readonly &&
          'cursor-pointer hover:bg-muted hover:text-foreground',
      )}
    >
      {isEditingSpeakerLabel ? (
        <>
          <Input
            size={12}
            autoFocus
            disabled={isFormattingTranscript}
            className="w-auto h-4 px-1 text-xs footnote"
            defaultValue={safeDecodeURIComponent(speaker)}
            onBlur={(e) => {
              setLocalLoading(true);
              setIsEditingSpeakerLabel(false);
              handleSpeakerChanged(speaker, e.target.value);
            }}
            onKeyDown={(e) => {
              if (e.key === 'Enter' && !e.nativeEvent.isComposing) {
                e.preventDefault();
                e.currentTarget.blur();
              }
            }}
          />
          <div className="w-3 h-3"></div>
        </>
      ) : (
        <div
          className="flex items-center"
          onClick={() => {
            if (localLoading || readonly || isDetectingSpeakers) {
              return;
            }
            setIsEditingSpeakerLabel(true);
          }}
        >
          {isDetectingSpeakers ? (
            <TooltipProvider>
              <Tooltip delayDuration={0}>
                <TooltipTrigger asChild>
                  <span className="loading-shimmer">{safeDecodeURIComponent(speaker)}</span>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Identifying speakers...</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          ) : (
            safeDecodeURIComponent(speaker)
          )}
          {localLoading ? (
            <Loader2 className="w-3 h-3 ml-1 animate-spin" size={8} />
          ) : (
            !readonly &&
            !isDetectingSpeakers && (
              <EditIcon className="w-3 h-3 ml-1 opacity-0 group-hover:opacity-100" size={8} />
            )
          )}
        </div>
      )}
    </div>
  );
});

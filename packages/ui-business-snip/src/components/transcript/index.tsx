'use client';

import { cn } from '@repo/ui/lib/utils';
import { useAtom } from 'jotai';
import { useEffect, useRef } from 'react';
import { SnipVO } from '../../typings/snip';
import {
  displayLineByLineAtom,
  hasTranscriptAtom,
  resetTranscriptAtomsAtom,
  stickyTopAtom,
  transcriptRefAtom,
} from './atoms';
import { EmptyTranscript } from './EmptyTranscript';
import {
  useTranscriptFormatting,
  useTranscriptHighlight,
  useTranscriptPlayer,
  useTranscriptState,
  useTranscriptUrlParams,
} from './hooks';
import { LineByLineTranscript } from './LineByLineTranscript';
import { ParagraphTranscript } from './ParagraphTranscript';
import { TranscriptToolbar } from './TranscriptToolbar';
import { TRANSCRIPT_CONTAINER_CLASS_NAME } from './utils';

export interface TranscriptBlockProps extends React.HTMLAttributes<HTMLDivElement> {
  snip: SnipVO;
  stickyTop?: number;
  play?: () => void;
  readonly?: boolean;
  renderRightActions?: () => React.ReactNode;
}

export default function TranscriptBlock({
  className,
  snip,
  stickyTop = 0,
  play,
  readonly,
  renderRightActions,
}: TranscriptBlockProps) {
  const [, setStickyTop] = useAtom(stickyTopAtom);
  const [, resetTranscriptAtoms] = useAtom(resetTranscriptAtomsAtom);
  const [, setTranscriptRef] = useAtom(transcriptRefAtom);

  const transcriptRef = useRef<HTMLDivElement>(null);
  const { updateStateWhenSnipChanged, handleGenerateTranscript, handleUploadTranscript } =
    useTranscriptState();

  useEffect(() => {
    setStickyTop(stickyTop || 0);
  }, [stickyTop, readonly, setStickyTop]);

  useEffect(() => {
    updateStateWhenSnipChanged(snip);
    return () => {
      resetTranscriptAtoms();
    };
  }, [resetTranscriptAtoms, snip, updateStateWhenSnipChanged]);

  useEffect(() => {
    setTranscriptRef(transcriptRef);
  }, [setTranscriptRef]);

  const { seek } = useTranscriptPlayer({ play });
  const { highlightSegment } = useTranscriptHighlight();

  const [displayLineByLine] = useAtom(displayLineByLineAtom);
  const [hasTranscript] = useAtom(hasTranscriptAtom);

  const { handleUrlParams } = useTranscriptUrlParams({
    seek,
    highlightSegment,
  });

  useEffect(() => {
    handleUrlParams();
  }, [handleUrlParams]);

  const { handleReformatTranscript, generateSubtitleWithPunctuation } = useTranscriptFormatting();

  const transcriptContent = hasTranscript ? (
    <div className={cn('body text-base', className, TRANSCRIPT_CONTAINER_CLASS_NAME)}>
      <TranscriptToolbar
        renderRightActions={renderRightActions}
        handleReformatTranscript={handleReformatTranscript}
      />
      {displayLineByLine ? (
        <LineByLineTranscript seek={seek} />
      ) : (
        <ParagraphTranscript
          seek={seek}
          generateSubtitleWithPunctuation={generateSubtitleWithPunctuation}
        />
      )}
    </div>
  ) : (
    <EmptyTranscript
      playUrl={snip?.play_url}
      handleGenerateTranscript={handleGenerateTranscript}
      handleUploadTranscript={handleUploadTranscript}
    />
  );

  return <div ref={transcriptRef}>{transcriptContent}</div>;
}

'use client';

import { Button } from '@repo/ui/components/ui/button';
import { useAtom } from 'jotai';
import { Upload } from 'lucide-react';
import { memo, useState } from 'react';
import { useSnipContext } from '../../context';
import { StarIcon } from '../icon';
import { fromYouTubeAtom } from './atoms';
import { UploadTranscriptDialog } from './UploadTranscriptDialog';

interface EmptyTranscriptProps {
  playUrl?: string;
  handleGenerateTranscript: () => void;
  handleUploadTranscript?: (transcript: string) => Promise<{ success: boolean; error?: string }>;
}

export const EmptyTranscript = memo(function EmptyTranscript({
  playUrl,
  handleGenerateTranscript,
  handleUploadTranscript,
}: EmptyTranscriptProps) {
  const [generateButtonHover, setGenerateButtonHover] = useState(false);
  const [uploadDialogOpen, setUploadDialogOpen] = useState(false);

  const [fromYouTube] = useAtom(fromYouTubeAtom);
  const { readonly } = useSnipContext();
  const canGenerateTranscript = !fromYouTube && playUrl && !readonly;

  return (
    <div className="flex flex-col items-center justify-center py-8">
      <p className="mb-4 paragraph text-muted-foreground">No transcript is available.</p>
      {canGenerateTranscript ? (
        <Button
          variant="outline"
          className="body-strong group relative h-8 w-[184px] overflow-hidden rounded-full border border-border bg-brand p-[1.5px] text-card hover:bg-brand/90 hover:text-card"
          onClick={handleGenerateTranscript}
          onMouseEnter={() => setGenerateButtonHover(true)}
          onMouseLeave={() => setGenerateButtonHover(false)}
        >
          <div className="absolute inset-0 h-full w-full animate-rotate rounded-full bg-[conic-gradient(#ffffff_20deg,transparent_120deg)] opacity-0 group-hover:opacity-100"></div>
          <div className="relative flex items-center justify-center w-full rounded-full h-7 bg-brand">
            <StarIcon animate={generateButtonHover} size={16} className="mr-1" />
            Generate
          </div>
        </Button>
      ) : (
        <div className="flex items-center justify-center gap-2">
          <p className="text-sm paragraph text-muted-foreground">
            Upload or paste transcript generated by other tools.
          </p>
          <Button
            size="icon"
            variant="ghost"
            className="w-8 h-8 rounded-full hover:bg-muted"
            onClick={() => setUploadDialogOpen(true)}
            title="Upload"
          >
            <Upload size={16} />
          </Button>

          {handleUploadTranscript && (
            <UploadTranscriptDialog
              open={uploadDialogOpen}
              onOpenChange={setUploadDialogOpen}
              onSubmit={handleUploadTranscript}
            />
          )}
        </div>
      )}
    </div>
  );
});

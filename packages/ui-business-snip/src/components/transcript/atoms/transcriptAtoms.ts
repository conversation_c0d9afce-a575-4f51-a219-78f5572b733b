import { BlockContentDto } from '@repo/api/generated-client/snake-case/index';
import { AILanguageEnumKeys } from '@repo/common/types/language/enum';
import { atom } from 'jotai';
import { atomWithStorage } from 'jotai/utils';
import { FormattedCue } from '../utils/convertTokensToCues';

export type TranscriptContent = BlockContentDto;

export const hasTranscriptAtom = atom<boolean>(false);
export const isGeneratingTranscriptAtom = atom<boolean>(false);
export const rawTranscriptContentsAtom = atom<TranscriptContent[]>([]);

export const isFormattingTranscriptAtom = atom<boolean>(false);
export const formattedTranscriptContentsAtom = atom<TranscriptContent[]>([]);

export const languageAtom = atom<string>('');
export const translateLanguageAtom = atom<AILanguageEnumKeys | undefined>(undefined);
export const displayLineByLineAtom = atom<boolean>(true);

export interface CueItem {
  timestamp: string;
  content: string;
  speaker?: string;
}

export interface CuesList {
  cues: CueItem[];
  language: string | undefined;
}

export const cuesListAtom = atom<CuesList[]>([]);
export const unformattedCuesAtom = atom<CueItem[]>([]);
export const formattedCuesAtom = atom<FormattedCue[]>([]);
export const speakersAtom = atom<string[]>([]);
export const isDetectingSpeakersAtom = atom<boolean>(false);
export const hasDetectedSpeakersAtom = atomWithStorage<Record<string, boolean>>(
  'hasDetectedSpeakers',
  {},
);

export const isPlayerReadyAtom = atom<boolean>(false);
export const updatingSpeakerAtom = atom<boolean>(false);
export const isEditingSpeakerLabelAtom = atom<boolean>(false);
export const stickyTopAtom = atom<number>(0);

export const bilibiliPlayerAtom = atom<Record<string, unknown> | null>(null);
export const fromYouTubeAtom = atom<boolean>(false);
export const fromBilibiliAtom = atom<boolean>(false);

export const lineByLineRefAtom = atom<React.RefObject<HTMLOListElement> | null>(null);
export const paragraphRefAtom = atom<React.RefObject<HTMLDivElement> | null>(null);
export const transcriptRefAtom = atom<React.RefObject<HTMLDivElement> | null>(null);

const HAVE_VIEWED_PARAGRAPH_KEY = 'haveViewedParagraph';
export const haveViewedParagraphAtom = atomWithStorage<boolean>(HAVE_VIEWED_PARAGRAPH_KEY, false);

export const resetTranscriptAtomsAtom = atom(null, (_get, set) => {
  set(rawTranscriptContentsAtom, []);
  set(formattedTranscriptContentsAtom, []);
  set(languageAtom, '');
  set(cuesListAtom, []);
  set(unformattedCuesAtom, []);
  set(formattedCuesAtom, []);
  set(isPlayerReadyAtom, false);
  set(hasTranscriptAtom, false);
  set(isGeneratingTranscriptAtom, false);
  set(isFormattingTranscriptAtom, false);
  set(fromYouTubeAtom, false);
  set(fromBilibiliAtom, false);
  set(lineByLineRefAtom, null);
  set(paragraphRefAtom, null);
  set(transcriptRefAtom, null);
  set(speakersAtom, []);
  set(isDetectingSpeakersAtom, false);
  set(isEditingSpeakerLabelAtom, false);
});

export const pollingAtom = atom<Record<string, boolean>>({});

import { useAtom } from 'jotai';
import { useCallback, useEffect } from 'react';
import { YouTubePlayer } from 'react-youtube';
import { snipDetailAtom } from '../../../atoms';
import {
  convertTimestampToSeconds,
  youTubePlayerAtom,
} from '../../../hooks/scoped/useYouTubePlayer';
import { isBilibili, isYouTube } from '../../../utils/snip-util';
import { fromBilibiliAtom, fromYouTubeAtom, isPlayerReadyAtom } from '../atoms';

export function useTranscriptPlayer(props?: { play?: () => void }) {
  const [youTubePlayer] = useAtom(youTubePlayerAtom);
  const [fromYouTube, setFromYouTube] = useAtom(fromYouTubeAtom);
  const [fromBilibili, setFromBilibili] = useAtom(fromBilibiliAtom);
  const [, setIsPlayerReady] = useAtom(isPlayerReadyAtom);
  const [snipDetail] = useAtom(snipDetailAtom);

  useEffect(() => {
    if (snipDetail && isYouTube(snipDetail)) {
      setFromYouTube(true);
    } else {
      setFromYouTube(false);
    }

    if (snipDetail && isBilibili(snipDetail)) {
      setFromBilibili(true);
    } else {
      setFromBilibili(false);
    }
  }, [snipDetail, setFromYouTube, setFromBilibili]);

  const play = props?.play;
  useEffect(() => {
    if (youTubePlayer) {
      const timer = setTimeout(() => {
        setIsPlayerReady(true);
      }, 1000);
      return () => clearTimeout(timer);
    }
  }, [youTubePlayer, setIsPlayerReady]);

  const seek = useCallback(
    (timestamp: string | number) => {
      const timestampStr = timestamp.toString();
      if (fromYouTube) {
        const seconds = convertTimestampToSeconds(timestampStr);
        (youTubePlayer as YouTubePlayer)?.seekTo(seconds, true);
      }

      if (fromBilibili) {
        window.postMessage({ type: 'youmind_seek_bilibili_iframe', timestamp: timestampStr }, '*');
      }

      window.postMessage({ type: 'youmind_seek', timestamp: timestampStr }, '*');

      setTimeout(() => {
        play?.();
      }, 50);
    },
    [fromYouTube, fromBilibili, youTubePlayer, play],
  );

  return {
    seek,
  };
}

import { LanguageEnum, ProcessStatusEnum } from '@repo/api/generated-client/snake-case/index';
import { Content } from '@repo/common';
import { toast } from '@repo/ui/components/ui/sonner';
import usePolling from '@repo/ui/hooks/usePolling';
import { ContentFormatEnum, type SubtitleCue, SubtitleHandler } from '@youmindinc/youcommon';
import { produce } from 'immer';
import { useAtom } from 'jotai';
import { Marked } from 'marked';
import { useCallback, useEffect, useState } from 'react';
import { snipDetailAtom } from '../../../atoms';
import { useSnipContext } from '../../../context';
import {
  cuesListAtom,
  displayLineByLineAtom,
  formattedCuesAtom,
  formattedTranscriptContentsAtom,
  hasDetectedSpeakersAtom,
  isDetectingSpeakersAtom,
  isFormattingTranscriptAtom,
  isGeneratingTranscriptAtom,
  languageAtom,
  pollingAtom,
  rawTranscript<PERSON>ontents<PERSON>tom,
  speakersAtom,
  type TranscriptContent,
  unformattedCuesAtom,
} from '../atoms';
import { formatLanguage } from '../utils';
import { convertTokensToCues } from '../utils/convertTokensToCues';

const marked = new Marked();

export const useTranscriptFormatting = () => {
  const { apiClient, readonly } = useSnipContext();
  const [displayLineByLine, setDisplayLineByLine] = useAtom(displayLineByLineAtom);
  const [rawTranscriptContents] = useAtom(rawTranscriptContentsAtom);
  const [formattedTranscriptContents, setFormattedTranscriptContents] = useAtom(
    formattedTranscriptContentsAtom,
  );
  const [language] = useAtom(languageAtom);
  const [isFormattingTranscript, setIsFormattingTranscript] = useAtom(isFormattingTranscriptAtom);
  const [snipDetail, setSnipDetail] = useAtom(snipDetailAtom);

  const [, setCuesList] = useAtom(cuesListAtom);
  const [, setUnformattedCues] = useAtom(unformattedCuesAtom);
  const [formattedCues, setFormattedCues] = useAtom(formattedCuesAtom);

  useEffect(() => {
    const newCuesList = rawTranscriptContents.map((content) => {
      const handler = SubtitleHandler.fromRaw(content.raw || '');
      const cues = handler.getCues().map((cue: SubtitleCue) => {
        const cueContent = cue.content.join('\n');
        return {
          timestamp: handler.toLlmTimestamp(cue.start),
          content: cueContent,
          speaker: cue.speaker || '',
        };
      });
      return { cues, language: content.language };
    });
    setCuesList(newCuesList);

    const newUnformattedCues = newCuesList?.find(({ language: l }) => l === language)?.cues || [];
    setUnformattedCues(newUnformattedCues);
  }, [rawTranscriptContents, language, setCuesList, setUnformattedCues]);

  const [speakers, setSpeakers] = useAtom(speakersAtom);
  const [isDetectingSpeakers, setIsDetectingSpeakers] = useAtom(isDetectingSpeakersAtom);
  const [hasDetectedSpeakers, setHasDetectedSpeakers] = useAtom(hasDetectedSpeakersAtom);
  const [isGeneratingTranscript] = useAtom(isGeneratingTranscriptAtom);
  useEffect(() => {
    const filteredContents = formattedTranscriptContents.filter(
      ({ language: l }) => l === language || l === formatLanguage(language),
    );

    const markdown =
      filteredContents
        .reverse()
        .map(({ raw }) => raw)
        .join('') || '';

    const newFormattedCues = convertTokensToCues(
      marked.lexer(markdown.replace(/^```/g, '').replace(/```$/g, '')),
      snipDetail?.id || '',
    );

    const newSpeakers: string[] = [];
    newFormattedCues.forEach((cue) => {
      if (cue.speaker && !newSpeakers.includes(cue.speaker)) {
        newSpeakers.push(cue.speaker);
      }
    });
    setSpeakers(newSpeakers);
    setFormattedCues(newFormattedCues);
  }, [formattedTranscriptContents, language, setFormattedCues, setSpeakers, snipDetail?.id]);

  useEffect(() => {
    if (
      speakers.length > 0 &&
      !displayLineByLine &&
      !isDetectingSpeakers &&
      !isGeneratingTranscript
    ) {
      const unknownSpeakerCount = speakers.filter((s) => s.includes('speaker')).length;

      const shouldDetect =
        unknownSpeakerCount >= 2 &&
        unknownSpeakerCount === speakers.length &&
        !hasDetectedSpeakers[snipDetail?.id || ''];

      const fetchSpeakers = async () => {
        const transcript = formattedTranscriptContents
          .filter(
            ({ language: l, status }) => l === language && status === ProcessStatusEnum.completed,
          )
          .map(({ plain }) => plain)
          .at(0)
          ?.split('\n')
          ?.slice(0, 5)
          ?.join('\n');

        if (!transcript) {
          return;
        }

        setIsDetectingSpeakers(true);
        try {
          const { speakers = [] } = await apiClient.snipApi.detectSpeakers({
            snip_id: snipDetail?.id!,
            transcript,
          });

          setHasDetectedSpeakers((prev) => ({
            ...prev,
            [snipDetail?.id || '']: true,
          }));

          if (speakers.length > 0) {
            setFormattedCues(
              produce(formattedCues, (draft) => {
                draft.forEach((cue) => {
                  const speaker = speakers.find((s) => (s.original_speaker || '') === cue.speaker);
                  if (speaker && cue.speaker && speaker.new_speaker) {
                    cue.speaker = speaker.new_speaker;
                  }
                });
              }),
            );
          }

          setIsDetectingSpeakers(false);
        } catch (error) {
          console.error('Failed to detect speakers', error);
          setIsDetectingSpeakers(false);
          return;
        }
      };

      if (shouldDetect) {
        setIsDetectingSpeakers(true);
        fetchSpeakers();
      }
    }
  }, [
    speakers,
    displayLineByLine,
    setIsDetectingSpeakers,
    hasDetectedSpeakers,
    snipDetail?.id,
    setHasDetectedSpeakers,
    setFormattedCues,
    formattedCues,
    isDetectingSpeakers,
    formattedTranscriptContents,
    language,
    isGeneratingTranscript,
  ]);

  const generateSubtitleWithPunctuation = useCallback(
    async (regenerate: boolean = false) => {
      if (!rawTranscriptContents?.length) {
        return;
      }
      setIsFormattingTranscript(true);
      try {
        await apiClient.snipApi.addPunctuation({
          language: formatLanguage(language) as LanguageEnum,
          snip_id: snipDetail?.id!,
          // [FIXME] 等沐轲修复
          block_id: rawTranscriptContents?.[0]?.block_id!,
          regenerate,
        });
      } catch (error) {
        console.error('Failed to add punctuation', error);
        setIsFormattingTranscript(false);
        return;
      }
    },
    [rawTranscriptContents, language, snipDetail?.id, setIsFormattingTranscript],
  );

  const [polling, setPolling] = useAtom(pollingAtom);
  const fetchData = useCallback(async () => {
    if (!snipDetail?.id) {
      return;
    }

    try {
      const data = await apiClient.snipApi.listFormattedSubtitles({
        snip_id: snipDetail?.id!,
        language: language as LanguageEnum,
      });

      if (data.length > 0) {
        setFormattedTranscriptContents(data as TranscriptContent[]);

        if (data.every(({ status }) => status === ProcessStatusEnum.completed)) {
          setSnipDetail(
            produce(snipDetail, (draft) => {
              if (!draft?.transcript) {
                return;
              }
              draft.transcript.contents = [...draft.transcript.contents, ...(data as Content[])];
            }),
          );
          setIsFormattingTranscript(false);
        }
      } else {
        if (formattedTranscriptContents.length > 0) {
          toast('Failed to enhance transcript. Please try again.');
          setFormattedTranscriptContents([]);
          setIsFormattingTranscript(false);
        }
      }
    } catch (error) {
      console.error('Failed to list formatted subtitles', error);
      setPolling({
        ...polling,
        [snipDetail?.id || '']: false,
      });
      setIsFormattingTranscript(false);
      return;
    }
  }, [
    snipDetail,
    language,
    setPolling,
    polling,
    setIsFormattingTranscript,
    setFormattedTranscriptContents,
    setSnipDetail,
    formattedTranscriptContents.length,
  ]);
  const { stopPolling, startPolling } = usePolling(fetchData, 5000);

  useEffect(() => {
    if (polling[snipDetail?.id || '']) {
      setIsFormattingTranscript(true);
    }
  }, [polling, snipDetail?.id, setIsFormattingTranscript]);

  const [isDeletingFormattedSubtitles, setIsDeletingFormattedSubtitles] = useState(false);

  useEffect(() => {
    const currentSnipId = snipDetail?.id || '';
    const shouldBePolling = isFormattingTranscript && !readonly && !isDeletingFormattedSubtitles;
    const isCurrentlyPolling = polling[currentSnipId] || false;

    if (shouldBePolling && !isCurrentlyPolling) {
      setPolling((prev) => ({
        ...prev,
        [currentSnipId]: true,
      }));
      startPolling();
    } else if (!shouldBePolling && isCurrentlyPolling) {
      setPolling((prev) => ({
        ...prev,
        [currentSnipId]: false,
      }));
      stopPolling();
    } else if (shouldBePolling && isCurrentlyPolling) {
      startPolling();
    } else if (!shouldBePolling && !isCurrentlyPolling) {
      stopPolling();
    }

    return () => {
      stopPolling();
    };
  }, [
    isDeletingFormattedSubtitles,
    isFormattingTranscript,
    polling,
    readonly,
    setPolling,
    snipDetail?.id,
    startPolling,
    stopPolling,
  ]);

  const handleReformatTranscript = useCallback(async () => {
    if (rawTranscriptContents?.length === 0 || isFormattingTranscript) {
      return;
    }

    setDisplayLineByLine(false);
    setIsFormattingTranscript(true);
    setFormattedTranscriptContents([]);

    if (formattedTranscriptContents.length > 0) {
      setIsDeletingFormattedSubtitles(true);
      try {
        const data = await apiClient.snipApi.deleteFormattedSubtitles({
          snip_id: snipDetail?.id!,
          language: language as LanguageEnum,
        });
        console.log('Deleted formatted subtitles:', data);
      } catch (error) {
        console.error('Failed to delete formatted subtitles', error);
        setIsFormattingTranscript(false);
        return;
      } finally {
        setIsDeletingFormattedSubtitles(false);
        setSnipDetail(
          produce(snipDetail, (draft) => {
            if (!draft?.transcript) {
              return;
            }
            draft.transcript.contents = draft.transcript.contents.filter(
              (content) =>
                content.language === language &&
                content.format !== ContentFormatEnum.SUBTITLE_FORMATTED,
            );
          }),
        );
      }
    }
    await generateSubtitleWithPunctuation(true);
  }, [
    rawTranscriptContents?.length,
    isFormattingTranscript,
    setDisplayLineByLine,
    setIsFormattingTranscript,
    setFormattedTranscriptContents,
    setSnipDetail,
    snipDetail,
    formattedTranscriptContents.length,
    generateSubtitleWithPunctuation,
    language,
  ]);

  return {
    generateSubtitleWithPunctuation,
    handleReformatTranscript,
  };
};

'use client';

import { sleep } from '@repo/common/utilities/sleep';
import { useAtom } from 'jotai';
import { useCallback, useEffect, useRef } from 'react';
import { snipDetailAtom } from '../../../atoms';
import { useSnipContext } from '../../../context';
import {
  convertTimestampToSeconds,
  youTubePlayerAtom,
} from '../../../hooks/scoped/useYouTubePlayer';
import { SnipTypeEnum } from '../../../typings/snip';
import {
  bilibiliPlayerAtom,
  displayLineByLineAtom,
  fromBilibiliAtom,
  fromYouTubeAtom,
  isPlayerReadyAtom,
} from '../atoms';

interface UseTranscriptUrlParamsProps {
  seek: (timestamp: string) => void;
  highlightSegment: (timestamp: number) => void;
}

export function useTranscriptUrlParams(props: UseTranscriptUrlParamsProps) {
  const [snipDetail] = useAtom(snipDetailAtom);
  const [fromYouTube] = useAtom(fromYouTubeAtom);
  const [fromBilibili] = useAtom(fromBilibiliAtom);
  const [isPlayerReady] = useAtom(isPlayerReadyAtom);
  const [, setDisplayLineByLine] = useAtom(displayLineByLineAtom);
  const [youTubePlayer] = useAtom(youTubePlayerAtom);
  const [bilibiliPlayer] = useAtom(bilibiliPlayerAtom);
  const { services, searchParams } = useSnipContext();

  const seek = props.seek;
  const highlightSegment = props.highlightSegment;

  const timeoutRef = useRef<NodeJS.Timeout>();

  const handleUrlParams = useCallback(async () => {
    if (!searchParams) {
      return;
    }

    const paramsToRemove = [];

    const highlightField = searchParams?.get?.('highlightField');
    const highlight = searchParams?.get?.('highlight');
    const timestamp = searchParams?.get?.('timestamp');
    const highlightSleep = searchParams?.get?.('highlightSleep');

    if (highlightField?.includes('transcript') || highlightField?.includes('show_notes')) {
      setDisplayLineByLine(true);
      paramsToRemove.push('highlightField');
    }

    if (highlightField === 'transcript' && highlight) {
      if (highlightSleep && parseInt(highlightSleep) > 0) {
        await sleep(parseInt(highlightSleep));
      }

      const elements = document.querySelectorAll('.transcript-content');
      const textNodes: Node[] = [];
      elements.forEach((element) => {
        const walker = document.createTreeWalker(element, NodeFilter.SHOW_TEXT, null);
        let node;
        while ((node = walker.nextNode())) {
          textNodes.push(node);
        }
      });

      const text = textNodes.map((node) => node.textContent).join(' ');
      const words = highlight.split(' ');
      let bestMatch = { start: 0, end: 0, score: 0 };

      for (let i = 0; i < text.length; i++) {
        let score = 0;
        let matchedLength = 0;
        for (const word of words) {
          const index = text.indexOf(word, i + matchedLength);
          if (index === -1) break;
          score++;
          matchedLength = index - i + word.length;
        }
        if (score > bestMatch.score) {
          bestMatch = { start: i, end: i + matchedLength, score };
        }
      }

      if (bestMatch.score > 0) {
        const range = document.createRange();
        let currentNode = textNodes[0];
        let currentOffset = 0;

        for (const node of textNodes) {
          if (currentOffset + node.textContent!.length >= bestMatch.start) {
            currentNode = node;
            break;
          }
          currentOffset += node.textContent!.length;
        }

        range.setStart(currentNode!, bestMatch.start - currentOffset);
        range.setEnd(currentNode!, bestMatch.end - currentOffset);

        const span = document.createElement('span');
        span.style.backgroundColor = 'yellow';
        range.surroundContents(span);
      }
    }

    if (timestamp) {
      const time = parseInt(timestamp);
      if (fromYouTube && youTubePlayer) {
        timeoutRef.current = setTimeout(() => {
          seek(time.toString());
        }, 1000);
      } else if (fromBilibili && bilibiliPlayer) {
        timeoutRef.current = setTimeout(() => {
          seek(time.toString());
        }, 1000);
      }
      paramsToRemove.push('timestamp');
    }

    const timestampParam = searchParams.get('highlight');
    if (timestampParam) {
      if (fromYouTube) {
        if (isPlayerReady) {
          seek(timestampParam);
          paramsToRemove.push('highlight');
        }
      }
      if (fromBilibili) {
        timeoutRef.current = setTimeout(() => {
          seek(timestampParam);
          highlightSegment(convertTimestampToSeconds(timestampParam));
        }, 500);
        paramsToRemove.push('highlight');
      }
      if (snipDetail?.type === SnipTypeEnum.voice) {
        timeoutRef.current = setTimeout(() => {
          seek(timestampParam);
          highlightSegment(convertTimestampToSeconds(timestampParam));
        }, 500);
        paramsToRemove.push('highlight');
      }
    }

    if (paramsToRemove.length > 0) {
      services?.deleteUrlParams?.(paramsToRemove);
    }
  }, [
    searchParams,
    isPlayerReady,
    fromYouTube,
    fromBilibili,
    youTubePlayer,
    bilibiliPlayer,
    seek,
    highlightSegment,
    snipDetail?.type,
    setDisplayLineByLine,
    services,
  ]);

  useEffect(() => {
    handleUrlParams();

    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, [handleUrlParams]);

  return {
    handleUrlParams,
  };
}

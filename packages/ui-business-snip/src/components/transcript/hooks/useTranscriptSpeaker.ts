import { use<PERSON>tom } from 'jotai';
import { useCallback } from 'react';
import { snipDetailAtom } from '../../../atoms';
import { useSnipContext } from '../../../context';
import { updatingSpeakerAtom } from '../atoms';
export const useTranscriptSpeaker = () => {
  const [snipDetail] = useAtom(snipDetailAtom);
  const [updatingSpeaker, setUpdatingSpeaker] = useAtom(updatingSpeakerAtom);
  const { events, apiClient } = useSnipContext();

  const handleSpeakerChanged = useCallback(
    async (prevSpeaker: string, nextSpeaker: string) => {
      if (updatingSpeaker || !snipDetail) {
        return;
      }
      setUpdatingSpeaker(true);

      try {
        await apiClient.snipApi.updateTranscriptSpeaker({
          id: snipDetail.id,
          speaker_updates: [{ prev_speaker: prevSpeaker, next_speaker: nextSpeaker }],
        });
        await events?.onNeedRefreshSnip?.();
        setUpdatingSpeaker(false);
      } catch (error) {
        console.error('Failed to update transcript speaker', error);
        setUpdatingSpeaker(false);
        return;
      }
    },
    [updatingSpeaker, snipDetail, setUpdatingSpeaker],
  );

  return {
    handleSpeakerChanged,
  };
};

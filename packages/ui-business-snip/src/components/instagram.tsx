import { SimpleLoading } from '@repo/ui/components/custom/simple-loading';
import { SnipArticleVO } from '../typings/snip';

const WIDTH = 464;
const HEIGHT = WIDTH * 2;

const getId = (snip: SnipArticleVO) => {
  if (!snip.webpage?.url) return null;
  try {
    const url = new URL(snip?.webpage?.url);
    const pathSegments = url.pathname.split('/').filter(Boolean);
    return pathSegments[pathSegments.length - 1];
  } catch (error) {
    return null;
  }
};

export const Instagram = ({ snip }: { snip: SnipArticleVO }) => {
  const id = getId(snip);

  if (!id) return <p>Instagram</p>;

  return (
    <div className="relative mx-auto px-4 pb-10 pt-4" style={{ width: WIDTH, height: HEIGHT }}>
      <iframe
        className="relative z-20 h-full w-full"
        allowFullScreen
        src={`https://www.instagram.com/p/${id}/embed/captioned`} // 使用嵌入链接
      />

      <div className="absolute inset-0 z-0 flex h-[25%] items-center justify-center">
        <SimpleLoading />
      </div>
    </div>
  );
};

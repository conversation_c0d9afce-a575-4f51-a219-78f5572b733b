import { SafeParse } from '@repo/common/utilities/object';
import { Button } from '@repo/ui/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@repo/ui/components/ui/select';
import { ChevronDownIcon, ChevronUpIcon } from 'lucide-react';
import { useState } from 'react';
import { TweetReplies } from 'react-tweet';
import { SnipArticleVO } from '../typings/snip';
import { Tweet } from './tweet/tweet';

interface TwitterProps {
  snip: SnipArticleVO;
}

export const Twitter = ({ snip }: TwitterProps) => {
  const [showComments, setShowComments] = useState(true);

  const [filter, setFilter] = useState<'all' | 'author'>('all');

  const renderContent = () => {
    const raw = SafeParse<{ mainTweet?: any; commentTweets?: any[] }>(snip?.content?.raw);
    let mainTweet = raw.mainTweet;

    const commentTweets = raw.commentTweets || [];

    if (!mainTweet) {
      // @ts-expect-error 忽略类型
      mainTweet = SafeParse(snip.extra)?.tweet;
    }

    if (mainTweet) {
      const filteredCommentTweets = commentTweets.filter((tweet) => {
        if (filter === 'all') {
          return true;
        }
        return tweet.user?.id_str === mainTweet.user?.id_str;
      });
      return (
        <div>
          <Tweet tweet={mainTweet} apiUrl={''} />
          <div className="mt-8 border-l border-border">
            {commentTweets.length > 0 && (
              <div className="flex w-full items-center justify-between">
                <div className="flex items-center gap-2 pl-8 text-sm text-muted-foreground">
                  {`Partial Replies`}
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => setShowComments(!showComments)}
                    aria-label={showComments ? 'Hide replies' : 'Show replies'}
                    className="h-7 w-7 rounded-full"
                  >
                    {showComments ? (
                      <ChevronUpIcon className="h-4 w-4" />
                    ) : (
                      <ChevronDownIcon className="h-4 w-4" />
                    )}
                  </Button>
                </div>
                {showComments && (
                  <Select
                    value={filter}
                    onValueChange={(value: 'all' | 'author') => setFilter(value)}
                  >
                    <SelectTrigger className="h-8 w-[100px] bg-transparent text-xs">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All</SelectItem>
                      <SelectItem value="author">Author</SelectItem>
                    </SelectContent>
                  </Select>
                )}
              </div>
            )}
            {showComments &&
              filteredCommentTweets.map((tweet, index) => (
                <div
                  key={index}
                  className={`py-6 pl-8 ${index !== filteredCommentTweets.length - 1 ? 'border-b border-border' : ''}`}
                >
                  <Tweet tweet={tweet} apiUrl={''} isComment />
                </div>
              ))}
            {showComments && filteredCommentTweets.length > 1 && (
              <div className="mt-8 pl-8">
                <TweetReplies tweet={mainTweet} />
              </div>
            )}
          </div>
        </div>
      );
    }
    return snip.title;
  };

  return <div className="mx-auto max-w-[40em] px-6 pb-10 pt-4">{renderContent()}</div>;
};

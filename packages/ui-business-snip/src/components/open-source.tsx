import { ButtonWithTooltip } from '@repo/ui/components/ui/button';
import { useTrackActions } from '@repo/ui/lib/posthog/useTrackActions';
import { SquareArrowOutUpRight } from 'lucide-react';
import { SnipArticleVO, SnipVO } from '../typings/snip';

export function OpenSource({ snip }: { snip: SnipVO }) {
  const { webpage } = (snip as SnipArticleVO) || {};

  const { trackButtonClick } = useTrackActions();

  return (
    <>
      {webpage?.url && (
        <ButtonWithTooltip
          tooltip={'Open source'}
          variant="ghost"
          size="sm"
          className="ml-2 h-7 w-7"
          onClick={() => {
            trackButtonClick('open_source_click', {
              webpage_url: webpage?.url,
              snip_type: snip.type,
              snip_id: snip.id,
            });
          }}
        >
          <a
            href={webpage?.url}
            target="_blank"
            rel="noreferrer"
            className="text-muted-foreground"
            onClick={() => {
              trackButtonClick('open_source_click', {
                webpage_url: webpage?.url,
                snip_type: snip.type,
                snip_id: snip.id,
              });
            }}
          >
            <SquareArrowOutUpRight size={14} />
          </a>
        </ButtonWithTooltip>
      )}
    </>
  );
}

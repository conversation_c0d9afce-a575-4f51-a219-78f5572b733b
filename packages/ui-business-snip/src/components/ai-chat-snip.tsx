'use client';

import { Avatar, AvatarFallback, AvatarImage } from '@repo/ui/components/ui/avatar';
import { Button } from '@repo/ui/components/ui/button';
import { cn } from '@repo/ui/lib/utils';
import { Atom, ChevronDown } from 'lucide-react';
import { useEffect, useState } from 'react';
import { BlockContentVO, SnipArticleVO, SnipVO } from '../typings/snip';
import { isAIChat, isChatGPT, isDeepSeek, isGemini, isPerplexity } from '../utils/snip-util';
import { ArticleContent } from './article-content';
import { OpenSource } from './open-source';

export const CHATGPT_ICON = 'https://cdn.gooo.ai/assets/chatgpt-detail-icon.png';
export const DEEPSEEK_ICON = 'https://cdn.gooo.ai/assets/deepseek-detail-icon.png';
export const GEMINI_ICON = 'https://cdn.gooo.ai/assets/gemini-detail-icon.png';
export const PERPLEXITY_ICON = 'https://cdn.gooo.ai/assets/perplexity-detail-icon.png';
export const ASK_AI_ICON = 'https://cdn.gooo.ai/assets/ask-ai.png';

type ThreadUnit = {
  role: string;
  content?: BlockContentVO;
  // TODO: remove this
  transcript?: BlockContentVO;
};

export type ThreadTranscript = ThreadUnit[];

export interface AIChatSnipProps {
  snip: SnipArticleVO;
  onHashLinkClick?: (href: string) => void;
}

export default function AIChatSnip({ snip, onHashLinkClick }: AIChatSnipProps) {
  const [messageList, setMessageList] = useState<ThreadTranscript>([]);
  const [collapsedMessages, setCollapsedMessages] = useState<Set<number>>(new Set());

  useEffect(() => {
    try {
      if (!snip) {
        return;
      }
      const messages = snip.content;
      const thread = JSON.parse(messages.raw);
      setMessageList(thread);
    } catch (error) {
      console.error('Failed to parse thread', error);
    }
  }, [snip]);

  const thisIsChatGPT = isChatGPT(snip as SnipVO);
  const thisIsDeepSeek = isDeepSeek(snip as SnipVO);
  const thisIsGemini = isGemini(snip as SnipVO);
  const thisIsPerplexity = isPerplexity(snip as SnipVO);
  const thisIsAIChat = isAIChat(snip as SnipVO);

  let avatar = '';
  if (thisIsChatGPT) {
    avatar = CHATGPT_ICON;
  } else if (thisIsDeepSeek) {
    avatar = DEEPSEEK_ICON;
  } else if (thisIsGemini) {
    avatar = GEMINI_ICON;
  } else if (thisIsPerplexity) {
    avatar = PERPLEXITY_ICON;
  } else if (thisIsAIChat) {
    avatar = ASK_AI_ICON;
  }

  return (
    <div className={cn('mx-auto max-w-[40em] px-[1em] text-lg text-foreground')}>
      <div className="mb-10 ml-4 w-[calc(100%-32px)]">
        <div className="mb-4 flex items-center gap-3">
          <Avatar className="h-8 w-8 rounded-full border">
            <AvatarImage src={avatar} alt="role avatar" className="rounded-full" />
            <AvatarFallback className="rounded-full"></AvatarFallback>
          </Avatar>
          <div className="text-base font-medium text-foreground">
            {snip.webpage?.site?.name}
            <OpenSource snip={snip as SnipVO} />
          </div>
        </div>

        <div className="text-[28px] font-medium leading-[38px]">
          {snip.title || snip.webpage?.title}
        </div>
      </div>

      {messageList.map((message, index) => {
        const isUser = message.role === 'User';
        const html = message.content?.raw || message.transcript?.raw || '';
        const isThinking = html.includes('class="not-prose"');
        const isCollapsed = collapsedMessages.has(index);

        return (
          <div
            key={index}
            className={cn('mb-10 flex flex-row', {
              'mb-6 justify-end': isUser,
            })}
          >
            <div
              className={cn('text-muted-foreground', {
                'ml-4 mt-1 max-w-[calc(100%-56px)] rounded-[12px] bg-card-snips py-2': isUser,
              })}
            >
              {isThinking ? (
                <div
                  className={cn(
                    'not-prose -mb-[6px] ml-4 flex w-full items-center justify-between border-l-[2px] border-l-muted pl-2',
                    isCollapsed ? 'pb-[0px]' : 'pb-[10px]',
                  )}
                >
                  <div className="tems-center flex gap-2">
                    <Atom size={16} />
                    <span className="text-sm font-medium text-muted-foreground">
                      {`Reasoning process`}
                    </span>
                  </div>
                  <Button
                    className="h-6 w-6 rounded-full transition-transform duration-200"
                    variant="ghost"
                    size="icon"
                    onClick={() => {
                      setCollapsedMessages((prev) => {
                        const newSet = new Set(prev);
                        if (newSet.has(index)) {
                          newSet.delete(index);
                        } else {
                          newSet.add(index);
                        }
                        return newSet;
                      });
                    }}
                    style={{
                      transform: isCollapsed ? 'rotate(180deg)' : 'rotate(0deg)',
                    }}
                  >
                    <ChevronDown size={16} />
                  </Button>
                </div>
              ) : null}
              <ArticleContent
                source={html}
                onHashLinkClick={onHashLinkClick}
                type="chat"
                style={{
                  whiteSpace: thisIsGemini || thisIsPerplexity ? 'normal' : 'break-spaces',
                }}
                className={cn('prose-chat', {
                  'perplexity-chat': thisIsPerplexity,
                  '[&_.not-prose]:max-h-0 [&_.not-prose]:overflow-hidden [&_.not-prose]:opacity-0':
                    isCollapsed,
                  '[&_.not-prose]:max-h-[99999px] [&_.not-prose]:opacity-100': !isCollapsed,
                })}
              />
            </div>
          </div>
        );
      })}
    </div>
  );
}

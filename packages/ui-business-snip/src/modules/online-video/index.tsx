'use client';

import { TimeSince } from '@repo/ui/components/custom/time-since';
import { Avatar, AvatarFallback, AvatarImage } from '@repo/ui/components/ui/avatar';
import { Button } from '@repo/ui/components/ui/button';
import { Skeleton } from '@repo/ui/components/ui/skeleton';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@repo/ui/components/ui/tabs';
import { cn } from '@repo/ui/lib/utils';
import { useAtom } from 'jotai';
import { FullscreenIcon, MinimizeIcon } from 'lucide-react';
import { useEffect, useRef, useState } from 'react';
import { useDebounceCallback, useResizeObserver } from 'usehooks-ts';
import { snipDetailAtom } from '../../atoms';
import { BilibiliPlayer } from '../../components/bilibili-player';
import { OpenSource } from '../../components/open-source';
import { TimestampHighlighter } from '../../components/timestamp-highlighter';
import TranscriptBlock from '../../components/transcript';
import { useTranscriptPlayer } from '../../components/transcript/hooks';
import { YouTubePlayer } from '../../components/youtube-player';
import { useSnipContext } from '../../context';
import { useTranslation } from '../../i18n/base';
import { SnipVideoVO, SnipVO } from '../../typings/snip';
import { isBilibili, isSnipFetching, isYouTube } from '../../utils/snip-util';

export function OnlineVideo() {
  const { searchParams, onlineVideo, readonly } = useSnipContext();
  const { showViewButton } = onlineVideo?.options || {};
  const [snip] = useAtom(snipDetailAtom);
  const [theaterMode, setTheaterMode] = useState(false);
  const [isSticky, setIsSticky] = useState(false);

  const { title, authors, play_url, webpage, description, published_at } =
    (snip as SnipVideoVO) || {};

  const { t } = useTranslation('Action');

  const isFetching = snip && isSnipFetching(snip);

  const fromYouTube = snip && isYouTube(snip);
  const fromBilibili = snip && isBilibili(snip);

  // const [isOpen, setIsOpen] = useState(true);
  const playerRef = useRef<HTMLDivElement>(null);
  const [playerHeight, setPlayerHeight] = useState(0);

  const onResize = useDebounceCallback(({ height }) => {
    setPlayerHeight(height);
  }, 200);
  useResizeObserver({
    ref: playerRef,
    onResize,
  });

  const [tab, setTab] = useState<'Transcript' | 'Description'>('Transcript');

  useEffect(() => {
    if (!searchParams) return;
    // 检查 highlightField 参数是否等于 description
    if (searchParams.get('highlightField') === 'description') {
      setTab('Description');
      // deleteParams(["highlightField"]);
    } else if (!snip?.transcript?.contents?.length) {
      setTab('Description');
    }
  }, [searchParams, snip?.transcript?.contents?.length]);

  useEffect(() => {
    if (!playerRef.current) return;
    const root = document.getElementById('mind-material-view');

    // @see https://stackoverflow.com/questions/16302483/event-to-detect-when-positionsticky-is-triggered
    const observer = new IntersectionObserver(
      ([e]) => {
        const isSticky = e?.intersectionRatio ? e.intersectionRatio < 1 : false;
        e?.target?.classList?.toggle('isSticky', isSticky);
        setIsSticky(isSticky);
      },
      { threshold: [1], root },
    );
    observer.observe(playerRef.current);
  }, [playerRef]);

  const { seek } = useTranscriptPlayer();
  const handleTimestampClick = (timestamp: string) => {
    seek(timestamp);
  };

  const renderTheaterModeButton = () => {
    if (!showViewButton) return null;
    return (
      <Button
        className="notranslate h-8 px-2"
        variant="ghost"
        onClick={() => setTheaterMode(!theaterMode)}
      >
        {theaterMode ? (
          <MinimizeIcon className="h-4 w-4" />
        ) : (
          <FullscreenIcon className="h-4 w-4" />
        )}
        <span className="body ml-1">{theaterMode ? t('defaultView') : t('theaterMode')}</span>
      </Button>
    );
  };

  return (
    <div
      className={cn(
        'relative mx-auto max-w-[40em] text-lg text-foreground',
        theaterMode && 'max-w-full',
      )}
    >
      {!theaterMode && (
        <div className="notranslate mx-auto max-w-[40em] pt-5">
          <div className="p-4 text-3xl font-medium leading-10">
            {title}
            <OpenSource snip={snip as SnipVO} />
          </div>
          <div className="mb-8 mt-5 flex flex-row px-4">
            <Avatar className="mr-2">
              <AvatarImage
                src={authors?.[0]?.picture || webpage?.site?.favicon_url}
                alt="author avatar"
              />
              <AvatarFallback></AvatarFallback>
            </Avatar>
            <div className="flex-1">
              {isFetching ? (
                <Skeleton className="h-full w-full rounded-lg" />
              ) : (
                <>
                  <p className="text-sm text-muted-foreground">
                    {authors?.map((author) => author?.name).join(', ')}
                  </p>
                  {published_at && (
                    <p className="text-sm text-muted-foreground">
                      <TimeSince dateString={published_at} />
                    </p>
                  )}
                </>
              )}
            </div>
          </div>
        </div>
      )}

      <div
        className={cn(
          'player-container notranslate sticky -top-[1px] z-10 rounded-xl bg-card px-4 pt-[1px]',
          theaterMode && 'rounded-t-xl bg-black',
        )}
        ref={playerRef}
      >
        <div className={cn(theaterMode && 'mx-auto max-w-[min(1440px,80vw)]')}>
          {fromBilibili && <BilibiliPlayer />}
          {fromYouTube && <YouTubePlayer url={play_url || webpage?.url} />}
        </div>
      </div>

      <div className={cn('mx-auto max-w-[40em]', theaterMode && 'max-w-[880px]')}>
        <Tabs
          value={tab}
          className="relative mb-2 w-full px-4 pt-4"
          onValueChange={(value) => setTab(value as 'Transcript' | 'Description')}
        >
          {!isSticky && <div className="absolute right-4 top-4">{renderTheaterModeButton()}</div>}
          <TabsList variant="underline">
            {(['Transcript', 'Description'] as const).map((name) => {
              return (
                <TabsTrigger key={name} value={name} variant="underline" className="notranslate">
                  {t(`${name}.title`)}
                </TabsTrigger>
              );
            })}
          </TabsList>
          <TabsContent value="Transcript" className="mt-0">
            <TranscriptBlock
              snip={snip!}
              stickyTop={playerHeight}
              readonly={readonly}
              renderRightActions={() => isSticky && renderTheaterModeButton()}
            />
          </TabsContent>
          <TabsContent value="Description" className="mt-0">
            <TimestampHighlighter
              content={description?.plain || ''}
              onTimestampClick={handleTimestampClick}
              className="whitespace-pre-wrap py-4 text-base"
            />
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}

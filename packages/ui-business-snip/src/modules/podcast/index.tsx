'use client';

import { TimeSince } from '@repo/ui/components/custom/time-since';
import { Avatar, AvatarFallback, AvatarImage } from '@repo/ui/components/ui/avatar';
import { Button } from '@repo/ui/components/ui/button';
import { Skeleton } from '@repo/ui/components/ui/skeleton';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@repo/ui/components/ui/tabs';
import { cn } from '@repo/ui/lib/utils';
import { useAtom } from 'jotai';
import { CircleAlert, PauseIcon, PlayIcon } from 'lucide-react';
import { useCallback, useEffect, useRef, useState } from 'react';
import { snipDetailAtom } from '../../atoms';
import { AudioPlayer } from '../../components/audio-player';
import { OpenSource } from '../../components/open-source';
import { TimestampHighlighter } from '../../components/timestamp-highlighter';
import TranscriptBlock from '../../components/transcript';
import { useSnipContext } from '../../context';
import { convertTimestampToSeconds } from '../../hooks/scoped/useYouTubePlayer';
import { useTranslation } from '../../i18n/base';
import { SnipFromEnum, SnipVoiceVO } from '../../typings/snip';
import { formatSeconds, isSnipFetching } from '../../utils/snip-util';
import Other from '../other';

export interface PodcastProps extends React.HTMLAttributes<HTMLDivElement> {
  readonly?: boolean;
}

export const TIMESTAMP_REGEXP = /<a class="timestamp"/g; // Explain / Translate

export default function Podcast({ className, readonly }: PodcastProps) {
  const [snip] = useAtom(snipDetailAtom);
  const { searchParams, services } = useSnipContext();
  const { play_url, title, published_at, updated_at, authors, hero_image_url, show_notes, from } =
    (snip as SnipVoiceVO) || {};

  const { t } = useTranslation('Action');

  const [activeTab, setActiveTab] = useState<'Transcript' | 'Shownotes'>('Transcript');

  const isFetching = isSnipFetching(snip!);
  const isWebpage = from === SnipFromEnum.webpage;

  const [seekTo, setSeekTo] = useState(0);
  const [sound, setSound] = useState<Howl | null>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [lastTimeUpdate, setLastTimeUpdate] = useState(0);
  const containerRef = useRef<HTMLDivElement>(null);

  const handlePlayerTimeUpdate = (time: number) => {
    const currentSecond = Math.round(time);
    if (currentSecond !== lastTimeUpdate) {
      setLastTimeUpdate(currentSecond);
      window.postMessage({ type: 'youmind_audio_player_seek', time: currentSecond }, '*');
    }
  };

  const handleSubtitleLocate = (time: number) => {
    const currentSecond = Math.floor(time);
    setLastTimeUpdate(currentSecond);
    window.postMessage({ type: 'youmind_audio_player_seek', time: currentSecond }, '*');
  };

  const handlePlayerReady = (sound: Howl) => {
    setSound(sound);
  };

  const handlePlayerLoadFailed = () => {
    setSound(null);
    setSeekTo(0);
    setIsPlaying(false);
  };

  const handleTogglePlay = useCallback(() => {
    if (isPlaying) {
      sound?.pause();
    } else {
      sound?.play();
    }

    setIsPlaying(!isPlaying);
  }, [isPlaying, sound]);

  // Seek in transcripts.
  useEffect(() => {
    const handler = (event: MessageEvent) => {
      if (event.data.type === 'youmind_seek') {
        const timestamp = event.data.timestamp;
        const seconds = convertTimestampToSeconds(timestamp);
        setSeekTo(seconds);
      }
    };
    // 监听 youmind_seek
    window.addEventListener('message', handler);
    return () => {
      window.removeEventListener('message', handler);
    };
  }, []);

  useEffect(() => {
    if (!searchParams || !searchParams.get('size')) return;
    const highlightField = searchParams.get('highlightField');
    if (highlightField === 'show_notes' || highlightField === 'description') {
      setActiveTab('Shownotes');

      services?.deleteUrlParams?.(['highlightField']);
    } else if (!snip?.transcript?.contents?.length && from === SnipFromEnum.webpage) {
      setActiveTab('Shownotes');
    }
  }, [searchParams, snip?.transcript?.contents?.length]);

  const playIfNotPlaying = useCallback(() => {
    if (!isPlaying) {
      handleTogglePlay();
    }
  }, [isPlaying, handleTogglePlay]);

  const handleTimestampClick = (timestamp: string) => {
    playIfNotPlaying();
    setTimeout(
      () => {
        const seconds = convertTimestampToSeconds(timestamp);
        setSeekTo(seconds);
      },
      isPlaying ? 50 : 300,
    );
  };

  return isWebpage && !play_url ? (
    <>
      <Other />
      <div className="footnote mt-2 flex flex-1 items-center overflow-hidden text-ellipsis whitespace-nowrap text-muted-foreground">
        <CircleAlert size={14} className="mr-2" />
        Unable to fetch content from the URL.
      </div>
    </>
  ) : (
    <div ref={containerRef} className="relative h-full text-foreground">
      <div className={cn('mx-auto max-w-[40em] pb-4 text-lg', className)}>
        <div className="notranslate flex flex-col items-center gap-7 px-4 pb-2 pt-6 md:flex-row md:items-start md:pb-5">
          <Avatar className="h-[200px] w-[200px] rounded-xl shadow-lg">
            <AvatarImage
              src={hero_image_url || 'https://cdn.gooo.ai/assets/music.png'}
              alt="track album"
              className="rounded-xl object-cover"
            />
            <AvatarFallback className="rounded-xl"></AvatarFallback>
          </Avatar>
          <div className="flex flex-col items-center justify-end md:items-start">
            <p className="footnote hidden text-muted-foreground md:block">
              {/* TODO 看看这里 */}
              {isWebpage ? (
                <TimeSince dateString={published_at || ''} />
              ) : (
                <TimeSince dateString={updated_at || ''} />
              )}
              <span className="px-1">&#183;</span>
              {sound && formatSeconds(sound.duration() ?? 0)}
            </p>
            <p className="headline3 line-clamp-3 w-full break-all pb-[10px] pt-1 text-center md:text-start">
              {title}
              <OpenSource snip={snip!} />
            </p>
            {isFetching ? (
              <Skeleton className="h-full w-full rounded-lg" />
            ) : (
              <p className="body-strong hidden md:block">
                {authors?.map((author) => author?.name).join(', ')}
              </p>
            )}
            <p className="footnote block text-muted-foreground md:hidden">
              {authors?.map((author) => author?.name).join(', ')}
              <span className="px-1"> &#183; </span>
              {/* TODO 看看这里 */}
              {isWebpage ? (
                <TimeSince dateString={published_at || ''} />
              ) : (
                <TimeSince dateString={updated_at || ''} />
              )}
              <span className="px-1">&#183;</span>
              {sound && formatSeconds(sound.duration() ?? 0)}
            </p>
            {play_url && (
              <Button
                size="icon"
                className="h-8 w-8 mt-4"
                disabled={!sound}
                onClick={handleTogglePlay}
              >
                {isPlaying ? (
                  <PauseIcon size={20} fill="hsl(var(--background))" />
                ) : (
                  <PlayIcon size={20} fill="hsl(var(--background))" />
                )}
              </Button>
            )}
          </div>
        </div>

        <div className="h-5 w-full"></div>
        <div className="sticky -top-5 z-10 -mt-5 bg-card px-4 pt-5"></div>

        <Tabs
          value={activeTab}
          onValueChange={(value) => setActiveTab(value as 'Transcript' | 'Shownotes')}
          defaultValue="Transcript"
          className="min-h-[calc(100vh-448px)] w-full px-4"
        >
          <TabsList variant="underline">
            {(from === SnipFromEnum.webpage
              ? (['Transcript', 'Shownotes'] as const)
              : (['Transcript'] as const)
            ).map((name) => {
              return (
                <TabsTrigger variant="underline" key={name} value={name} className="notranslate">
                  {t(`${name}.title`)}
                </TabsTrigger>
              );
            })}
          </TabsList>
          <TabsContent
            forceMount
            hidden={activeTab !== 'Transcript'}
            value="Transcript"
            className="mt-0"
          >
            <TranscriptBlock
              snip={snip!}
              play={playIfNotPlaying}
              stickyTop={0}
              readonly={readonly}
            />
          </TabsContent>
          <TabsContent value="Shownotes" className="mt-0">
            <TimestampHighlighter
              content={show_notes?.raw ?? ''}
              className="body text-base"
              onTimestampClick={handleTimestampClick}
            />
          </TabsContent>
        </Tabs>
      </div>

      {play_url && (
        <div className={cn('sticky bottom-0 left-0 right-0 z-10 bg-white/80 backdrop-blur-md')}>
          <div className="notranslate mx-auto max-w-[800px]">
            <AudioPlayer
              seekTo={seekTo}
              track={{
                src: play_url,
                title,
                artist: authors?.map((author) => author?.name).join(', '),
                album: hero_image_url,
              }}
              onPlayerReady={handlePlayerReady}
              onPlayerPlaying={() => setIsPlaying(true)}
              onPlayerPaused={() => setIsPlaying(false)}
              onPlayerTimeUpdate={handlePlayerTimeUpdate}
              onSubtitleLocate={handleSubtitleLocate}
              onPlayerLoadFailed={handlePlayerLoadFailed}
              className="h-full w-full"
            />
          </div>
          <div
            style={{
              paddingBottom: 'env(safe-area-inset-bottom)',
            }}
          />
        </div>
      )}
    </div>
  );
}

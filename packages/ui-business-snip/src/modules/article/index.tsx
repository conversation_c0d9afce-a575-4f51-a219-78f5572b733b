import { SafeParse } from '@repo/common/utilities/object';
import { TimeSince } from '@repo/ui/components/custom/time-since';
import { Avatar, AvatarFallback, AvatarImage } from '@repo/ui/components/ui/avatar';
import { Skeleton } from '@repo/ui/components/ui/skeleton';
import usePolling from '@repo/ui/hooks/usePolling';
import { cn } from '@repo/ui/lib/utils';
import { useAtom } from 'jotai';
import { useCallback, useEffect } from 'react';
import { snipDetailAtom } from '../../atoms';
import AIChatSnip from '../../components/ai-chat-snip';
import { ArticleContent } from '../../components/article-content';
import { Instagram } from '../../components/instagram';
import { Jike } from '../../components/jike';
import { OpenSource } from '../../components/open-source';
import { Reddit } from '../../components/reddit';
import { Twitter } from '../../components/twitter';
import ZhihuQuestion from '../../components/zhihu-question';
import { useSnipContext } from '../../context';
import { SnipArticleVO, SnipVideoVO } from '../../typings/snip';
import {
  isAIChat,
  isChatGPT,
  isDeepSeek,
  isGemini,
  isInstagram,
  isJike,
  isPerplexity,
  isReddit,
  isSnipImageTransfering,
  isTwitter,
  isXiaohongshu,
  isZhihuQuestion,
} from '../../utils/snip-util';
export interface ArticleProps extends React.HTMLAttributes<HTMLDivElement> {
  readonly?: boolean;
  showOpenSource?: boolean;
}

export default function Article({ className, readonly, showOpenSource = true }: ArticleProps) {
  const [snip] = useAtom(snipDetailAtom);
  const { loading, events, article } = useSnipContext();
  const { onHashLinkClick } = article?.events || {};
  const { title, authors, content, webpage, published_at } = (snip as SnipArticleVO) || {};

  const isImageTransfering = isSnipImageTransfering(snip!);

  const fetchData = useCallback(async () => {
    events?.onNeedRefreshSnip?.();
  }, [snip]);

  const { stopPolling, startPolling } = usePolling(fetchData, 3000);

  useEffect(() => {
    if (isImageTransfering && !readonly) {
      startPolling();
    } else {
      stopPolling();
    }

    return () => {
      stopPolling();
    };
  }, [fetchData, isImageTransfering, readonly, snip, startPolling, stopPolling]);

  useEffect(() => {
    if (window.location.hash) {
      try {
        const element = document.querySelector(window.location.hash);
        if (element) element.scrollIntoView();
      } catch (error) {
        console.error(error);
      }
    }
  }, []);

  const renderExtraContent = () => {
    if (isXiaohongshu(snip!)) {
      // 如果抓到了视频
      if ((snip as SnipVideoVO)?.play_url) {
        return (
          <div className="relative mt-4 w-full">
            <video
              className="mx-auto w-[400px] rounded-lg"
              src={(snip as SnipVideoVO)?.play_url}
              controls
              playsInline
            />
          </div>
        );
      }
    }
  };

  const renderContent = () => {
    if (!content?.raw && loading) {
      return (
        <div className="mx-4">
          <Skeleton className="h-[160px] w-full" />
          <Skeleton className="mt-3 h-[20px] w-full" />
          <Skeleton className="mt-3 h-[20px] w-full" />
          <Skeleton className="mt-3 h-[20px] w-[45%]" />
        </div>
      );
    }
    return <ArticleContent title={title} source={content?.raw} onHashLinkClick={onHashLinkClick} />;
  };

  // 如果是新版推特，则显示推特
  if (isTwitter(snip!) && snip?.extra) {
    const isArticle = SafeParse<{ is_article: boolean }>(snip?.extra)?.is_article;
    if (!isArticle) {
      return <Twitter snip={snip as SnipArticleVO} />;
    }
  }

  // 如果是 instagram
  if (isInstagram(snip!)) {
    return <Instagram snip={snip as SnipArticleVO} />;
  }

  // 如果是 reddit
  if (isReddit(snip!)) {
    return <Reddit snip={snip as SnipArticleVO} />;
  }

  // 如果是 jike
  if (isJike(snip!)) {
    return <Jike snip={snip as SnipArticleVO} />;
  }

  // 如果是 chatgpt
  if (isChatGPT(snip!)) {
    return <AIChatSnip snip={snip as SnipArticleVO} onHashLinkClick={onHashLinkClick} />;
  }

  // 如果是 deepseek
  if (isDeepSeek(snip!)) {
    return <AIChatSnip snip={snip as SnipArticleVO} onHashLinkClick={onHashLinkClick} />;
  }

  // 如果是 gemini
  if (isGemini(snip!)) {
    return <AIChatSnip snip={snip as SnipArticleVO} onHashLinkClick={onHashLinkClick} />;
  }

  // 如果是 perplexity
  if (isPerplexity(snip!)) {
    return <AIChatSnip snip={snip as SnipArticleVO} onHashLinkClick={onHashLinkClick} />;
  }

  // 如果是 AI Chat
  if (isAIChat(snip!)) {
    return <AIChatSnip snip={snip as SnipArticleVO} onHashLinkClick={onHashLinkClick} />;
  }

  // 如果是 知乎
  if (isZhihuQuestion(snip!)) {
    return <ZhihuQuestion snip={snip as SnipArticleVO} />;
  }

  return (
    <>
      <div className={cn('mx-auto max-w-[40em] text-lg', className)}>
        <div className="group break-words p-4 text-3xl font-medium leading-10 text-foreground">
          {title}
          {showOpenSource && <OpenSource snip={snip!} />}
        </div>
        <div className="notranslate mb-8 mt-5 flex px-4">
          <div className="flex flex-1 flex-row items-center">
            <Avatar className="mr-2">
              <AvatarImage
                src={authors?.[0]?.picture || webpage?.site?.favicon_url}
                alt="author avatar"
              />
              <AvatarFallback></AvatarFallback>
            </Avatar>
            <div className={cn('flex flex-1 items-center', published_at && 'flex-col items-start')}>
              <p className="text-sm text-muted-foreground">
                {authors?.map((author) => author?.name).join(', ') || webpage?.site?.name}
              </p>
              {published_at && (
                <p className="text-sm text-muted-foreground">
                  <TimeSince dateString={published_at} />
                </p>
              )}
            </div>
          </div>
        </div>
      </div>

      <div className="pb-4 text-sm leading-5 text-secondary" id="article-snip-content">
        {renderContent()}
        {renderExtraContent()}
      </div>
    </>
  );
}

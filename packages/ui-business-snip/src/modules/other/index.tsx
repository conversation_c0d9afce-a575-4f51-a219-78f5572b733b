import { Earth } from '@repo/ui/components/icons/earth';
import { Avatar, AvatarFallback, AvatarImage } from '@repo/ui/components/ui/avatar';
import { cn } from '@repo/ui/lib/utils';
import { useAtom } from 'jotai';
import type React from 'react';
import { snipDetailAtom } from '../../atoms';
import { OpenSource } from '../../components/open-source';
import { SnipOtherWebpageVO } from '../../typings/snip';

export interface OtherProps extends React.HTMLAttributes<HTMLDivElement> {}

export default function Other({ className }: OtherProps) {
  const [snip] = useAtom(snipDetailAtom);
  const { webpage } = (snip as SnipOtherWebpageVO) || {};
  const { title, site, description, url } = webpage || {};

  const header = title || site?.name;

  return (
    <a target="_blank" href={url} className="mt-8">
      <div
        className={cn(
          'mx-auto max-w-[40em] cursor-pointer rounded-2xl border border-[hsla(var(--divider))] p-4 text-foreground',
          className,
        )}
      >
        {header && (
          <div className={cn('title', description ? 'mb-1' : 'mb-3')}>
            {header}
            <OpenSource snip={snip!} />
          </div>
        )}
        {description && <div className="body mb-3 text-caption">{description}</div>}
        <div className="flex flex-row items-center">
          {site?.favicon_url ? (
            <Avatar className="mr-1 h-4 w-4">
              <AvatarImage src={site?.favicon_url} alt="favicon" />
              <AvatarFallback></AvatarFallback>
            </Avatar>
          ) : (
            <Earth size={14} className="mr-1" />
          )}
          <div className="footnote flex-1 overflow-hidden text-ellipsis whitespace-nowrap text-muted-foreground">
            {url}
          </div>
        </div>
      </div>
    </a>
  );
}

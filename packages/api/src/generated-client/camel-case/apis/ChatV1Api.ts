/* tslint:disable */
/* eslint-disable */
/**
 * YouAPI
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 1.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import * as runtime from '../../../runtime';
import type {
  BoardGroupControllerDeleteBoardGroup200Response,
  ChatDetailListV1Dto,
  ChatDetailV1Dto,
  ChatIdDto,
  ChatV1ControllerCreateChat200Response,
  ChatV1ControllerListChatModels200ResponseValue,
  CreateChatV1Dto,
  QueryChatDetailsByOriginDto,
  RegenerateMessageV1Dto,
  SaveMessagesDto,
  SendMessageV1Dto,
  UpdateCompletionBlockDto,
} from '../models/index';
import {
  BoardGroupControllerDeleteBoardGroup200ResponseFromJSON,
  BoardGroupControllerDeleteBoardGroup200ResponseToJSON,
  ChatDetailListV1DtoFromJSON,
  ChatDetailListV1DtoToJSON,
  ChatDetailV1DtoFromJSON,
  ChatDetailV1DtoToJSON,
  ChatIdDtoFromJSON,
  ChatIdDtoToJSON,
  ChatV1ControllerCreateChat200ResponseFromJSON,
  ChatV1ControllerCreateChat200ResponseToJSON,
  ChatV1ControllerListChatModels200ResponseValueFromJSON,
  ChatV1ControllerListChatModels200ResponseValueToJSON,
  CreateChatV1DtoFromJSON,
  CreateChatV1DtoToJSON,
  QueryChatDetailsByOriginDtoFromJSON,
  QueryChatDetailsByOriginDtoToJSON,
  RegenerateMessageV1DtoFromJSON,
  RegenerateMessageV1DtoToJSON,
  SaveMessagesDtoFromJSON,
  SaveMessagesDtoToJSON,
  SendMessageV1DtoFromJSON,
  SendMessageV1DtoToJSON,
  UpdateCompletionBlockDtoFromJSON,
  UpdateCompletionBlockDtoToJSON,
} from '../models/index';

export interface ChatV1ControllerCreateChatRequest {
  createChatV1Dto: CreateChatV1Dto;
}

export interface ChatV1ControllerDeleteChatRequest {
  chatIdDto: ChatIdDto;
}

export interface ChatV1ControllerGetChatDetailRequest {
  chatIdDto: ChatIdDto;
}

export interface ChatV1ControllerQueryChatDetailsByOriginRequest {
  queryChatDetailsByOriginDto: QueryChatDetailsByOriginDto;
}

export interface ChatV1ControllerRegenerateMessageRequest {
  regenerateMessageV1Dto: RegenerateMessageV1Dto;
}

export interface ChatV1ControllerSaveMessagesRequest {
  saveMessagesDto: SaveMessagesDto;
}

export interface ChatV1ControllerSendMessageRequest {
  sendMessageV1Dto: SendMessageV1Dto;
}

export interface ChatV1ControllerUpdateCompletionBlockRequest {
  updateCompletionBlockDto: UpdateCompletionBlockDto;
}

/**
 * ChatV1Api - interface
 *
 * @export
 * @interface ChatV1ApiInterface
 */
export interface ChatV1ApiInterface {
  /**
   * Creates a new chat with the first message and returns a server-sent event stream with V1-compatible stream messages
   * @summary Create a new chat
   * @param {CreateChatV1Dto} createChatV1Dto
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof ChatV1ApiInterface
   */
  createChatRaw(
    requestParameters: ChatV1ControllerCreateChatRequest,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<runtime.ApiResponse<ChatV1ControllerCreateChat200Response>>;

  /**
   * Creates a new chat with the first message and returns a server-sent event stream with V1-compatible stream messages
   * Create a new chat
   */
  createChat(
    createChatV1Dto: CreateChatV1Dto,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<ChatV1ControllerCreateChat200Response>;

  /**
   *
   * @summary Delete a chat
   * @param {ChatIdDto} chatIdDto
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof ChatV1ApiInterface
   */
  deleteChatRaw(
    requestParameters: ChatV1ControllerDeleteChatRequest,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<runtime.ApiResponse<BoardGroupControllerDeleteBoardGroup200Response>>;

  /**
   * Delete a chat
   */
  deleteChat(
    chatIdDto: ChatIdDto,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<BoardGroupControllerDeleteBoardGroup200Response>;

  /**
   *
   * @summary Get chat details
   * @param {ChatIdDto} chatIdDto
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof ChatV1ApiInterface
   */
  getChatDetailRaw(
    requestParameters: ChatV1ControllerGetChatDetailRequest,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<runtime.ApiResponse<ChatDetailV1Dto>>;

  /**
   * Get chat details
   */
  getChatDetail(
    chatIdDto: ChatIdDto,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<ChatDetailV1Dto>;

  /**
   *
   * @summary List available chat models
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof ChatV1ApiInterface
   */
  listChatModelsRaw(
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<
    runtime.ApiResponse<{ [key: string]: ChatV1ControllerListChatModels200ResponseValue }>
  >;

  /**
   * List available chat models
   */
  listChatModels(
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<{ [key: string]: ChatV1ControllerListChatModels200ResponseValue }>;

  /**
   *
   * @summary Query chat details by origin
   * @param {QueryChatDetailsByOriginDto} queryChatDetailsByOriginDto
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof ChatV1ApiInterface
   */
  queryChatDetailsByOriginRaw(
    requestParameters: ChatV1ControllerQueryChatDetailsByOriginRequest,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<runtime.ApiResponse<ChatDetailListV1Dto>>;

  /**
   * Query chat details by origin
   */
  queryChatDetailsByOrigin(
    queryChatDetailsByOriginDto: QueryChatDetailsByOriginDto,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<ChatDetailListV1Dto>;

  /**
   * Regenerates an AI assistant message and returns a server-sent event stream with V1-compatible stream messages
   * @summary Regenerate a chat message
   * @param {RegenerateMessageV1Dto} regenerateMessageV1Dto
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof ChatV1ApiInterface
   */
  regenerateMessageRaw(
    requestParameters: ChatV1ControllerRegenerateMessageRequest,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<runtime.ApiResponse<ChatV1ControllerCreateChat200Response>>;

  /**
   * Regenerates an AI assistant message and returns a server-sent event stream with V1-compatible stream messages
   * Regenerate a chat message
   */
  regenerateMessage(
    regenerateMessageV1Dto: RegenerateMessageV1Dto,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<ChatV1ControllerCreateChat200Response>;

  /**
   *
   * @summary Save user and assistant messages to a chat
   * @param {SaveMessagesDto} saveMessagesDto
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof ChatV1ApiInterface
   */
  saveMessagesRaw(
    requestParameters: ChatV1ControllerSaveMessagesRequest,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<runtime.ApiResponse<ChatDetailV1Dto>>;

  /**
   * Save user and assistant messages to a chat
   */
  saveMessages(
    saveMessagesDto: SaveMessagesDto,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<ChatDetailV1Dto>;

  /**
   * Sends a message to an existing chat and returns a server-sent event stream with V1-compatible stream messages
   * @summary Send a message to an existing chat
   * @param {SendMessageV1Dto} sendMessageV1Dto
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof ChatV1ApiInterface
   */
  sendMessageRaw(
    requestParameters: ChatV1ControllerSendMessageRequest,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<runtime.ApiResponse<ChatV1ControllerCreateChat200Response>>;

  /**
   * Sends a message to an existing chat and returns a server-sent event stream with V1-compatible stream messages
   * Send a message to an existing chat
   */
  sendMessage(
    sendMessageV1Dto: SendMessageV1Dto,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<ChatV1ControllerCreateChat200Response>;

  /**
   *
   * @summary Update tool result
   * @param {UpdateCompletionBlockDto} updateCompletionBlockDto
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof ChatV1ApiInterface
   */
  updateCompletionBlockRaw(
    requestParameters: ChatV1ControllerUpdateCompletionBlockRequest,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<runtime.ApiResponse<void>>;

  /**
   * Update tool result
   */
  updateCompletionBlock(
    updateCompletionBlockDto: UpdateCompletionBlockDto,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<void>;
}

/**
 *
 */
export class ChatV1Api extends runtime.BaseAPI implements ChatV1ApiInterface {
  /**
   * Creates a new chat with the first message and returns a server-sent event stream with V1-compatible stream messages
   * Create a new chat
   */
  async createChatRaw(
    requestParameters: ChatV1ControllerCreateChatRequest,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<runtime.ApiResponse<ChatV1ControllerCreateChat200Response>> {
    if (requestParameters.createChatV1Dto == null) {
      throw new runtime.RequiredError(
        'createChatV1Dto',
        'Required parameter "createChatV1Dto" was null or undefined when calling createChat().',
      );
    }

    const queryParameters: any = {};

    const headerParameters: runtime.HTTPHeaders = {};

    headerParameters['Content-Type'] = 'application/json';

    const urlPath = `/api/v1/chat/createChat`;

    const response = await this.request(
      {
        path: urlPath,
        method: 'POST',
        headers: headerParameters,
        query: queryParameters,
        body: CreateChatV1DtoToJSON(requestParameters.createChatV1Dto),
      },
      initOverrides,
    );

    return new runtime.JSONApiResponse(response, (jsonValue) =>
      ChatV1ControllerCreateChat200ResponseFromJSON(jsonValue),
    );
  }

  /**
   * Creates a new chat with the first message and returns a server-sent event stream with V1-compatible stream messages
   * Create a new chat
   */
  async createChat(
    createChatV1Dto: CreateChatV1Dto,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<ChatV1ControllerCreateChat200Response> {
    const response = await this.createChatRaw({ createChatV1Dto: createChatV1Dto }, initOverrides);
    return await response.value();
  }

  /**
   * Delete a chat
   */
  async deleteChatRaw(
    requestParameters: ChatV1ControllerDeleteChatRequest,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<runtime.ApiResponse<BoardGroupControllerDeleteBoardGroup200Response>> {
    if (requestParameters.chatIdDto == null) {
      throw new runtime.RequiredError(
        'chatIdDto',
        'Required parameter "chatIdDto" was null or undefined when calling deleteChat().',
      );
    }

    const queryParameters: any = {};

    const headerParameters: runtime.HTTPHeaders = {};

    headerParameters['Content-Type'] = 'application/json';

    const urlPath = `/api/v1/chat/deleteChat`;

    const response = await this.request(
      {
        path: urlPath,
        method: 'POST',
        headers: headerParameters,
        query: queryParameters,
        body: ChatIdDtoToJSON(requestParameters.chatIdDto),
      },
      initOverrides,
    );

    return new runtime.JSONApiResponse(response, (jsonValue) =>
      BoardGroupControllerDeleteBoardGroup200ResponseFromJSON(jsonValue),
    );
  }

  /**
   * Delete a chat
   */
  async deleteChat(
    chatIdDto: ChatIdDto,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<BoardGroupControllerDeleteBoardGroup200Response> {
    const response = await this.deleteChatRaw({ chatIdDto: chatIdDto }, initOverrides);
    return await response.value();
  }

  /**
   * Get chat details
   */
  async getChatDetailRaw(
    requestParameters: ChatV1ControllerGetChatDetailRequest,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<runtime.ApiResponse<ChatDetailV1Dto>> {
    if (requestParameters.chatIdDto == null) {
      throw new runtime.RequiredError(
        'chatIdDto',
        'Required parameter "chatIdDto" was null or undefined when calling getChatDetail().',
      );
    }

    const queryParameters: any = {};

    const headerParameters: runtime.HTTPHeaders = {};

    headerParameters['Content-Type'] = 'application/json';

    const urlPath = `/api/v1/chat/getChatDetail`;

    const response = await this.request(
      {
        path: urlPath,
        method: 'POST',
        headers: headerParameters,
        query: queryParameters,
        body: ChatIdDtoToJSON(requestParameters.chatIdDto),
      },
      initOverrides,
    );

    return new runtime.JSONApiResponse(response, (jsonValue) => ChatDetailV1DtoFromJSON(jsonValue));
  }

  /**
   * Get chat details
   */
  async getChatDetail(
    chatIdDto: ChatIdDto,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<ChatDetailV1Dto> {
    const response = await this.getChatDetailRaw({ chatIdDto: chatIdDto }, initOverrides);
    return await response.value();
  }

  /**
   * List available chat models
   */
  async listChatModelsRaw(
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<
    runtime.ApiResponse<{ [key: string]: ChatV1ControllerListChatModels200ResponseValue }>
  > {
    const queryParameters: any = {};

    const headerParameters: runtime.HTTPHeaders = {};

    const urlPath = `/api/v1/chat/listChatModels`;

    const response = await this.request(
      {
        path: urlPath,
        method: 'POST',
        headers: headerParameters,
        query: queryParameters,
      },
      initOverrides,
    );

    return new runtime.JSONApiResponse(response, (jsonValue) =>
      runtime.mapValues(jsonValue, ChatV1ControllerListChatModels200ResponseValueFromJSON),
    );
  }

  /**
   * List available chat models
   */
  async listChatModels(
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<{ [key: string]: ChatV1ControllerListChatModels200ResponseValue }> {
    const response = await this.listChatModelsRaw(initOverrides);
    return await response.value();
  }

  /**
   * Query chat details by origin
   */
  async queryChatDetailsByOriginRaw(
    requestParameters: ChatV1ControllerQueryChatDetailsByOriginRequest,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<runtime.ApiResponse<ChatDetailListV1Dto>> {
    if (requestParameters.queryChatDetailsByOriginDto == null) {
      throw new runtime.RequiredError(
        'queryChatDetailsByOriginDto',
        'Required parameter "queryChatDetailsByOriginDto" was null or undefined when calling queryChatDetailsByOrigin().',
      );
    }

    const queryParameters: any = {};

    const headerParameters: runtime.HTTPHeaders = {};

    headerParameters['Content-Type'] = 'application/json';

    const urlPath = `/api/v1/chat/queryChatDetailsByOrigin`;

    const response = await this.request(
      {
        path: urlPath,
        method: 'POST',
        headers: headerParameters,
        query: queryParameters,
        body: QueryChatDetailsByOriginDtoToJSON(requestParameters.queryChatDetailsByOriginDto),
      },
      initOverrides,
    );

    return new runtime.JSONApiResponse(response, (jsonValue) =>
      ChatDetailListV1DtoFromJSON(jsonValue),
    );
  }

  /**
   * Query chat details by origin
   */
  async queryChatDetailsByOrigin(
    queryChatDetailsByOriginDto: QueryChatDetailsByOriginDto,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<ChatDetailListV1Dto> {
    const response = await this.queryChatDetailsByOriginRaw(
      { queryChatDetailsByOriginDto: queryChatDetailsByOriginDto },
      initOverrides,
    );
    return await response.value();
  }

  /**
   * Regenerates an AI assistant message and returns a server-sent event stream with V1-compatible stream messages
   * Regenerate a chat message
   */
  async regenerateMessageRaw(
    requestParameters: ChatV1ControllerRegenerateMessageRequest,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<runtime.ApiResponse<ChatV1ControllerCreateChat200Response>> {
    if (requestParameters.regenerateMessageV1Dto == null) {
      throw new runtime.RequiredError(
        'regenerateMessageV1Dto',
        'Required parameter "regenerateMessageV1Dto" was null or undefined when calling regenerateMessage().',
      );
    }

    const queryParameters: any = {};

    const headerParameters: runtime.HTTPHeaders = {};

    headerParameters['Content-Type'] = 'application/json';

    const urlPath = `/api/v1/chat/regenerateMessage`;

    const response = await this.request(
      {
        path: urlPath,
        method: 'POST',
        headers: headerParameters,
        query: queryParameters,
        body: RegenerateMessageV1DtoToJSON(requestParameters.regenerateMessageV1Dto),
      },
      initOverrides,
    );

    return new runtime.JSONApiResponse(response, (jsonValue) =>
      ChatV1ControllerCreateChat200ResponseFromJSON(jsonValue),
    );
  }

  /**
   * Regenerates an AI assistant message and returns a server-sent event stream with V1-compatible stream messages
   * Regenerate a chat message
   */
  async regenerateMessage(
    regenerateMessageV1Dto: RegenerateMessageV1Dto,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<ChatV1ControllerCreateChat200Response> {
    const response = await this.regenerateMessageRaw(
      { regenerateMessageV1Dto: regenerateMessageV1Dto },
      initOverrides,
    );
    return await response.value();
  }

  /**
   * Save user and assistant messages to a chat
   */
  async saveMessagesRaw(
    requestParameters: ChatV1ControllerSaveMessagesRequest,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<runtime.ApiResponse<ChatDetailV1Dto>> {
    if (requestParameters.saveMessagesDto == null) {
      throw new runtime.RequiredError(
        'saveMessagesDto',
        'Required parameter "saveMessagesDto" was null or undefined when calling saveMessages().',
      );
    }

    const queryParameters: any = {};

    const headerParameters: runtime.HTTPHeaders = {};

    headerParameters['Content-Type'] = 'application/json';

    const urlPath = `/api/v1/chat/saveMessages`;

    const response = await this.request(
      {
        path: urlPath,
        method: 'POST',
        headers: headerParameters,
        query: queryParameters,
        body: SaveMessagesDtoToJSON(requestParameters.saveMessagesDto),
      },
      initOverrides,
    );

    return new runtime.JSONApiResponse(response, (jsonValue) => ChatDetailV1DtoFromJSON(jsonValue));
  }

  /**
   * Save user and assistant messages to a chat
   */
  async saveMessages(
    saveMessagesDto: SaveMessagesDto,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<ChatDetailV1Dto> {
    const response = await this.saveMessagesRaw(
      { saveMessagesDto: saveMessagesDto },
      initOverrides,
    );
    return await response.value();
  }

  /**
   * Sends a message to an existing chat and returns a server-sent event stream with V1-compatible stream messages
   * Send a message to an existing chat
   */
  async sendMessageRaw(
    requestParameters: ChatV1ControllerSendMessageRequest,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<runtime.ApiResponse<ChatV1ControllerCreateChat200Response>> {
    if (requestParameters.sendMessageV1Dto == null) {
      throw new runtime.RequiredError(
        'sendMessageV1Dto',
        'Required parameter "sendMessageV1Dto" was null or undefined when calling sendMessage().',
      );
    }

    const queryParameters: any = {};

    const headerParameters: runtime.HTTPHeaders = {};

    headerParameters['Content-Type'] = 'application/json';

    const urlPath = `/api/v1/chat/sendMessage`;

    const response = await this.request(
      {
        path: urlPath,
        method: 'POST',
        headers: headerParameters,
        query: queryParameters,
        body: SendMessageV1DtoToJSON(requestParameters.sendMessageV1Dto),
      },
      initOverrides,
    );

    return new runtime.JSONApiResponse(response, (jsonValue) =>
      ChatV1ControllerCreateChat200ResponseFromJSON(jsonValue),
    );
  }

  /**
   * Sends a message to an existing chat and returns a server-sent event stream with V1-compatible stream messages
   * Send a message to an existing chat
   */
  async sendMessage(
    sendMessageV1Dto: SendMessageV1Dto,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<ChatV1ControllerCreateChat200Response> {
    const response = await this.sendMessageRaw(
      { sendMessageV1Dto: sendMessageV1Dto },
      initOverrides,
    );
    return await response.value();
  }

  /**
   * Update tool result
   */
  async updateCompletionBlockRaw(
    requestParameters: ChatV1ControllerUpdateCompletionBlockRequest,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<runtime.ApiResponse<void>> {
    if (requestParameters.updateCompletionBlockDto == null) {
      throw new runtime.RequiredError(
        'updateCompletionBlockDto',
        'Required parameter "updateCompletionBlockDto" was null or undefined when calling updateCompletionBlock().',
      );
    }

    const queryParameters: any = {};

    const headerParameters: runtime.HTTPHeaders = {};

    headerParameters['Content-Type'] = 'application/json';

    const urlPath = `/api/v1/chat/updateCompletionBlock`;

    const response = await this.request(
      {
        path: urlPath,
        method: 'POST',
        headers: headerParameters,
        query: queryParameters,
        body: UpdateCompletionBlockDtoToJSON(requestParameters.updateCompletionBlockDto),
      },
      initOverrides,
    );

    return new runtime.VoidApiResponse(response);
  }

  /**
   * Update tool result
   */
  async updateCompletionBlock(
    updateCompletionBlockDto: UpdateCompletionBlockDto,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<void> {
    await this.updateCompletionBlockRaw(
      { updateCompletionBlockDto: updateCompletionBlockDto },
      initOverrides,
    );
  }
}

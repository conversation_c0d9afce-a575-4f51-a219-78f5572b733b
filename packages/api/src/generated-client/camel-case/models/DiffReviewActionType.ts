/* tslint:disable */
/* eslint-disable */
/**
 * YouAPI
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 1.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

/**
 * 操作类型
 * @export
 * @enum {string}
 */
export enum DiffReviewActionType {
  accept = 'accept',
  reject = 'reject',
  unknownDefaultOpenApi = '11184809',
}

export function instanceOfDiffReviewActionType(value: any): boolean {
  for (const key in DiffReviewActionType) {
    if (Object.hasOwn(DiffReviewActionType, key)) {
      if (DiffReviewActionType[key as keyof typeof DiffReviewActionType] === value) {
        return true;
      }
    }
  }
  return false;
}

export function DiffReviewActionTypeFromJSON(json: any): DiffReviewActionType {
  return DiffReviewActionTypeFromJSONTyped(json, false);
}

export function DiffReviewActionTypeFromJSONTyped(
  json: any,
  _ignoreDiscriminator: boolean,
): DiffReviewActionType {
  return json as DiffReviewActionType;
}

export function DiffReviewActionTypeToJSON(value?: DiffReviewActionType | null): any {
  return value as any;
}

export function DiffReviewActionTypeToJSONTyped(
  value: any,
  _ignoreDiscriminator: boolean,
): DiffReviewActionType {
  return value as DiffReviewActionType;
}

/* tslint:disable */
/* eslint-disable */
/**
 * YouAPI
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 1.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../../../runtime';
import type { CompletionBlockStatusEnum } from './CompletionBlockStatusEnum';
import {
  CompletionBlockStatusEnumFromJSON,
  CompletionBlockStatusEnumFromJSONTyped,
  CompletionBlockStatusEnumToJSON,
  CompletionBlockStatusEnumToJSONTyped,
} from './CompletionBlockStatusEnum';
import type { CompletionBlockTypeEnum } from './CompletionBlockTypeEnum';
import {
  CompletionBlockTypeEnumFromJSON,
  CompletionBlockTypeEnumFromJSONTyped,
  CompletionBlockTypeEnumToJSON,
  CompletionBlockTypeEnumToJSONTyped,
} from './CompletionBlockTypeEnum';

/**
 *
 * @export
 * @interface CompletionBlockDto
 */
export interface CompletionBlockDto {
  /**
   * The ID of the block
   * @type {string}
   * @memberof CompletionBlockDto
   */
  id: string;
  /**
   * The ID of the chat
   * @type {string}
   * @memberof CompletionBlockDto
   */
  messageId: string;
  /**
   * The type of the block
   * @type {CompletionBlockTypeEnum}
   * @memberof CompletionBlockDto
   */
  type: CompletionBlockTypeEnum;
  /**
   * The status of the block
   * @type {CompletionBlockStatusEnum}
   * @memberof CompletionBlockDto
   */
  status: CompletionBlockStatusEnum;
  /**
   * The extra of the block
   * @type {object}
   * @memberof CompletionBlockDto
   */
  extra: object;
  /**
   * The data of the block
   * @type {string}
   * @memberof CompletionBlockDto
   */
  data?: string;
  /**
   * The tool ID of the block
   * @type {string}
   * @memberof CompletionBlockDto
   */
  toolId?: string;
  /**
   * The tool name of the block
   * @type {string}
   * @memberof CompletionBlockDto
   */
  toolName?: string;
  /**
   * The tool arguments of the block
   * @type {object}
   * @memberof CompletionBlockDto
   */
  toolArguments?: object;
  /**
   * The tool result of the block
   * @type {object}
   * @memberof CompletionBlockDto
   */
  toolResult?: object;
  /**
   * The tool response of the block
   * @type {string}
   * @memberof CompletionBlockDto
   */
  toolResponse?: string;
}

/**
 * Check if a given object implements the CompletionBlockDto interface.
 */
export function instanceOfCompletionBlockDto(value: object): value is CompletionBlockDto {
  if (!('id' in value) || value.id === undefined) return false;
  if (!('messageId' in value) || value.messageId === undefined) return false;
  if (!('type' in value) || value.type === undefined) return false;
  if (!('status' in value) || value.status === undefined) return false;
  if (!('extra' in value) || value.extra === undefined) return false;
  return true;
}

export function CompletionBlockDtoFromJSON(json: any): CompletionBlockDto {
  return CompletionBlockDtoFromJSONTyped(json, false);
}

export function CompletionBlockDtoFromJSONTyped(
  json: any,
  _ignoreDiscriminator: boolean,
): CompletionBlockDto {
  if (json == null) {
    return json;
  }
  return {
    id: json.id,
    messageId: json.messageId,
    type: CompletionBlockTypeEnumFromJSON(json.type),
    status: CompletionBlockStatusEnumFromJSON(json.status),
    extra: json.extra,
    data: json.data == null ? undefined : json.data,
    toolId: json.toolId == null ? undefined : json.toolId,
    toolName: json.toolName == null ? undefined : json.toolName,
    toolArguments: json.toolArguments == null ? undefined : json.toolArguments,
    toolResult: json.toolResult == null ? undefined : json.toolResult,
    toolResponse: json.toolResponse == null ? undefined : json.toolResponse,
  };
}

export function CompletionBlockDtoToJSON(json: any): CompletionBlockDto {
  return CompletionBlockDtoToJSONTyped(json, false);
}

export function CompletionBlockDtoToJSONTyped(
  value?: CompletionBlockDto | null,
  _ignoreDiscriminator: boolean = false,
): any {
  if (value == null) {
    return value;
  }

  return {
    id: value.id,
    messageId: value.messageId,
    type: CompletionBlockTypeEnumToJSON(value.type),
    status: CompletionBlockStatusEnumToJSON(value.status),
    extra: value.extra,
    data: value.data,
    toolId: value.toolId,
    toolName: value.toolName,
    toolArguments: value.toolArguments,
    toolResult: value.toolResult,
    toolResponse: value.toolResponse,
  };
}

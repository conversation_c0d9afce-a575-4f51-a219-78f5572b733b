/* tslint:disable */
/* eslint-disable */
/**
 * YouAPI
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 1.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../../../runtime';
import type { LLMs } from './LLMs';
import { LLMsFromJSON, LLMsFromJSONTyped, LLMsToJSON, LLMsToJSONTyped } from './LLMs';

/**
 *
 * @export
 * @interface SendMessageV1Dto
 */
export interface SendMessageV1Dto {
  /**
   * Chat ID to send message to
   * @type {string}
   * @memberof SendMessageV1Dto
   */
  chatId: string;
  /**
   * Message content
   * @type {string}
   * @memberof SendMessageV1Dto
   */
  message: string;
  /**
   * Selected text context
   * @type {string}
   * @memberof SendMessageV1Dto
   */
  selection?: string;
  /**
   * AI model to use
   * @type {LLMs}
   * @memberof SendMessageV1Dto
   */
  chatModel?: LLMs;
  /**
   * Enable web search
   * @type {boolean}
   * @memberof SendMessageV1Dto
   */
  webSearch?: boolean;
}

/**
 * Check if a given object implements the SendMessageV1Dto interface.
 */
export function instanceOfSendMessageV1Dto(value: object): value is SendMessageV1Dto {
  if (!('chatId' in value) || value.chatId === undefined) return false;
  if (!('message' in value) || value.message === undefined) return false;
  return true;
}

export function SendMessageV1DtoFromJSON(json: any): SendMessageV1Dto {
  return SendMessageV1DtoFromJSONTyped(json, false);
}

export function SendMessageV1DtoFromJSONTyped(
  json: any,
  _ignoreDiscriminator: boolean,
): SendMessageV1Dto {
  if (json == null) {
    return json;
  }
  return {
    chatId: json.chatId,
    message: json.message,
    selection: json.selection == null ? undefined : json.selection,
    chatModel: json.chatModel == null ? undefined : LLMsFromJSON(json.chatModel),
    webSearch: json.webSearch == null ? undefined : json.webSearch,
  };
}

export function SendMessageV1DtoToJSON(json: any): SendMessageV1Dto {
  return SendMessageV1DtoToJSONTyped(json, false);
}

export function SendMessageV1DtoToJSONTyped(
  value?: SendMessageV1Dto | null,
  _ignoreDiscriminator: boolean = false,
): any {
  if (value == null) {
    return value;
  }

  return {
    chatId: value.chatId,
    message: value.message,
    selection: value.selection,
    chatModel: LLMsToJSON(value.chatModel),
    webSearch: value.webSearch,
  };
}

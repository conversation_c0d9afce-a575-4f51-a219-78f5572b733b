/* tslint:disable */
/* eslint-disable */
/**
 * YouAPI
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 1.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../../../runtime';
import type { EntityType } from './EntityType';
import {
  EntityTypeFromJSON,
  EntityTypeFromJSONTyped,
  EntityTypeToJSON,
  EntityTypeToJSONTyped,
} from './EntityType';

/**
 *
 * @export
 * @interface ShortLinkDto
 */
export interface ShortLinkDto {
  /**
   * 短链接ID
   * @type {string}
   * @memberof ShortLinkDto
   */
  id: string;
  /**
   * 短链接标识符
   * @type {string}
   * @memberof ShortLinkDto
   */
  shortId: string;
  /**
   * 短链接是否激活
   * @type {boolean}
   * @memberof ShortLinkDto
   */
  active: boolean;
  /**
   * 创建时间
   * @type {Date}
   * @memberof ShortLinkDto
   */
  createdAt: Date;
  /**
   * 更新时间
   * @type {Date}
   * @memberof ShortLinkDto
   */
  updatedAt: Date;
  /**
   * 实体类型
   * @type {EntityType}
   * @memberof ShortLinkDto
   */
  entityType: EntityType;
  /**
   * 实体ID
   * @type {string}
   * @memberof ShortLinkDto
   */
  entityId: string;
}

/**
 * Check if a given object implements the ShortLinkDto interface.
 */
export function instanceOfShortLinkDto(value: object): value is ShortLinkDto {
  if (!('id' in value) || value.id === undefined) return false;
  if (!('shortId' in value) || value.shortId === undefined) return false;
  if (!('active' in value) || value.active === undefined) return false;
  if (!('createdAt' in value) || value.createdAt === undefined) return false;
  if (!('updatedAt' in value) || value.updatedAt === undefined) return false;
  if (!('entityType' in value) || value.entityType === undefined) return false;
  if (!('entityId' in value) || value.entityId === undefined) return false;
  return true;
}

export function ShortLinkDtoFromJSON(json: any): ShortLinkDto {
  return ShortLinkDtoFromJSONTyped(json, false);
}

export function ShortLinkDtoFromJSONTyped(json: any, _ignoreDiscriminator: boolean): ShortLinkDto {
  if (json == null) {
    return json;
  }
  return {
    id: json.id,
    shortId: json.shortId,
    active: json.active,
    createdAt: new Date(json.createdAt),
    updatedAt: new Date(json.updatedAt),
    entityType: EntityTypeFromJSON(json.entityType),
    entityId: json.entityId,
  };
}

export function ShortLinkDtoToJSON(json: any): ShortLinkDto {
  return ShortLinkDtoToJSONTyped(json, false);
}

export function ShortLinkDtoToJSONTyped(
  value?: ShortLinkDto | null,
  _ignoreDiscriminator: boolean = false,
): any {
  if (value == null) {
    return value;
  }

  return {
    id: value.id,
    shortId: value.shortId,
    active: value.active,
    createdAt: value.createdAt.toISOString(),
    updatedAt: value.updatedAt.toISOString(),
    entityType: EntityTypeToJSON(value.entityType),
    entityId: value.entityId,
  };
}

/* tslint:disable */
/* eslint-disable */
/**
 * YouAPI
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 1.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../../../runtime';
import type { PlaylistItemSourceEntityType } from './PlaylistItemSourceEntityType';
import {
  PlaylistItemSourceEntityTypeFromJSON,
  PlaylistItemSourceEntityTypeFromJSONTyped,
  PlaylistItemSourceEntityTypeToJSON,
  PlaylistItemSourceEntityTypeToJSONTyped,
} from './PlaylistItemSourceEntityType';
import type { PlaylistItemStatus } from './PlaylistItemStatus';
import {
  PlaylistItemStatusFromJSON,
  PlaylistItemStatusFromJSONTyped,
  PlaylistItemStatusToJSON,
  PlaylistItemStatusToJSONTyped,
} from './PlaylistItemStatus';

/**
 *
 * @export
 * @interface PlaylistItemDto
 */
export interface PlaylistItemDto {
  /**
   * 播放列表项ID
   * @type {string}
   * @memberof PlaylistItemDto
   */
  id: string;
  /**
   * 创建者ID
   * @type {string}
   * @memberof PlaylistItemDto
   */
  creatorId: string;
  /**
   * 空间ID
   * @type {string}
   * @memberof PlaylistItemDto
   */
  spaceId: string;
  /**
   * 创作板ID
   * @type {string}
   * @memberof PlaylistItemDto
   */
  boardId?: string;
  /**
   * 实体类型
   * @type {PlaylistItemSourceEntityType}
   * @memberof PlaylistItemDto
   */
  entityType: PlaylistItemSourceEntityType;
  /**
   * 实体ID
   * @type {string}
   * @memberof PlaylistItemDto
   */
  entityId: string;
  /**
   * 标题
   * @type {string}
   * @memberof PlaylistItemDto
   */
  title: string;
  /**
   * 播放地址
   * @type {string}
   * @memberof PlaylistItemDto
   */
  playUrl: string;
  /**
   * 音频时长（秒）
   * @type {number}
   * @memberof PlaylistItemDto
   */
  duration: number;
  /**
   * 状态
   * @type {PlaylistItemStatus}
   * @memberof PlaylistItemDto
   */
  status: PlaylistItemStatus;
  /**
   * 专辑封面URL
   * @type {string}
   * @memberof PlaylistItemDto
   */
  albumCoverUrl?: string;
  /**
   * 转录文本
   * @type {string}
   * @memberof PlaylistItemDto
   */
  transcript?: string;
  /**
   * 排序
   * @type {string}
   * @memberof PlaylistItemDto
   */
  rank?: string;
  /**
   * 播放进度
   * @type {number}
   * @memberof PlaylistItemDto
   */
  playbackProgress?: number;
  /**
   * 创建时间
   * @type {Date}
   * @memberof PlaylistItemDto
   */
  createdAt: Date;
  /**
   * 更新时间
   * @type {Date}
   * @memberof PlaylistItemDto
   */
  updatedAt: Date;
}

/**
 * Check if a given object implements the PlaylistItemDto interface.
 */
export function instanceOfPlaylistItemDto(value: object): value is PlaylistItemDto {
  if (!('id' in value) || value.id === undefined) return false;
  if (!('creatorId' in value) || value.creatorId === undefined) return false;
  if (!('spaceId' in value) || value.spaceId === undefined) return false;
  if (!('entityType' in value) || value.entityType === undefined) return false;
  if (!('entityId' in value) || value.entityId === undefined) return false;
  if (!('title' in value) || value.title === undefined) return false;
  if (!('playUrl' in value) || value.playUrl === undefined) return false;
  if (!('duration' in value) || value.duration === undefined) return false;
  if (!('status' in value) || value.status === undefined) return false;
  if (!('createdAt' in value) || value.createdAt === undefined) return false;
  if (!('updatedAt' in value) || value.updatedAt === undefined) return false;
  return true;
}

export function PlaylistItemDtoFromJSON(json: any): PlaylistItemDto {
  return PlaylistItemDtoFromJSONTyped(json, false);
}

export function PlaylistItemDtoFromJSONTyped(
  json: any,
  _ignoreDiscriminator: boolean,
): PlaylistItemDto {
  if (json == null) {
    return json;
  }
  return {
    id: json.id,
    creatorId: json.creatorId,
    spaceId: json.spaceId,
    boardId: json.boardId == null ? undefined : json.boardId,
    entityType: PlaylistItemSourceEntityTypeFromJSON(json.entityType),
    entityId: json.entityId,
    title: json.title,
    playUrl: json.playUrl,
    duration: json.duration,
    status: PlaylistItemStatusFromJSON(json.status),
    albumCoverUrl: json.albumCoverUrl == null ? undefined : json.albumCoverUrl,
    transcript: json.transcript == null ? undefined : json.transcript,
    rank: json.rank == null ? undefined : json.rank,
    playbackProgress: json.playbackProgress == null ? undefined : json.playbackProgress,
    createdAt: new Date(json.createdAt),
    updatedAt: new Date(json.updatedAt),
  };
}

export function PlaylistItemDtoToJSON(json: any): PlaylistItemDto {
  return PlaylistItemDtoToJSONTyped(json, false);
}

export function PlaylistItemDtoToJSONTyped(
  value?: PlaylistItemDto | null,
  _ignoreDiscriminator: boolean = false,
): any {
  if (value == null) {
    return value;
  }

  return {
    id: value.id,
    creatorId: value.creatorId,
    spaceId: value.spaceId,
    boardId: value.boardId,
    entityType: PlaylistItemSourceEntityTypeToJSON(value.entityType),
    entityId: value.entityId,
    title: value.title,
    playUrl: value.playUrl,
    duration: value.duration,
    status: PlaylistItemStatusToJSON(value.status),
    albumCoverUrl: value.albumCoverUrl,
    transcript: value.transcript,
    rank: value.rank,
    playbackProgress: value.playbackProgress,
    createdAt: value.createdAt.toISOString(),
    updatedAt: value.updatedAt.toISOString(),
  };
}

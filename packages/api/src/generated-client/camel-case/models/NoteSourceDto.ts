/* tslint:disable */
/* eslint-disable */
/**
 * YouAPI
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 1.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../../../runtime';
import type { HtmlSelectionDto } from './HtmlSelectionDto';
import {
  HtmlSelectionDtoFromJSON,
  HtmlSelectionDtoFromJSONTyped,
  HtmlSelectionDtoToJSON,
  HtmlSelectionDtoToJSONTyped,
} from './HtmlSelectionDto';
import type { NoteQuoteDto } from './NoteQuoteDto';
import {
  NoteQuoteDtoFromJSON,
  NoteQuoteDtoFromJSONTyped,
  NoteQuoteDtoToJSON,
  NoteQuoteDtoToJSONTyped,
} from './NoteQuoteDto';
import type { NoteSourceEntityDto } from './NoteSourceEntityDto';
import {
  NoteSourceEntityDtoFromJSON,
  NoteSourceEntityDtoFromJSONTyped,
  NoteSourceEntityDtoToJSON,
  NoteSourceEntityDtoToJSONTyped,
} from './NoteSourceEntityDto';
import type { NoteSourceEntityType } from './NoteSourceEntityType';
import {
  NoteSourceEntityTypeFromJSON,
  NoteSourceEntityTypeFromJSONTyped,
  NoteSourceEntityTypeToJSON,
  NoteSourceEntityTypeToJSONTyped,
} from './NoteSourceEntityType';

/**
 *
 * @export
 * @interface NoteSourceDto
 */
export interface NoteSourceDto {
  /**
   * 来源实体类型
   * @type {NoteSourceEntityType}
   * @memberof NoteSourceDto
   */
  entityType: NoteSourceEntityType;
  /**
   * 来源实体ID（兼容字段）
   * @type {string}
   * @memberof NoteSourceDto
   */
  entityId: string;
  /**
   * 来源实体信息
   * @type {NoteSourceEntityDto}
   * @memberof NoteSourceDto
   */
  entity: NoteSourceEntityDto;
  /**
   * HTML选区信息
   * @type {HtmlSelectionDto}
   * @memberof NoteSourceDto
   */
  selection?: HtmlSelectionDto;
  /**
   * 引用内容
   * @type {NoteQuoteDto}
   * @memberof NoteSourceDto
   */
  quote?: NoteQuoteDto;
}

/**
 * Check if a given object implements the NoteSourceDto interface.
 */
export function instanceOfNoteSourceDto(value: object): value is NoteSourceDto {
  if (!('entityType' in value) || value.entityType === undefined) return false;
  if (!('entityId' in value) || value.entityId === undefined) return false;
  if (!('entity' in value) || value.entity === undefined) return false;
  return true;
}

export function NoteSourceDtoFromJSON(json: any): NoteSourceDto {
  return NoteSourceDtoFromJSONTyped(json, false);
}

export function NoteSourceDtoFromJSONTyped(
  json: any,
  _ignoreDiscriminator: boolean,
): NoteSourceDto {
  if (json == null) {
    return json;
  }
  return {
    entityType: NoteSourceEntityTypeFromJSON(json.entityType),
    entityId: json.entityId,
    entity: NoteSourceEntityDtoFromJSON(json.entity),
    selection: json.selection == null ? undefined : HtmlSelectionDtoFromJSON(json.selection),
    quote: json.quote == null ? undefined : NoteQuoteDtoFromJSON(json.quote),
  };
}

export function NoteSourceDtoToJSON(json: any): NoteSourceDto {
  return NoteSourceDtoToJSONTyped(json, false);
}

export function NoteSourceDtoToJSONTyped(
  value?: NoteSourceDto | null,
  _ignoreDiscriminator: boolean = false,
): any {
  if (value == null) {
    return value;
  }

  return {
    entityType: NoteSourceEntityTypeToJSON(value.entityType),
    entityId: value.entityId,
    entity: NoteSourceEntityDtoToJSON(value.entity),
    selection: HtmlSelectionDtoToJSON(value.selection),
    quote: NoteQuoteDtoToJSON(value.quote),
  };
}

/* tslint:disable */
/* eslint-disable */
/**
 * YouAPI
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 1.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

/**
 * The status being updated
 * @export
 * @enum {string}
 */
export enum MessageStatusEnum {
  queued = 'queued',
  generating = 'generating',
  success = 'success',
  errored = 'errored',
  aborted = 'aborted',
  unknownDefaultOpenApi = '11184809',
}

export function instanceOfMessageStatusEnum(value: any): boolean {
  for (const key in MessageStatusEnum) {
    if (Object.hasOwn(MessageStatusEnum, key)) {
      if (MessageStatusEnum[key as keyof typeof MessageStatusEnum] === value) {
        return true;
      }
    }
  }
  return false;
}

export function MessageStatusEnumFromJSON(json: any): MessageStatusEnum {
  return MessageStatusEnumFromJSONTyped(json, false);
}

export function MessageStatusEnumFromJSONTyped(
  json: any,
  _ignoreDiscriminator: boolean,
): MessageStatusEnum {
  return json as MessageStatusEnum;
}

export function MessageStatusEnumToJSON(value?: MessageStatusEnum | null): any {
  return value as any;
}

export function MessageStatusEnumToJSONTyped(
  value: any,
  _ignoreDiscriminator: boolean,
): MessageStatusEnum {
  return value as MessageStatusEnum;
}

/* tslint:disable */
/* eslint-disable */
/**
 * YouAPI
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 1.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../../../runtime';
import type { ChatOriginTypeEnum } from './ChatOriginTypeEnum';
import {
  ChatOriginTypeEnumFromJSON,
  ChatOriginTypeEnumFromJSONTyped,
  ChatOriginTypeEnumToJSON,
  ChatOriginTypeEnumToJSONTyped,
} from './ChatOriginTypeEnum';
import type { EditCommandDto } from './EditCommandDto';
import {
  EditCommandDtoFromJSON,
  EditCommandDtoFromJSONTyped,
  EditCommandDtoToJSON,
  EditCommandDtoToJSONTyped,
} from './EditCommandDto';
import type { MessageModeEnum } from './MessageModeEnum';
import {
  MessageModeEnumFromJSON,
  MessageModeEnumFromJSONTyped,
  MessageModeEnumToJSON,
  MessageModeEnumToJSONTyped,
} from './MessageModeEnum';
import type { MessageRoleEnum } from './MessageRoleEnum';
import {
  MessageRoleEnumFromJSON,
  MessageRoleEnumFromJSONTyped,
  MessageRoleEnumToJSON,
  MessageRoleEnumToJSONTyped,
} from './MessageRoleEnum';
import type { MessageStatusEnum } from './MessageStatusEnum';
import {
  MessageStatusEnumFromJSON,
  MessageStatusEnumFromJSONTyped,
  MessageStatusEnumToJSON,
  MessageStatusEnumToJSONTyped,
} from './MessageStatusEnum';
import type { UseToolsDto } from './UseToolsDto';
import {
  UseToolsDtoFromJSON,
  UseToolsDtoFromJSONTyped,
  UseToolsDtoToJSON,
  UseToolsDtoToJSONTyped,
} from './UseToolsDto';

/**
 *
 * @export
 * @interface CompletionMessageDto
 */
export interface CompletionMessageDto {
  /**
   * The ID of the message
   * @type {string}
   * @memberof CompletionMessageDto
   */
  id: string;
  /**
   * The ID of the chat
   * @type {string}
   * @memberof CompletionMessageDto
   */
  chatId: string;
  /**
   * The creation time of the chat
   * @type {string}
   * @memberof CompletionMessageDto
   */
  createdAt: string;
  /**
   * The update time of the chat
   * @type {string}
   * @memberof CompletionMessageDto
   */
  updatedAt: string;
  /**
   * The status of the message
   * @type {MessageRoleEnum}
   * @memberof CompletionMessageDto
   */
  role: MessageRoleEnum;
  /**
   * The status of the message
   * @type {MessageStatusEnum}
   * @memberof CompletionMessageDto
   */
  status: MessageStatusEnum;
  /**
   * The content of the message
   * @type {string}
   * @memberof CompletionMessageDto
   */
  content?: string;
  /**
   * The origin of the message
   * @type {ChatOriginTypeEnum}
   * @memberof CompletionMessageDto
   */
  origin?: ChatOriginTypeEnum;
  /**
   * The selection of the message
   * @type {string}
   * @memberof CompletionMessageDto
   */
  selection?: string;
  /**
   * The at references of the message
   * @type {Array<string>}
   * @memberof CompletionMessageDto
   */
  atReferences?: Array<string>;
  /**
   * The board ID of the message
   * @type {string}
   * @memberof CompletionMessageDto
   */
  boardId?: string;
  /**
   * Tools configuration
   * @type {Array<UseToolsDto>}
   * @memberof CompletionMessageDto
   */
  tools?: Array<UseToolsDto>;
  /**
   * Edit command
   * @type {EditCommandDto}
   * @memberof CompletionMessageDto
   */
  command?: EditCommandDto;
  /**
   * Message mode
   * @type {MessageModeEnum}
   * @memberof CompletionMessageDto
   */
  mode: MessageModeEnum;
  /**
   * Shortcut information
   * @type {Array<UseToolsDto>}
   * @memberof CompletionMessageDto
   */
  shortcut?: Array<UseToolsDto>;
}

/**
 * Check if a given object implements the CompletionMessageDto interface.
 */
export function instanceOfCompletionMessageDto(value: object): value is CompletionMessageDto {
  if (!('id' in value) || value.id === undefined) return false;
  if (!('chatId' in value) || value.chatId === undefined) return false;
  if (!('createdAt' in value) || value.createdAt === undefined) return false;
  if (!('updatedAt' in value) || value.updatedAt === undefined) return false;
  if (!('role' in value) || value.role === undefined) return false;
  if (!('status' in value) || value.status === undefined) return false;
  if (!('mode' in value) || value.mode === undefined) return false;
  return true;
}

export function CompletionMessageDtoFromJSON(json: any): CompletionMessageDto {
  return CompletionMessageDtoFromJSONTyped(json, false);
}

export function CompletionMessageDtoFromJSONTyped(
  json: any,
  _ignoreDiscriminator: boolean,
): CompletionMessageDto {
  if (json == null) {
    return json;
  }
  return {
    id: json.id,
    chatId: json.chatId,
    createdAt: json.createdAt,
    updatedAt: json.updatedAt,
    role: MessageRoleEnumFromJSON(json.role),
    status: MessageStatusEnumFromJSON(json.status),
    content: json.content == null ? undefined : json.content,
    origin: json.origin == null ? undefined : ChatOriginTypeEnumFromJSON(json.origin),
    selection: json.selection == null ? undefined : json.selection,
    atReferences: json.atReferences == null ? undefined : json.atReferences,
    boardId: json.boardId == null ? undefined : json.boardId,
    tools: json.tools == null ? undefined : (json.tools as Array<any>).map(UseToolsDtoFromJSON),
    command: json.command == null ? undefined : EditCommandDtoFromJSON(json.command),
    mode: MessageModeEnumFromJSON(json.mode),
    shortcut:
      json.shortcut == null ? undefined : (json.shortcut as Array<any>).map(UseToolsDtoFromJSON),
  };
}

export function CompletionMessageDtoToJSON(json: any): CompletionMessageDto {
  return CompletionMessageDtoToJSONTyped(json, false);
}

export function CompletionMessageDtoToJSONTyped(
  value?: CompletionMessageDto | null,
  _ignoreDiscriminator: boolean = false,
): any {
  if (value == null) {
    return value;
  }

  return {
    id: value.id,
    chatId: value.chatId,
    createdAt: value.createdAt,
    updatedAt: value.updatedAt,
    role: MessageRoleEnumToJSON(value.role),
    status: MessageStatusEnumToJSON(value.status),
    content: value.content,
    origin: ChatOriginTypeEnumToJSON(value.origin),
    selection: value.selection,
    atReferences: value.atReferences,
    boardId: value.boardId,
    tools: value.tools == null ? undefined : (value.tools as Array<any>).map(UseToolsDtoToJSON),
    command: EditCommandDtoToJSON(value.command),
    mode: MessageModeEnumToJSON(value.mode),
    shortcut:
      value.shortcut == null ? undefined : (value.shortcut as Array<any>).map(UseToolsDtoToJSON),
  };
}

/* tslint:disable */
/* eslint-disable */
/**
 * YouAPI
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 1.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

/**
 * 状态
 * @export
 * @enum {string}
 */
export enum PlaylistItemStatus {
  generating = 'generating',
  success = 'success',
  failed = 'failed',
  unknownDefaultOpenApi = '11184809',
}

export function instanceOfPlaylistItemStatus(value: any): boolean {
  for (const key in PlaylistItemStatus) {
    if (Object.hasOwn(PlaylistItemStatus, key)) {
      if (PlaylistItemStatus[key as keyof typeof PlaylistItemStatus] === value) {
        return true;
      }
    }
  }
  return false;
}

export function PlaylistItemStatusFromJSON(json: any): PlaylistItemStatus {
  return PlaylistItemStatusFromJSONTyped(json, false);
}

export function PlaylistItemStatusFromJSONTyped(
  json: any,
  _ignoreDiscriminator: boolean,
): PlaylistItemStatus {
  return json as PlaylistItemStatus;
}

export function PlaylistItemStatusToJSON(value?: PlaylistItemStatus | null): any {
  return value as any;
}

export function PlaylistItemStatusToJSONTyped(
  value: any,
  _ignoreDiscriminator: boolean,
): PlaylistItemStatus {
  return value as PlaylistItemStatus;
}

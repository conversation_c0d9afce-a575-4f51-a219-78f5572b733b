/* tslint:disable */
/* eslint-disable */
/**
 * YouAPI
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 1.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../../../runtime';
import type { AtReferenceDto } from './AtReferenceDto';
import {
  AtReferenceDtoFromJSON,
  AtReferenceDtoFromJSONTyped,
  AtReferenceDtoToJSON,
  AtReferenceDtoToJSONTyped,
} from './AtReferenceDto';
import type { ChatOriginDto } from './ChatOriginDto';
import {
  ChatOriginDtoFromJSON,
  ChatOriginDtoFromJSONTyped,
  ChatOriginDtoToJSON,
  ChatOriginDtoToJSONTyped,
} from './ChatOriginDto';
import type { EditCommandDto } from './EditCommandDto';
import {
  EditCommandDtoFromJSON,
  EditCommandDtoFromJSONTyped,
  EditCommandDtoToJSON,
  EditCommandDtoToJSONTyped,
} from './EditCommandDto';
import type { LLMs } from './LLMs';
import { LLMsFromJSON, LLMsFromJSONTyped, LLMsToJSON, LLMsToJSONTyped } from './LLMs';
import type { MessageModeEnum } from './MessageModeEnum';
import {
  MessageModeEnumFromJSON,
  MessageModeEnumFromJSONTyped,
  MessageModeEnumToJSON,
  MessageModeEnumToJSONTyped,
} from './MessageModeEnum';
import type { ShortcutDto } from './ShortcutDto';
import {
  ShortcutDtoFromJSON,
  ShortcutDtoFromJSONTyped,
  ShortcutDtoToJSON,
  ShortcutDtoToJSONTyped,
} from './ShortcutDto';
import type { UseToolsDto } from './UseToolsDto';
import {
  UseToolsDtoFromJSON,
  UseToolsDtoFromJSONTyped,
  UseToolsDtoToJSON,
  UseToolsDtoToJSONTyped,
} from './UseToolsDto';

/**
 *
 * @export
 * @interface CreateChatV2Dto
 */
export interface CreateChatV2Dto {
  /**
   * Message content
   * @type {string}
   * @memberof CreateChatV2Dto
   */
  message: string;
  /**
   * Board Id
   * @type {string}
   * @memberof CreateChatV2Dto
   */
  boardId?: string;
  /**
   * Selected text context
   * @type {string}
   * @memberof CreateChatV2Dto
   */
  selection?: string;
  /**
   * Referenced entities
   * @type {Array<AtReferenceDto>}
   * @memberof CreateChatV2Dto
   */
  atReferences?: Array<AtReferenceDto>;
  /**
   * AI model to use
   * @type {LLMs}
   * @memberof CreateChatV2Dto
   */
  chatModel?: LLMs;
  /**
   * Tools configuration
   * @type {Array<UseToolsDto>}
   * @memberof CreateChatV2Dto
   */
  tools?: Array<UseToolsDto>;
  /**
   * Shortcut information
   * @type {Array<ShortcutDto>}
   * @memberof CreateChatV2Dto
   */
  shortcut?: Array<ShortcutDto>;
  /**
   * Message mode
   * @type {MessageModeEnum}
   * @memberof CreateChatV2Dto
   */
  messageMode?: MessageModeEnum;
  /**
   * Edit command
   * @type {Array<EditCommandDto>}
   * @memberof CreateChatV2Dto
   */
  command?: Array<EditCommandDto>;
  /**
   * Chat origin context
   * @type {ChatOriginDto}
   * @memberof CreateChatV2Dto
   */
  origin?: ChatOriginDto;
}

/**
 * Check if a given object implements the CreateChatV2Dto interface.
 */
export function instanceOfCreateChatV2Dto(value: object): value is CreateChatV2Dto {
  if (!('message' in value) || value.message === undefined) return false;
  return true;
}

export function CreateChatV2DtoFromJSON(json: any): CreateChatV2Dto {
  return CreateChatV2DtoFromJSONTyped(json, false);
}

export function CreateChatV2DtoFromJSONTyped(
  json: any,
  _ignoreDiscriminator: boolean,
): CreateChatV2Dto {
  if (json == null) {
    return json;
  }
  return {
    message: json.message,
    boardId: json.boardId == null ? undefined : json.boardId,
    selection: json.selection == null ? undefined : json.selection,
    atReferences:
      json.atReferences == null
        ? undefined
        : (json.atReferences as Array<any>).map(AtReferenceDtoFromJSON),
    chatModel: json.chatModel == null ? undefined : LLMsFromJSON(json.chatModel),
    tools: json.tools == null ? undefined : (json.tools as Array<any>).map(UseToolsDtoFromJSON),
    shortcut:
      json.shortcut == null ? undefined : (json.shortcut as Array<any>).map(ShortcutDtoFromJSON),
    messageMode: json.messageMode == null ? undefined : MessageModeEnumFromJSON(json.messageMode),
    command:
      json.command == null ? undefined : (json.command as Array<any>).map(EditCommandDtoFromJSON),
    origin: json.origin == null ? undefined : ChatOriginDtoFromJSON(json.origin),
  };
}

export function CreateChatV2DtoToJSON(json: any): CreateChatV2Dto {
  return CreateChatV2DtoToJSONTyped(json, false);
}

export function CreateChatV2DtoToJSONTyped(
  value?: CreateChatV2Dto | null,
  _ignoreDiscriminator: boolean = false,
): any {
  if (value == null) {
    return value;
  }

  return {
    message: value.message,
    boardId: value.boardId,
    selection: value.selection,
    atReferences:
      value.atReferences == null
        ? undefined
        : (value.atReferences as Array<any>).map(AtReferenceDtoToJSON),
    chatModel: LLMsToJSON(value.chatModel),
    tools: value.tools == null ? undefined : (value.tools as Array<any>).map(UseToolsDtoToJSON),
    shortcut:
      value.shortcut == null ? undefined : (value.shortcut as Array<any>).map(ShortcutDtoToJSON),
    messageMode: MessageModeEnumToJSON(value.messageMode),
    command:
      value.command == null ? undefined : (value.command as Array<any>).map(EditCommandDtoToJSON),
    origin: ChatOriginDtoToJSON(value.origin),
  };
}

/* tslint:disable */
/* eslint-disable */
/**
 * YouAPI
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 1.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../../../runtime';
import type { NoteSourceEntityType } from './NoteSourceEntityType';
import {
  NoteSourceEntityTypeFromJSON,
  NoteSourceEntityTypeFromJSONTyped,
  NoteSourceEntityTypeToJSON,
  NoteSourceEntityTypeToJSONTyped,
} from './NoteSourceEntityType';

/**
 *
 * @export
 * @interface ListNotesSourceDto
 */
export interface ListNotesSourceDto {
  /**
   * 来源实体类型
   * @type {NoteSourceEntityType}
   * @memberof ListNotesSourceDto
   */
  entityType?: NoteSourceEntityType;
  /**
   * 来源实体ID
   * @type {string}
   * @memberof ListNotesSourceDto
   */
  entityId?: string;
}

/**
 * Check if a given object implements the ListNotesSourceDto interface.
 */
export function instanceOfListNotesSourceDto(value: object): value is ListNotesSourceDto {
  return true;
}

export function ListNotesSourceDtoFromJSON(json: any): ListNotesSourceDto {
  return ListNotesSourceDtoFromJSONTyped(json, false);
}

export function ListNotesSourceDtoFromJSONTyped(
  json: any,
  _ignoreDiscriminator: boolean,
): ListNotesSourceDto {
  if (json == null) {
    return json;
  }
  return {
    entityType: json.entityType == null ? undefined : NoteSourceEntityTypeFromJSON(json.entityType),
    entityId: json.entityId == null ? undefined : json.entityId,
  };
}

export function ListNotesSourceDtoToJSON(json: any): ListNotesSourceDto {
  return ListNotesSourceDtoToJSONTyped(json, false);
}

export function ListNotesSourceDtoToJSONTyped(
  value?: ListNotesSourceDto | null,
  _ignoreDiscriminator: boolean = false,
): any {
  if (value == null) {
    return value;
  }

  return {
    entityType: NoteSourceEntityTypeToJSON(value.entityType),
    entityId: value.entityId,
  };
}

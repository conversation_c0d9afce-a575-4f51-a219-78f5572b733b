/* tslint:disable */
/* eslint-disable */
/**
 * YouAPI
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 1.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

/**
 * The status of the block
 * @export
 * @enum {string}
 */
export enum CompletionBlockStatusEnum {
  queued = 'queued',
  generating = 'generating',
  executing = 'executing',
  completed = 'completed',
  errored = 'errored',
  aborted = 'aborted',
  unknownDefaultOpenApi = '11184809',
}

export function instanceOfCompletionBlockStatusEnum(value: any): boolean {
  for (const key in CompletionBlockStatusEnum) {
    if (Object.hasOwn(CompletionBlockStatusEnum, key)) {
      if (CompletionBlockStatusEnum[key as keyof typeof CompletionBlockStatusEnum] === value) {
        return true;
      }
    }
  }
  return false;
}

export function CompletionBlockStatusEnumFromJSON(json: any): CompletionBlockStatusEnum {
  return CompletionBlockStatusEnumFromJSONTyped(json, false);
}

export function CompletionBlockStatusEnumFromJSONTyped(
  json: any,
  _ignoreDiscriminator: boolean,
): CompletionBlockStatusEnum {
  return json as CompletionBlockStatusEnum;
}

export function CompletionBlockStatusEnumToJSON(value?: CompletionBlockStatusEnum | null): any {
  return value as any;
}

export function CompletionBlockStatusEnumToJSONTyped(
  value: any,
  _ignoreDiscriminator: boolean,
): CompletionBlockStatusEnum {
  return value as CompletionBlockStatusEnum;
}

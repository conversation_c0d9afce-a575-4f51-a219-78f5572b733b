/* tslint:disable */
/* eslint-disable */
/**
 * YouAPI
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 1.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../../../runtime';
import type { ThoughtContentDto } from './ThoughtContentDto';
import {
  ThoughtContentDtoFromJSON,
  ThoughtContentDtoFromJSONTyped,
  ThoughtContentDtoToJSON,
  ThoughtContentDtoToJSONTyped,
} from './ThoughtContentDto';
import type { TitleType } from './TitleType';
import {
  TitleTypeFromJSON,
  TitleTypeFromJSONTyped,
  TitleTypeToJSON,
  TitleTypeToJSONTyped,
} from './TitleType';

/**
 *
 * @export
 * @interface PatchThoughtDto
 */
export interface PatchThoughtDto {
  /**
   * 标题
   * @type {string}
   * @memberof PatchThoughtDto
   */
  title?: string;
  /**
   * 标题类型
   * @type {TitleType}
   * @memberof PatchThoughtDto
   */
  titleType?: TitleType;
  /**
   * 内容
   * @type {ThoughtContentDto}
   * @memberof PatchThoughtDto
   */
  content?: ThoughtContentDto;
  /**
   * 想法ID
   * @type {string}
   * @memberof PatchThoughtDto
   */
  id: string;
}

/**
 * Check if a given object implements the PatchThoughtDto interface.
 */
export function instanceOfPatchThoughtDto(value: object): value is PatchThoughtDto {
  if (!('id' in value) || value.id === undefined) return false;
  return true;
}

export function PatchThoughtDtoFromJSON(json: any): PatchThoughtDto {
  return PatchThoughtDtoFromJSONTyped(json, false);
}

export function PatchThoughtDtoFromJSONTyped(
  json: any,
  _ignoreDiscriminator: boolean,
): PatchThoughtDto {
  if (json == null) {
    return json;
  }
  return {
    title: json.title == null ? undefined : json.title,
    titleType: json.titleType == null ? undefined : TitleTypeFromJSON(json.titleType),
    content: json.content == null ? undefined : ThoughtContentDtoFromJSON(json.content),
    id: json.id,
  };
}

export function PatchThoughtDtoToJSON(json: any): PatchThoughtDto {
  return PatchThoughtDtoToJSONTyped(json, false);
}

export function PatchThoughtDtoToJSONTyped(
  value?: PatchThoughtDto | null,
  _ignoreDiscriminator: boolean = false,
): any {
  if (value == null) {
    return value;
  }

  return {
    title: value.title,
    titleType: TitleTypeToJSON(value.titleType),
    content: ThoughtContentDtoToJSON(value.content),
    id: value.id,
  };
}

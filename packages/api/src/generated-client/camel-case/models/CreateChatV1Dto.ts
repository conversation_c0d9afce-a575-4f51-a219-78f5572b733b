/* tslint:disable */
/* eslint-disable */
/**
 * YouAPI
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 1.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../../../runtime';
import type { ChatOriginDto } from './ChatOriginDto';
import {
  ChatOriginDtoFromJSON,
  ChatOriginDtoFromJSONTyped,
  Chat<PERSON><PERSON>inDtoToJSON,
  ChatOriginDtoToJSONTyped,
} from './ChatOriginDto';

/**
 *
 * @export
 * @interface CreateChatV1Dto
 */
export interface CreateChatV1Dto {
  /**
   * Chat origin context
   * @type {ChatOriginDto}
   * @memberof CreateChatV1Dto
   */
  origin: ChatOriginDto;
  /**
   * Initial message for the chat
   * @type {string}
   * @memberof CreateChatV1Dto
   */
  message: string;
  /**
   * AI model to use
   * @type {string}
   * @memberof CreateChatV1Dto
   */
  chatModel: string;
  /**
   * Selected text context
   * @type {string}
   * @memberof CreateChatV1Dto
   */
  selection?: string;
  /**
   * Enable web search
   * @type {boolean}
   * @memberof CreateChatV1Dto
   */
  webSearch?: boolean;
}

/**
 * Check if a given object implements the CreateChatV1Dto interface.
 */
export function instanceOfCreateChatV1Dto(value: object): value is CreateChatV1Dto {
  if (!('origin' in value) || value.origin === undefined) return false;
  if (!('message' in value) || value.message === undefined) return false;
  if (!('chatModel' in value) || value.chatModel === undefined) return false;
  return true;
}

export function CreateChatV1DtoFromJSON(json: any): CreateChatV1Dto {
  return CreateChatV1DtoFromJSONTyped(json, false);
}

export function CreateChatV1DtoFromJSONTyped(
  json: any,
  _ignoreDiscriminator: boolean,
): CreateChatV1Dto {
  if (json == null) {
    return json;
  }
  return {
    origin: ChatOriginDtoFromJSON(json.origin),
    message: json.message,
    chatModel: json.chatModel,
    selection: json.selection == null ? undefined : json.selection,
    webSearch: json.webSearch == null ? undefined : json.webSearch,
  };
}

export function CreateChatV1DtoToJSON(json: any): CreateChatV1Dto {
  return CreateChatV1DtoToJSONTyped(json, false);
}

export function CreateChatV1DtoToJSONTyped(
  value?: CreateChatV1Dto | null,
  _ignoreDiscriminator: boolean = false,
): any {
  if (value == null) {
    return value;
  }

  return {
    origin: ChatOriginDtoToJSON(value.origin),
    message: value.message,
    chatModel: value.chatModel,
    selection: value.selection,
    webSearch: value.webSearch,
  };
}

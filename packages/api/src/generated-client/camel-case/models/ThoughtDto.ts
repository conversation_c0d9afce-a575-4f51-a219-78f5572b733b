/* tslint:disable */
/* eslint-disable */
/**
 * YouAPI
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 1.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../../../runtime';
import type { ThoughtDtoContent } from './ThoughtDtoContent';
import {
  ThoughtDtoContentFromJSON,
  ThoughtDtoContentFromJSONTyped,
  ThoughtDtoContentToJSON,
  ThoughtDtoContentToJSONTyped,
} from './ThoughtDtoContent';
import type { TitleType } from './TitleType';
import {
  TitleTypeFromJSON,
  TitleTypeFromJSONTyped,
  TitleTypeToJSON,
  TitleTypeToJSONTyped,
} from './TitleType';
import type { Visibility } from './Visibility';
import {
  VisibilityFromJSON,
  VisibilityFromJSONTyped,
  VisibilityToJSON,
  VisibilityToJSONTyped,
} from './Visibility';

/**
 *
 * @export
 * @interface ThoughtDto
 */
export interface ThoughtDto {
  /**
   * 想法ID
   * @type {string}
   * @memberof ThoughtDto
   */
  id: string;
  /**
   * 创建时间
   * @type {Date}
   * @memberof ThoughtDto
   */
  createdAt: Date;
  /**
   * 更新时间
   * @type {Date}
   * @memberof ThoughtDto
   */
  updatedAt: Date;
  /**
   * 空间ID
   * @type {string}
   * @memberof ThoughtDto
   */
  spaceId: string;
  /**
   * 创建者ID
   * @type {string}
   * @memberof ThoughtDto
   */
  creatorId: string;
  /**
   * 创作板ID
   * @type {string}
   * @memberof ThoughtDto
   */
  boardId?: string;
  /**
   * 标题
   * @type {string}
   * @memberof ThoughtDto
   */
  title: string;
  /**
   * 标题类型
   * @type {TitleType}
   * @memberof ThoughtDto
   */
  titleType: TitleType;
  /**
   *
   * @type {ThoughtDtoContent}
   * @memberof ThoughtDto
   */
  content: ThoughtDtoContent;
  /**
   * 可见性
   * @type {Visibility}
   * @memberof ThoughtDto
   */
  visibility: Visibility;
  /**
   * 位置信息
   * @type {object}
   * @memberof ThoughtDto
   */
  position: object;
  /**
   * 创作板关联信息（已废弃，请使用 position 字段）
   * @type {object}
   * @memberof ThoughtDto
   * @deprecated
   */
  boardItem?: object;
  /**
   * 创作板关联信息（已废弃，请使用 position 字段）
   * @type {Array<string>}
   * @memberof ThoughtDto
   * @deprecated
   */
  boardIds?: Array<string>;
}

/**
 * Check if a given object implements the ThoughtDto interface.
 */
export function instanceOfThoughtDto(value: object): value is ThoughtDto {
  if (!('id' in value) || value.id === undefined) return false;
  if (!('createdAt' in value) || value.createdAt === undefined) return false;
  if (!('updatedAt' in value) || value.updatedAt === undefined) return false;
  if (!('spaceId' in value) || value.spaceId === undefined) return false;
  if (!('creatorId' in value) || value.creatorId === undefined) return false;
  if (!('title' in value) || value.title === undefined) return false;
  if (!('titleType' in value) || value.titleType === undefined) return false;
  if (!('content' in value) || value.content === undefined) return false;
  if (!('visibility' in value) || value.visibility === undefined) return false;
  if (!('position' in value) || value.position === undefined) return false;
  return true;
}

export function ThoughtDtoFromJSON(json: any): ThoughtDto {
  return ThoughtDtoFromJSONTyped(json, false);
}

export function ThoughtDtoFromJSONTyped(json: any, _ignoreDiscriminator: boolean): ThoughtDto {
  if (json == null) {
    return json;
  }
  return {
    id: json.id,
    createdAt: new Date(json.createdAt),
    updatedAt: new Date(json.updatedAt),
    spaceId: json.spaceId,
    creatorId: json.creatorId,
    boardId: json.boardId == null ? undefined : json.boardId,
    title: json.title,
    titleType: TitleTypeFromJSON(json.titleType),
    content: ThoughtDtoContentFromJSON(json.content),
    visibility: VisibilityFromJSON(json.visibility),
    position: json.position,
    boardItem: json.boardItem == null ? undefined : json.boardItem,
    boardIds: json.boardIds == null ? undefined : json.boardIds,
  };
}

export function ThoughtDtoToJSON(json: any): ThoughtDto {
  return ThoughtDtoToJSONTyped(json, false);
}

export function ThoughtDtoToJSONTyped(
  value?: ThoughtDto | null,
  _ignoreDiscriminator: boolean = false,
): any {
  if (value == null) {
    return value;
  }

  return {
    id: value.id,
    createdAt: value.createdAt.toISOString(),
    updatedAt: value.updatedAt.toISOString(),
    spaceId: value.spaceId,
    creatorId: value.creatorId,
    boardId: value.boardId,
    title: value.title,
    titleType: TitleTypeToJSON(value.titleType),
    content: ThoughtDtoContentToJSON(value.content),
    visibility: VisibilityToJSON(value.visibility),
    position: value.position,
    boardItem: value.boardItem,
    boardIds: value.boardIds,
  };
}

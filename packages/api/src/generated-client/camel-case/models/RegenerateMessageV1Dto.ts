/* tslint:disable */
/* eslint-disable */
/**
 * YouAPI
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 1.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../../../runtime';
import type { LLMs } from './LLMs';
import { LLMsFromJSON, LLMsFromJSONTyped, LLMsToJSON, LLMsToJSONTyped } from './LLMs';

/**
 *
 * @export
 * @interface RegenerateMessageV1Dto
 */
export interface RegenerateMessageV1Dto {
  /**
   * Chat ID containing the message to regenerate
   * @type {string}
   * @memberof RegenerateMessageV1Dto
   */
  chatId: string;
  /**
   * User message ID to regenerate from
   * @type {string}
   * @memberof RegenerateMessageV1Dto
   */
  userMessageId: string;
  /**
   * AI model to use for regeneration
   * @type {LLMs}
   * @memberof RegenerateMessageV1Dto
   */
  chatModel?: LLMs;
  /**
   * Enable web search for regeneration
   * @type {boolean}
   * @memberof RegenerateMessageV1Dto
   */
  webSearch?: boolean;
}

/**
 * Check if a given object implements the RegenerateMessageV1Dto interface.
 */
export function instanceOfRegenerateMessageV1Dto(value: object): value is RegenerateMessageV1Dto {
  if (!('chatId' in value) || value.chatId === undefined) return false;
  if (!('userMessageId' in value) || value.userMessageId === undefined) return false;
  return true;
}

export function RegenerateMessageV1DtoFromJSON(json: any): RegenerateMessageV1Dto {
  return RegenerateMessageV1DtoFromJSONTyped(json, false);
}

export function RegenerateMessageV1DtoFromJSONTyped(
  json: any,
  _ignoreDiscriminator: boolean,
): RegenerateMessageV1Dto {
  if (json == null) {
    return json;
  }
  return {
    chatId: json.chatId,
    userMessageId: json.userMessageId,
    chatModel: json.chatModel == null ? undefined : LLMsFromJSON(json.chatModel),
    webSearch: json.webSearch == null ? undefined : json.webSearch,
  };
}

export function RegenerateMessageV1DtoToJSON(json: any): RegenerateMessageV1Dto {
  return RegenerateMessageV1DtoToJSONTyped(json, false);
}

export function RegenerateMessageV1DtoToJSONTyped(
  value?: RegenerateMessageV1Dto | null,
  _ignoreDiscriminator: boolean = false,
): any {
  if (value == null) {
    return value;
  }

  return {
    chatId: value.chatId,
    userMessageId: value.userMessageId,
    chatModel: LLMsToJSON(value.chatModel),
    webSearch: value.webSearch,
  };
}

/* tslint:disable */
/* eslint-disable */
/**
 * YouAPI
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 1.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

/**
 * The mode of the chat
 * @export
 * @enum {string}
 */
export enum ChatModeEnum {
  chat = 'chat',
  newBoard = 'new_board',
  customAssistant = 'custom_assistant',
  assistantPreview = 'assistant_preview',
  unknownDefaultOpenApi = '11184809',
}

export function instanceOfChatModeEnum(value: any): boolean {
  for (const key in ChatModeEnum) {
    if (Object.hasOwn(ChatModeEnum, key)) {
      if (ChatModeEnum[key as keyof typeof ChatModeEnum] === value) {
        return true;
      }
    }
  }
  return false;
}

export function ChatModeEnumFromJSON(json: any): ChatModeEnum {
  return ChatModeEnumFromJSONTyped(json, false);
}

export function ChatModeEnumFromJSONTyped(json: any, _ignoreDiscriminator: boolean): ChatModeEnum {
  return json as ChatModeEnum;
}

export function ChatModeEnumToJSON(value?: ChatModeEnum | null): any {
  return value as any;
}

export function ChatModeEnumToJSONTyped(value: any, _ignoreDiscriminator: boolean): ChatModeEnum {
  return value as ChatModeEnum;
}

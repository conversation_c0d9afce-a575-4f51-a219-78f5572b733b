/* tslint:disable */
/* eslint-disable */
/**
 * YouAPI
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 1.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

/**
 * The type of the block
 * @export
 * @enum {string}
 */
export enum CompletionBlockTypeEnum {
  content = 'content',
  reasoning = 'reasoning',
  tool = 'tool',
  unknownDefaultOpenApi = '11184809',
}

export function instanceOfCompletionBlockTypeEnum(value: any): boolean {
  for (const key in CompletionBlockTypeEnum) {
    if (Object.hasOwn(CompletionBlockTypeEnum, key)) {
      if (CompletionBlockTypeEnum[key as keyof typeof CompletionBlockTypeEnum] === value) {
        return true;
      }
    }
  }
  return false;
}

export function CompletionBlockTypeEnumFromJSON(json: any): CompletionBlockTypeEnum {
  return CompletionBlockTypeEnumFromJSONTyped(json, false);
}

export function CompletionBlockTypeEnumFromJSONTyped(
  json: any,
  _ignoreDiscriminator: boolean,
): CompletionBlockTypeEnum {
  return json as CompletionBlockTypeEnum;
}

export function CompletionBlockTypeEnumToJSON(value?: CompletionBlockTypeEnum | null): any {
  return value as any;
}

export function CompletionBlockTypeEnumToJSONTyped(
  value: any,
  _ignoreDiscriminator: boolean,
): CompletionBlockTypeEnum {
  return value as CompletionBlockTypeEnum;
}

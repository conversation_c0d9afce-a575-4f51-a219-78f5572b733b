/* tslint:disable */
/* eslint-disable */
/**
 * YouAPI
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 1.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

/**
 * 标题类型
 * @export
 * @enum {string}
 */
export enum TitleType {
  default = 'default',
  ai = 'ai',
  manual = 'manual',
  unknownDefaultOpenApi = '11184809',
}

export function instanceOfTitleType(value: any): boolean {
  for (const key in TitleType) {
    if (Object.hasOwn(TitleType, key)) {
      if (TitleType[key as keyof typeof TitleType] === value) {
        return true;
      }
    }
  }
  return false;
}

export function TitleTypeFromJSON(json: any): TitleType {
  return TitleTypeFromJSONTyped(json, false);
}

export function TitleTypeFromJSONTyped(json: any, _ignoreDiscriminator: boolean): TitleType {
  return json as TitleType;
}

export function TitleTypeToJSON(value?: TitleType | null): any {
  return value as any;
}

export function TitleTypeToJSONTyped(value: any, _ignoreDiscriminator: boolean): TitleType {
  return value as TitleType;
}

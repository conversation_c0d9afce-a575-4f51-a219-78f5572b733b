/* tslint:disable */
/* eslint-disable */
/**
 * YouAPI
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 1.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../../../runtime';
import type { PlaylistItemSourceEntityType } from './PlaylistItemSourceEntityType';
import {
  PlaylistItemSourceEntityTypeFromJSON,
  PlaylistItemSourceEntityTypeFromJSONTyped,
  PlaylistItemSourceEntityTypeToJSON,
  PlaylistItemSourceEntityTypeToJSONTyped,
} from './PlaylistItemSourceEntityType';

/**
 *
 * @export
 * @interface CreatePlaylistItemDto
 */
export interface CreatePlaylistItemDto {
  /**
   * 实体类型
   * @type {PlaylistItemSourceEntityType}
   * @memberof CreatePlaylistItemDto
   */
  entityType: PlaylistItemSourceEntityType;
  /**
   * 实体ID
   * @type {string}
   * @memberof CreatePlaylistItemDto
   */
  entityId: string;
  /**
   * 创作板ID
   * @type {string}
   * @memberof CreatePlaylistItemDto
   */
  boardId?: string;
  /**
   * AI模型
   * @type {string}
   * @memberof CreatePlaylistItemDto
   */
  model?: string;
  /**
   * 声音类型
   * @type {string}
   * @memberof CreatePlaylistItemDto
   */
  voice?: string;
  /**
   * 情感类型
   * @type {string}
   * @memberof CreatePlaylistItemDto
   */
  emotion?: string;
}

/**
 * Check if a given object implements the CreatePlaylistItemDto interface.
 */
export function instanceOfCreatePlaylistItemDto(value: object): value is CreatePlaylistItemDto {
  if (!('entityType' in value) || value.entityType === undefined) return false;
  if (!('entityId' in value) || value.entityId === undefined) return false;
  return true;
}

export function CreatePlaylistItemDtoFromJSON(json: any): CreatePlaylistItemDto {
  return CreatePlaylistItemDtoFromJSONTyped(json, false);
}

export function CreatePlaylistItemDtoFromJSONTyped(
  json: any,
  _ignoreDiscriminator: boolean,
): CreatePlaylistItemDto {
  if (json == null) {
    return json;
  }
  return {
    entityType: PlaylistItemSourceEntityTypeFromJSON(json.entityType),
    entityId: json.entityId,
    boardId: json.boardId == null ? undefined : json.boardId,
    model: json.model == null ? undefined : json.model,
    voice: json.voice == null ? undefined : json.voice,
    emotion: json.emotion == null ? undefined : json.emotion,
  };
}

export function CreatePlaylistItemDtoToJSON(json: any): CreatePlaylistItemDto {
  return CreatePlaylistItemDtoToJSONTyped(json, false);
}

export function CreatePlaylistItemDtoToJSONTyped(
  value?: CreatePlaylistItemDto | null,
  _ignoreDiscriminator: boolean = false,
): any {
  if (value == null) {
    return value;
  }

  return {
    entityType: PlaylistItemSourceEntityTypeToJSON(value.entityType),
    entityId: value.entityId,
    boardId: value.boardId,
    model: value.model,
    voice: value.voice,
    emotion: value.emotion,
  };
}

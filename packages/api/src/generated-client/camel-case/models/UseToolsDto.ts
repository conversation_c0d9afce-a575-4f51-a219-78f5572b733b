/* tslint:disable */
/* eslint-disable */
/**
 * YouAPI
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 1.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../../../runtime';
import type { ImageGenerateToolDto } from './ImageGenerateToolDto';
import {
  ImageGenerateToolDtoFromJSON,
  ImageGenerateToolDtoFromJSONTyped,
  ImageGenerateToolDtoToJSON,
  ImageGenerateToolDtoToJSONTyped,
} from './ImageGenerateToolDto';
import type { ToolOptionsDto } from './ToolOptionsDto';
import {
  ToolOptionsDtoFromJSON,
  ToolOptionsDtoFromJSONTyped,
  ToolOptionsDtoToJSON,
  ToolOptionsDtoToJSONTyped,
} from './ToolOptionsDto';

/**
 *
 * @export
 * @interface UseToolsDto
 */
export interface UseToolsDto {
  /**
   * Image generation tool options
   * @type {ImageGenerateToolDto}
   * @memberof UseToolsDto
   */
  imageGenerate?: ImageGenerateToolDto;
  /**
   * Diagram generation tool options
   * @type {ToolOptionsDto}
   * @memberof UseToolsDto
   */
  diagramGenerate?: ToolOptionsDto;
  /**
   * Google search tool options
   * @type {ToolOptionsDto}
   * @memberof UseToolsDto
   */
  googleSearch?: ToolOptionsDto;
  /**
   * Library search tool options
   * @type {ToolOptionsDto}
   * @memberof UseToolsDto
   */
  librarySearch?: ToolOptionsDto;
  /**
   * Edit thought tool options
   * @type {ToolOptionsDto}
   * @memberof UseToolsDto
   */
  editThought?: ToolOptionsDto;
  /**
   * Audio generation tool options
   * @type {ToolOptionsDto}
   * @memberof UseToolsDto
   */
  audioGenerate?: ToolOptionsDto;
  /**
   * Board search tool options
   * @type {ToolOptionsDto}
   * @memberof UseToolsDto
   */
  boardSearch?: ToolOptionsDto;
}

/**
 * Check if a given object implements the UseToolsDto interface.
 */
export function instanceOfUseToolsDto(value: object): value is UseToolsDto {
  return true;
}

export function UseToolsDtoFromJSON(json: any): UseToolsDto {
  return UseToolsDtoFromJSONTyped(json, false);
}

export function UseToolsDtoFromJSONTyped(json: any, _ignoreDiscriminator: boolean): UseToolsDto {
  if (json == null) {
    return json;
  }
  return {
    imageGenerate:
      json.imageGenerate == null ? undefined : ImageGenerateToolDtoFromJSON(json.imageGenerate),
    diagramGenerate:
      json.diagramGenerate == null ? undefined : ToolOptionsDtoFromJSON(json.diagramGenerate),
    googleSearch: json.googleSearch == null ? undefined : ToolOptionsDtoFromJSON(json.googleSearch),
    librarySearch:
      json.librarySearch == null ? undefined : ToolOptionsDtoFromJSON(json.librarySearch),
    editThought: json.editThought == null ? undefined : ToolOptionsDtoFromJSON(json.editThought),
    audioGenerate:
      json.audioGenerate == null ? undefined : ToolOptionsDtoFromJSON(json.audioGenerate),
    boardSearch: json.boardSearch == null ? undefined : ToolOptionsDtoFromJSON(json.boardSearch),
  };
}

export function UseToolsDtoToJSON(json: any): UseToolsDto {
  return UseToolsDtoToJSONTyped(json, false);
}

export function UseToolsDtoToJSONTyped(
  value?: UseToolsDto | null,
  _ignoreDiscriminator: boolean = false,
): any {
  if (value == null) {
    return value;
  }

  return {
    imageGenerate: ImageGenerateToolDtoToJSON(value.imageGenerate),
    diagramGenerate: ToolOptionsDtoToJSON(value.diagramGenerate),
    googleSearch: ToolOptionsDtoToJSON(value.googleSearch),
    librarySearch: ToolOptionsDtoToJSON(value.librarySearch),
    editThought: ToolOptionsDtoToJSON(value.editThought),
    audioGenerate: ToolOptionsDtoToJSON(value.audioGenerate),
    boardSearch: ToolOptionsDtoToJSON(value.boardSearch),
  };
}

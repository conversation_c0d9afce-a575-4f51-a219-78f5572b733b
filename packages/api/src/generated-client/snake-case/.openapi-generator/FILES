.openapi-generator-ignore
apis/AIApi.ts
apis/AppApi.ts
apis/AuthApi.ts
apis/BoardApi.ts
apis/BoardGroupApi.ts
apis/BoardItemApi.ts
apis/ChatShortcutApi.ts
apis/ChatV1Api.ts
apis/ChatV2Api.ts
apis/CreditManagementAPIApi.ts
apis/DiffApi.ts
apis/EntityApi.ts
apis/FavoritesApi.ts
apis/FileApi.ts
apis/HealthApi.ts
apis/MaterialApi.ts
apis/NoteApi.ts
apis/PlaylistItemApi.ts
apis/PositionApi.ts
apis/SearchApi.ts
apis/ShareApi.ts
apis/ShortLinkApi.ts
apis/SnipApi.ts
apis/StreamingTestApi.ts
apis/SubscriptionApi.ts
apis/TextApi.ts
apis/ThoughtApi.ts
apis/ThoughtVersionApi.ts
apis/UsageRecordApi.ts
apis/UserApi.ts
apis/UserPreferenceApi.ts
apis/WebhookApi.ts
apis/index.ts
index.ts
models/AIControllerCreateLLMFeedback200Response.ts
models/AILanguageEnum.ts
models/AddPunctuationDto.ts
models/AdjustLengthDirectionEnum.ts
models/ArticleDto.ts
models/AtReferenceDto.ts
models/BlockContentDto.ts
models/BlockDto.ts
models/BoardDto.ts
models/BoardGroupControllerDeleteBoardGroup200Response.ts
models/BoardGroupDto.ts
models/BoardGroupIconDto.ts
models/BoardIconDto.ts
models/BoardIdDto.ts
models/BoardItemDto.ts
models/BoardItemDtoEntity.ts
models/BoardWithCountDto.ts
models/BoardWithItemsDto.ts
models/BoardWithSomeBoardItemsDto.ts
models/ChatDetailListV1Dto.ts
models/ChatDetailV1Dto.ts
models/ChatDetailV2Dto.ts
models/ChatIdDto.ts
models/ChatModeEnum.ts
models/ChatOriginDto.ts
models/ChatOriginTypeEnum.ts
models/ChatShortcutControllerDeleteShortcut200Response.ts
models/ChatV1ControllerCreateChat200Response.ts
models/ChatV1ControllerListChatModels200ResponseValue.ts
models/ChatV2ControllerCreateChat200Response.ts
models/ChatV2Dto.ts
models/CompletionBlockDto.ts
models/CompletionBlockStatusEnum.ts
models/CompletionBlockTypeEnum.ts
models/CompletionChatDto.ts
models/CompletionMessageDto.ts
models/CompletionStreamAppendJsonChunkDto.ts
models/CompletionStreamAppendStringChunkDto.ts
models/CompletionStreamEventDto.ts
models/CompletionStreamEventDtoData.ts
models/CompletionStreamInsertChunkDto.ts
models/CompletionStreamInsertChunkDtoData.ts
models/CompletionStreamModeEnum.ts
models/CompletionStreamReplaceChunkDto.ts
models/ContentFormatEnum.ts
models/CreateArticleDto.ts
models/CreateBoardDto.ts
models/CreateBoardGroupDto.ts
models/CreateChatV1Dto.ts
models/CreateChatV2Dto.ts
models/CreateEmptyChatDto.ts
models/CreateImageDto.ts
models/CreateImageDtoFile.ts
models/CreateLLMFeedbackDto.ts
models/CreateNoteDto.ts
models/CreateNoteSourceDto.ts
models/CreateOfficeDto.ts
models/CreateOtherWebpageDto.ts
models/CreatePdfByUrlDto.ts
models/CreatePdfDto.ts
models/CreatePlaylistItemDto.ts
models/CreateShortcutV2Dto.ts
models/CreateSnippetDto.ts
models/CreateStorageUsageRecordFromEditorRequestDto.ts
models/CreateSubscriptionDto.ts
models/CreateTextFileDto.ts
models/CreateThoughtDto.ts
models/CreateThoughtVersionDto.ts
models/CreateTingwuTaskDto.ts
models/CreateUploadFileDto.ts
models/CreateVideoDto.ts
models/CreateVoiceDto.ts
models/CreditAccountDto.ts
models/DeleteBoardDto.ts
models/DeleteBoardGroupDto.ts
models/DeleteFormattedSubtitlesDto.ts
models/DeleteManyNotesDto.ts
models/DeleteNoteDto.ts
models/DeletePlaylistItemDto.ts
models/DeleteShortcutV2Dto.ts
models/DeleteSnipDto.ts
models/DeleteThoughtDto.ts
models/DeleteThoughtVersionDto.ts
models/DetectSpeakersDto.ts
models/DetectSpeakersResponseDto.ts
models/DetectSpeakersResponseDtoSpeakersInner.ts
models/DiffNodeDto.ts
models/DiffReviewActionType.ts
models/DisplayLanguageEnum.ts
models/EditCommandDto.ts
models/EditCommandTypeEnum.ts
models/EditImageDto.ts
models/EntityForAtReferenceDto.ts
models/EntitySourceDto.ts
models/EntityType.ts
models/Environment.ts
models/ExplainRequestDto.ts
models/ExtractTextRequestDto.ts
models/ExtractTextResponseDto.ts
models/FavoriteDto.ts
models/FavoriteEntityDto.ts
models/FavoriteEntityType.ts
models/FileMetaDto.ts
models/FormDataDto.ts
models/FormatContentDto.ts
models/FormattedSubtitleContentDto.ts
models/GenSignedPutUrlIfNotExistRequestDto.ts
models/GenTitleDto.ts
models/GenerateImageInfoDto.ts
models/GenerateOptionsDto.ts
models/GenerateOverviewDto.ts
models/GenerateTranscriptDto.ts
models/GetBoardDto.ts
models/GetEntitiesDto.ts
models/GetSharedEntityResponseDto.ts
models/GetSnipResponseDtoClass.ts
models/GetThoughtDto.ts
models/GetTingwuTaskInfoDto.ts
models/GetUserDto.ts
models/HTMLSelectionDto.ts
models/HealthControllerConfig200Response.ts
models/HealthControllerRefreshConfig200Response.ts
models/HtmlSelectionDto0.ts
models/HybridSearchChunksDto.ts
models/HybridSearchDto.ts
models/ImageDto.ts
models/ImageGenerateToolDto.ts
models/InitCurrentUserDto.ts
models/InitUsersDto.ts
models/LLMs.ts
models/LanguageEnum.ts
models/ListBoardsDto.ts
models/ListChatHistoryV2Dto.ts
models/ListChatHistoryV2ResponseDto.ts
models/ListChatHistoryV2ResponseDtoPaging.ts
models/ListEntitiesForAtReferenceResponseDto.ts
models/ListFormattedSubtitlesDto.ts
models/ListMaterialsResponseDto.ts
models/ListNotesDto.ts
models/ListNotesSourceDto.ts
models/ListPlaylistItemsDto.ts
models/ListRecentMaterialsDto.ts
models/ListRecentMaterialsResponseDto.ts
models/ListShortcutsV2ResponseDto.ts
models/ListShortcutsV2ResponseDtoPaging.ts
models/ListSimpleBoardsByEntityIdsDto.ts
models/ListSnipsByUrlDto.ts
models/ListThoughtVersionsDto.ts
models/ListUnusedMaterialsForMobileResponseDto.ts
models/ListUnusedMaterialsResponseDto.ts
models/ListWebpagesByUrlsDto.ts
models/ListWebpagesInBoardByUrlsDto.ts
models/LogOutResponseDto.ts
models/MessageAtReferenceTypeEnum.ts
models/MessageModeEnum.ts
models/MessageRoleEnum.ts
models/MessageStatusEnum.ts
models/MoveBoardItemToBoardGroupDto.ts
models/MoveBoardItemToRootDto.ts
models/MoveEntityToUnsortedDto.ts
models/MoveFavoriteDto.ts
models/MoveShortcutV2Dto.ts
models/NoteContentDto.ts
models/NoteDto.ts
models/NoteQuoteDto.ts
models/NoteSourceDto.ts
models/NoteSourceEntityDto.ts
models/NoteSourceEntityType.ts
models/OfficeDto.ts
models/OfficeFileDto.ts
models/OtherWebpageDto.ts
models/OverviewRequestDto.ts
models/PatchNoteDto.ts
models/PatchNoteSourceDto.ts
models/PatchShortcutV2Dto.ts
models/PatchThoughtDto.ts
models/PatchVideoTranscriptDto.ts
models/PatchVoiceTranscriptDto.ts
models/PdfDto.ts
models/PdfFileDto.ts
models/PlaylistItemDto.ts
models/PlaylistItemSourceEntityType.ts
models/PlaylistItemStatus.ts
models/ProcessStatusEnum.ts
models/PublishMaterialDto.ts
models/PublishMaterialResponseDto.ts
models/PublishThoughtDto.ts
models/QueryChatDetailsByOriginDto.ts
models/ReaderHTMLContentDto.ts
models/RecentMaterialItemDto.ts
models/RedirectResponseDto.ts
models/RegenerateMessageV1Dto.ts
models/RegenerateMessageV2Dto.ts
models/ReportDiffReviewEventDto.ts
models/ResolveVersionDto.ts
models/RichtextContentDto.ts
models/SaveMessagesDto.ts
models/SaveSharedMaterialDto.ts
models/SaveSharedMaterialResponseDto.ts
models/SearchAdvancedDto.ts
models/SearchChunkDto.ts
models/SearchChunksResponseDto.ts
models/SearchResultDto.ts
models/SelfDescByFeedsRequestDto.ts
models/SemanticSearchChunksDto.ts
models/SendMessageV1Dto.ts
models/SendMessageV2Dto.ts
models/ShortLinkDto.ts
models/ShortcutDto.ts
models/ShortcutV2Dto.ts
models/SignInWithOAuthDto.ts
models/SignInWithOAuthProvider.ts
models/SignInWithOTPDto.ts
models/SimpleBoardWithEntityIdsDto.ts
models/SnipControllerListSnips200ResponseInner.ts
models/SnipFeatureEnum.ts
models/SnipFrom.ts
models/SnipStatus.ts
models/SnipType.ts
models/SnippetDto.ts
models/SpaceDto.ts
models/SpeakerUpdateDto.ts
models/StopTingwuTaskDto.ts
models/StorageFileDto.ts
models/StreamChunkedDataDto.ts
models/StreamDataDto.ts
models/StreamDataTypeEnum.ts
models/StreamErrorDto.ts
models/StreamMessageEventDto.ts
models/StreamMessageEventDtoData.ts
models/StreamStatusUpdateDto.ts
models/StreamTextDto.ts
models/StreamTextStartDto.ts
models/StreamTextStopDto.ts
models/StreamTraceDto.ts
models/SubscriptionDto.ts
models/SubtitleSegmentDto.ts
models/TextDto.ts
models/ThoughtContentDto.ts
models/ThoughtDto.ts
models/ThoughtDtoContent.ts
models/ThoughtVersionContentDto.ts
models/ThoughtVersionContentResponseDto.ts
models/ThoughtVersionDto.ts
models/TitleType.ts
models/ToolOptionsDto.ts
models/TranslateModeEnum.ts
models/TryCreateSnipByUrlDto.ts
models/UnknownWebpageDto.ts
models/UnpublishMaterialDto.ts
models/UnpublishThoughtDto.ts
models/UntransferredFileMetaDto.ts
models/UpdateBoardDto.ts
models/UpdateBoardGroupDto.ts
models/UpdateCompletionBlockDto.ts
models/UpdateImageDto.ts
models/UpdateLLMFeedbackDto.ts
models/UpdatePlaylistItemTitleDto.ts
models/UpdateSnipPlayUrlDto.ts
models/UpdateSnipTitleDto.ts
models/UpdateSubscriptionDto.ts
models/UpdateTranscriptSpeakerDto.ts
models/UpdateTranscriptSpeakerResponseDto.ts
models/UpdateUserAvatarDto.ts
models/UpdateUserNameDto.ts
models/UpdateUserPreferenceDto.ts
models/UpdateUserTimeZoneIfNotSetDto.ts
models/UploadSvgRequestDto.ts
models/UploadSvgResponseDto.ts
models/UseToolsDto.ts
models/UserDto.ts
models/UserPreferenceDto.ts
models/UserWithPreferenceSpaceDto.ts
models/ValidateOTPTokenDto.ts
models/VerifyTransactionDto.ts
models/VideoDto.ts
models/Visibility.ts
models/VoiceDto.ts
models/VoiceDtoFile.ts
models/VoiceFileDto.ts
models/WebhookFetchedRequestDto.ts
models/WebhookFetchedRequestDtoData.ts
models/WebhookFetchedResponseDto.ts
models/WebhookImagesTransferedRequestDto.ts
models/WebhookImagesTransferedRequestDtoData.ts
models/WebhookImagesTransferedResponseDto.ts
models/WebhookOfficeParsedResponseDto.ts
models/WebhookPdfParsedRequestDto.ts
models/WebhookPdfParsedRequestDtoData.ts
models/WebhookPdfParsedResponseDto.ts
models/WebhookResendRequestDto.ts
models/WebhookSubtitleTranscribedRequestDto.ts
models/WebhookSubtitleTranscribedRequestDtoData.ts
models/WebhookSubtitleTranscribedResponseDto.ts
models/WebpageMetaDto.ts
models/WebpageSiteDto.ts
models/YougetArticleFetchedEventDto.ts
models/YougetErrorEventDto.ts
models/YougetOfficeParsedEventDto.ts
models/YougetOtherWebpageFetchedEventDto.ts
models/YougetPdfParsedEventDto.ts
models/YougetSubtitleTranscribedEventDto.ts
models/YougetTransferedFileMetaDto.ts
models/YougetVideoFetchedEventDto.ts
models/YougetVoiceFetchedEventDto.ts
models/index.ts
runtime.ts

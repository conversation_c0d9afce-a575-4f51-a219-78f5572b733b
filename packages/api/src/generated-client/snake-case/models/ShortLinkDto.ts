/* tslint:disable */
/* eslint-disable */
/**
 * <PERSON><PERSON><PERSON> (Snake Case)
 *   This documentation uses snake_case naming convention for compatibility with legacy clients.
 *
 * The version of the OpenAPI document: 1.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../../../runtime';
import type { EntityType } from './EntityType';
import {
  EntityTypeFromJSON,
  EntityTypeFromJSONTyped,
  EntityTypeToJSON,
  EntityTypeToJSONTyped,
} from './EntityType';

/**
 *
 * @export
 * @interface ShortLinkDto
 */
export interface ShortLinkDto {
  /**
   * 短链接ID
   * @type {string}
   * @memberof ShortLinkDto
   */
  id: string;
  /**
   * 短链接标识符
   * @type {string}
   * @memberof ShortLinkDto
   */
  short_id: string;
  /**
   * 短链接是否激活
   * @type {boolean}
   * @memberof ShortLinkDto
   */
  active: boolean;
  /**
   * 创建时间
   * @type {Date}
   * @memberof ShortLinkDto
   */
  created_at: Date;
  /**
   * 更新时间
   * @type {Date}
   * @memberof ShortLinkDto
   */
  updated_at: Date;
  /**
   * 实体类型
   * @type {EntityType}
   * @memberof ShortLinkDto
   */
  entity_type: EntityType;
  /**
   * 实体ID
   * @type {string}
   * @memberof ShortLinkDto
   */
  entity_id: string;
}

/**
 * Check if a given object implements the ShortLinkDto interface.
 */
export function instanceOfShortLinkDto(value: object): value is ShortLinkDto {
  if (!('id' in value) || value.id === undefined) return false;
  if (!('short_id' in value) || value.short_id === undefined) return false;
  if (!('active' in value) || value.active === undefined) return false;
  if (!('created_at' in value) || value.created_at === undefined) return false;
  if (!('updated_at' in value) || value.updated_at === undefined) return false;
  if (!('entity_type' in value) || value.entity_type === undefined) return false;
  if (!('entity_id' in value) || value.entity_id === undefined) return false;
  return true;
}

export function ShortLinkDtoFromJSON(json: any): ShortLinkDto {
  return ShortLinkDtoFromJSONTyped(json, false);
}

export function ShortLinkDtoFromJSONTyped(json: any, _ignoreDiscriminator: boolean): ShortLinkDto {
  if (json == null) {
    return json;
  }
  return {
    id: json.id,
    short_id: json.short_id,
    active: json.active,
    created_at: new Date(json.created_at),
    updated_at: new Date(json.updated_at),
    entity_type: EntityTypeFromJSON(json.entity_type),
    entity_id: json.entity_id,
  };
}

export function ShortLinkDtoToJSON(json: any): ShortLinkDto {
  return ShortLinkDtoToJSONTyped(json, false);
}

export function ShortLinkDtoToJSONTyped(
  value?: ShortLinkDto | null,
  _ignoreDiscriminator: boolean = false,
): any {
  if (value == null) {
    return value;
  }

  return {
    id: value.id,
    short_id: value.short_id,
    active: value.active,
    created_at: value.created_at.toISOString(),
    updated_at: value.updated_at.toISOString(),
    entity_type: EntityTypeToJSON(value.entity_type),
    entity_id: value.entity_id,
  };
}

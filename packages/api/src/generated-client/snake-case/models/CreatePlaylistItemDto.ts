/* tslint:disable */
/* eslint-disable */
/**
 * You<PERSON><PERSON> (Snake Case)
 *   This documentation uses snake_case naming convention for compatibility with legacy clients.
 *
 * The version of the OpenAPI document: 1.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../../../runtime';
import type { PlaylistItemSourceEntityType } from './PlaylistItemSourceEntityType';
import {
  PlaylistItemSourceEntityTypeFromJSON,
  PlaylistItemSourceEntityTypeFromJSONTyped,
  PlaylistItemSourceEntityTypeToJSON,
  PlaylistItemSourceEntityTypeToJSONTyped,
} from './PlaylistItemSourceEntityType';

/**
 *
 * @export
 * @interface CreatePlaylistItemDto
 */
export interface CreatePlaylistItemDto {
  /**
   * 实体类型
   * @type {PlaylistItemSourceEntityType}
   * @memberof CreatePlaylistItemDto
   */
  entity_type: PlaylistItemSourceEntityType;
  /**
   * 实体ID
   * @type {string}
   * @memberof CreatePlaylistItemDto
   */
  entity_id: string;
  /**
   * 创作板ID
   * @type {string}
   * @memberof CreatePlaylistItemDto
   */
  board_id?: string;
  /**
   * AI模型
   * @type {string}
   * @memberof CreatePlaylistItemDto
   */
  model?: string;
  /**
   * 声音类型
   * @type {string}
   * @memberof CreatePlaylistItemDto
   */
  voice?: string;
  /**
   * 情感类型
   * @type {string}
   * @memberof CreatePlaylistItemDto
   */
  emotion?: string;
}

/**
 * Check if a given object implements the CreatePlaylistItemDto interface.
 */
export function instanceOfCreatePlaylistItemDto(value: object): value is CreatePlaylistItemDto {
  if (!('entity_type' in value) || value.entity_type === undefined) return false;
  if (!('entity_id' in value) || value.entity_id === undefined) return false;
  return true;
}

export function CreatePlaylistItemDtoFromJSON(json: any): CreatePlaylistItemDto {
  return CreatePlaylistItemDtoFromJSONTyped(json, false);
}

export function CreatePlaylistItemDtoFromJSONTyped(
  json: any,
  _ignoreDiscriminator: boolean,
): CreatePlaylistItemDto {
  if (json == null) {
    return json;
  }
  return {
    entity_type: PlaylistItemSourceEntityTypeFromJSON(json.entity_type),
    entity_id: json.entity_id,
    board_id: json.board_id == null ? undefined : json.board_id,
    model: json.model == null ? undefined : json.model,
    voice: json.voice == null ? undefined : json.voice,
    emotion: json.emotion == null ? undefined : json.emotion,
  };
}

export function CreatePlaylistItemDtoToJSON(json: any): CreatePlaylistItemDto {
  return CreatePlaylistItemDtoToJSONTyped(json, false);
}

export function CreatePlaylistItemDtoToJSONTyped(
  value?: CreatePlaylistItemDto | null,
  _ignoreDiscriminator: boolean = false,
): any {
  if (value == null) {
    return value;
  }

  return {
    entity_type: PlaylistItemSourceEntityTypeToJSON(value.entity_type),
    entity_id: value.entity_id,
    board_id: value.board_id,
    model: value.model,
    voice: value.voice,
    emotion: value.emotion,
  };
}

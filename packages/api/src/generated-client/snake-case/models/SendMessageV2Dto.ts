/* tslint:disable */
/* eslint-disable */
/**
 * <PERSON><PERSON><PERSON> (Snake Case)
 *   This documentation uses snake_case naming convention for compatibility with legacy clients.
 *
 * The version of the OpenAPI document: 1.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../../../runtime';
import type { AtReferenceDto } from './AtReferenceDto';
import {
  AtReferenceDtoFromJSON,
  AtReferenceDtoFromJSONTyped,
  AtReferenceDtoToJSON,
  AtReferenceDtoToJSONTyped,
} from './AtReferenceDto';
import type { ChatOriginDto } from './ChatOriginDto';
import {
  ChatOriginDtoFromJSON,
  ChatOriginDtoFromJSONTyped,
  ChatOriginDtoToJSON,
  ChatOriginDtoToJSONTyped,
} from './ChatOriginDto';
import type { EditCommandDto } from './EditCommandDto';
import {
  EditCommandDtoFromJSON,
  EditCommandDtoFromJSONTyped,
  EditCommandDtoToJSON,
  EditCommandDtoToJSONTyped,
} from './EditCommandDto';
import type { LLMs } from './LLMs';
import { LLMsFromJSON, LLMsFromJSONTyped, LLMsToJSON, LLMsToJSONTyped } from './LLMs';
import type { MessageModeEnum } from './MessageModeEnum';
import {
  MessageModeEnumFromJSON,
  MessageModeEnumFromJSONTyped,
  MessageModeEnumToJSON,
  MessageModeEnumToJSONTyped,
} from './MessageModeEnum';
import type { ShortcutDto } from './ShortcutDto';
import {
  ShortcutDtoFromJSON,
  ShortcutDtoFromJSONTyped,
  ShortcutDtoToJSON,
  ShortcutDtoToJSONTyped,
} from './ShortcutDto';
import type { UseToolsDto } from './UseToolsDto';
import {
  UseToolsDtoFromJSON,
  UseToolsDtoFromJSONTyped,
  UseToolsDtoToJSON,
  UseToolsDtoToJSONTyped,
} from './UseToolsDto';

/**
 *
 * @export
 * @interface SendMessageV2Dto
 */
export interface SendMessageV2Dto {
  /**
   * Message content
   * @type {string}
   * @memberof SendMessageV2Dto
   */
  message: string;
  /**
   * Board Id
   * @type {string}
   * @memberof SendMessageV2Dto
   */
  board_id?: string;
  /**
   * Selected text context
   * @type {string}
   * @memberof SendMessageV2Dto
   */
  selection?: string;
  /**
   * Referenced entities
   * @type {Array<AtReferenceDto>}
   * @memberof SendMessageV2Dto
   */
  at_references?: Array<AtReferenceDto>;
  /**
   * AI model to use
   * @type {LLMs}
   * @memberof SendMessageV2Dto
   */
  chat_model?: LLMs;
  /**
   * Tools configuration
   * @type {Array<UseToolsDto>}
   * @memberof SendMessageV2Dto
   */
  tools?: Array<UseToolsDto>;
  /**
   * Shortcut information
   * @type {Array<ShortcutDto>}
   * @memberof SendMessageV2Dto
   */
  shortcut?: Array<ShortcutDto>;
  /**
   * Message mode
   * @type {MessageModeEnum}
   * @memberof SendMessageV2Dto
   */
  message_mode?: MessageModeEnum;
  /**
   * Edit command
   * @type {Array<EditCommandDto>}
   * @memberof SendMessageV2Dto
   */
  command?: Array<EditCommandDto>;
  /**
   * Chat origin context
   * @type {ChatOriginDto}
   * @memberof SendMessageV2Dto
   */
  origin?: ChatOriginDto;
  /**
   * Chat ID to send message to
   * @type {string}
   * @memberof SendMessageV2Dto
   */
  chat_id: string;
}

/**
 * Check if a given object implements the SendMessageV2Dto interface.
 */
export function instanceOfSendMessageV2Dto(value: object): value is SendMessageV2Dto {
  if (!('message' in value) || value.message === undefined) return false;
  if (!('chat_id' in value) || value.chat_id === undefined) return false;
  return true;
}

export function SendMessageV2DtoFromJSON(json: any): SendMessageV2Dto {
  return SendMessageV2DtoFromJSONTyped(json, false);
}

export function SendMessageV2DtoFromJSONTyped(
  json: any,
  _ignoreDiscriminator: boolean,
): SendMessageV2Dto {
  if (json == null) {
    return json;
  }
  return {
    message: json.message,
    board_id: json.board_id == null ? undefined : json.board_id,
    selection: json.selection == null ? undefined : json.selection,
    at_references:
      json.at_references == null
        ? undefined
        : (json.at_references as Array<any>).map(AtReferenceDtoFromJSON),
    chat_model: json.chat_model == null ? undefined : LLMsFromJSON(json.chat_model),
    tools: json.tools == null ? undefined : (json.tools as Array<any>).map(UseToolsDtoFromJSON),
    shortcut:
      json.shortcut == null ? undefined : (json.shortcut as Array<any>).map(ShortcutDtoFromJSON),
    message_mode:
      json.message_mode == null ? undefined : MessageModeEnumFromJSON(json.message_mode),
    command:
      json.command == null ? undefined : (json.command as Array<any>).map(EditCommandDtoFromJSON),
    origin: json.origin == null ? undefined : ChatOriginDtoFromJSON(json.origin),
    chat_id: json.chat_id,
  };
}

export function SendMessageV2DtoToJSON(json: any): SendMessageV2Dto {
  return SendMessageV2DtoToJSONTyped(json, false);
}

export function SendMessageV2DtoToJSONTyped(
  value?: SendMessageV2Dto | null,
  _ignoreDiscriminator: boolean = false,
): any {
  if (value == null) {
    return value;
  }

  return {
    message: value.message,
    board_id: value.board_id,
    selection: value.selection,
    at_references:
      value.at_references == null
        ? undefined
        : (value.at_references as Array<any>).map(AtReferenceDtoToJSON),
    chat_model: LLMsToJSON(value.chat_model),
    tools: value.tools == null ? undefined : (value.tools as Array<any>).map(UseToolsDtoToJSON),
    shortcut:
      value.shortcut == null ? undefined : (value.shortcut as Array<any>).map(ShortcutDtoToJSON),
    message_mode: MessageModeEnumToJSON(value.message_mode),
    command:
      value.command == null ? undefined : (value.command as Array<any>).map(EditCommandDtoToJSON),
    origin: ChatOriginDtoToJSON(value.origin),
    chat_id: value.chat_id,
  };
}

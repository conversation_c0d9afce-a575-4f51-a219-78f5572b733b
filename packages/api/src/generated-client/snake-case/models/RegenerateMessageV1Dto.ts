/* tslint:disable */
/* eslint-disable */
/**
 * <PERSON><PERSON><PERSON> (Snake Case)
 *   This documentation uses snake_case naming convention for compatibility with legacy clients.
 *
 * The version of the OpenAPI document: 1.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../../../runtime';
import type { LLMs } from './LLMs';
import { LLMsFromJSON, LLMsFromJSONTyped, LLMsToJSON, LLMsToJSONTyped } from './LLMs';

/**
 *
 * @export
 * @interface RegenerateMessageV1Dto
 */
export interface RegenerateMessageV1Dto {
  /**
   * Chat ID containing the message to regenerate
   * @type {string}
   * @memberof RegenerateMessageV1Dto
   */
  chat_id: string;
  /**
   * User message ID to regenerate from
   * @type {string}
   * @memberof RegenerateMessageV1Dto
   */
  user_message_id: string;
  /**
   * AI model to use for regeneration
   * @type {LLMs}
   * @memberof RegenerateMessageV1Dto
   */
  chat_model?: LLMs;
  /**
   * Enable web search for regeneration
   * @type {boolean}
   * @memberof RegenerateMessageV1Dto
   */
  web_search?: boolean;
}

/**
 * Check if a given object implements the RegenerateMessageV1Dto interface.
 */
export function instanceOfRegenerateMessageV1Dto(value: object): value is RegenerateMessageV1Dto {
  if (!('chat_id' in value) || value.chat_id === undefined) return false;
  if (!('user_message_id' in value) || value.user_message_id === undefined) return false;
  return true;
}

export function RegenerateMessageV1DtoFromJSON(json: any): RegenerateMessageV1Dto {
  return RegenerateMessageV1DtoFromJSONTyped(json, false);
}

export function RegenerateMessageV1DtoFromJSONTyped(
  json: any,
  _ignoreDiscriminator: boolean,
): RegenerateMessageV1Dto {
  if (json == null) {
    return json;
  }
  return {
    chat_id: json.chat_id,
    user_message_id: json.user_message_id,
    chat_model: json.chat_model == null ? undefined : LLMsFromJSON(json.chat_model),
    web_search: json.web_search == null ? undefined : json.web_search,
  };
}

export function RegenerateMessageV1DtoToJSON(json: any): RegenerateMessageV1Dto {
  return RegenerateMessageV1DtoToJSONTyped(json, false);
}

export function RegenerateMessageV1DtoToJSONTyped(
  value?: RegenerateMessageV1Dto | null,
  _ignoreDiscriminator: boolean = false,
): any {
  if (value == null) {
    return value;
  }

  return {
    chat_id: value.chat_id,
    user_message_id: value.user_message_id,
    chat_model: LLMsToJSON(value.chat_model),
    web_search: value.web_search,
  };
}

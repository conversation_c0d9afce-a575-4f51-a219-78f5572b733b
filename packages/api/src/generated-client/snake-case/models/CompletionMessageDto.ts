/* tslint:disable */
/* eslint-disable */
/**
 * <PERSON><PERSON><PERSON> (Snake Case)
 *   This documentation uses snake_case naming convention for compatibility with legacy clients.
 *
 * The version of the OpenAPI document: 1.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../../../runtime';
import type { ChatOriginTypeEnum } from './ChatOriginTypeEnum';
import {
  ChatOriginTypeEnumFromJSON,
  ChatOriginTypeEnumFromJSONTyped,
  ChatOriginTypeEnumToJSON,
  ChatOriginTypeEnumToJSONTyped,
} from './ChatOriginTypeEnum';
import type { EditCommandDto } from './EditCommandDto';
import {
  EditCommandDtoFromJSON,
  EditCommandDtoFromJSONTyped,
  EditCommandDtoToJSON,
  EditCommandDtoToJSONTyped,
} from './EditCommandDto';
import type { MessageModeEnum } from './MessageModeEnum';
import {
  MessageModeEnumFromJSON,
  MessageModeEnumFromJSONTyped,
  MessageModeEnumToJSON,
  MessageModeEnumToJSONTyped,
} from './MessageModeEnum';
import type { MessageRoleEnum } from './MessageRoleEnum';
import {
  MessageRoleEnumFromJSON,
  MessageRoleEnumFromJSONTyped,
  MessageRoleEnumToJSON,
  MessageRoleEnumToJSONTyped,
} from './MessageRoleEnum';
import type { MessageStatusEnum } from './MessageStatusEnum';
import {
  MessageStatusEnumFromJSON,
  MessageStatusEnumFromJSONTyped,
  MessageStatusEnumToJSON,
  MessageStatusEnumToJSONTyped,
} from './MessageStatusEnum';
import type { UseToolsDto } from './UseToolsDto';
import {
  UseToolsDtoFromJSON,
  UseToolsDtoFromJSONTyped,
  UseToolsDtoToJSON,
  UseToolsDtoToJSONTyped,
} from './UseToolsDto';

/**
 *
 * @export
 * @interface CompletionMessageDto
 */
export interface CompletionMessageDto {
  /**
   * The ID of the message
   * @type {string}
   * @memberof CompletionMessageDto
   */
  id: string;
  /**
   * The ID of the chat
   * @type {string}
   * @memberof CompletionMessageDto
   */
  chat_id: string;
  /**
   * The creation time of the chat
   * @type {string}
   * @memberof CompletionMessageDto
   */
  created_at: string;
  /**
   * The update time of the chat
   * @type {string}
   * @memberof CompletionMessageDto
   */
  updated_at: string;
  /**
   * The status of the message
   * @type {MessageRoleEnum}
   * @memberof CompletionMessageDto
   */
  role: MessageRoleEnum;
  /**
   * The status of the message
   * @type {MessageStatusEnum}
   * @memberof CompletionMessageDto
   */
  status: MessageStatusEnum;
  /**
   * The content of the message
   * @type {string}
   * @memberof CompletionMessageDto
   */
  content?: string;
  /**
   * The origin of the message
   * @type {ChatOriginTypeEnum}
   * @memberof CompletionMessageDto
   */
  origin?: ChatOriginTypeEnum;
  /**
   * The selection of the message
   * @type {string}
   * @memberof CompletionMessageDto
   */
  selection?: string;
  /**
   * The at references of the message
   * @type {Array<string>}
   * @memberof CompletionMessageDto
   */
  at_references?: Array<string>;
  /**
   * The board ID of the message
   * @type {string}
   * @memberof CompletionMessageDto
   */
  board_id?: string;
  /**
   * Tools configuration
   * @type {Array<UseToolsDto>}
   * @memberof CompletionMessageDto
   */
  tools?: Array<UseToolsDto>;
  /**
   * Edit command
   * @type {EditCommandDto}
   * @memberof CompletionMessageDto
   */
  command?: EditCommandDto;
  /**
   * Message mode
   * @type {MessageModeEnum}
   * @memberof CompletionMessageDto
   */
  mode: MessageModeEnum;
  /**
   * Shortcut information
   * @type {Array<UseToolsDto>}
   * @memberof CompletionMessageDto
   */
  shortcut?: Array<UseToolsDto>;
}

/**
 * Check if a given object implements the CompletionMessageDto interface.
 */
export function instanceOfCompletionMessageDto(value: object): value is CompletionMessageDto {
  if (!('id' in value) || value.id === undefined) return false;
  if (!('chat_id' in value) || value.chat_id === undefined) return false;
  if (!('created_at' in value) || value.created_at === undefined) return false;
  if (!('updated_at' in value) || value.updated_at === undefined) return false;
  if (!('role' in value) || value.role === undefined) return false;
  if (!('status' in value) || value.status === undefined) return false;
  if (!('mode' in value) || value.mode === undefined) return false;
  return true;
}

export function CompletionMessageDtoFromJSON(json: any): CompletionMessageDto {
  return CompletionMessageDtoFromJSONTyped(json, false);
}

export function CompletionMessageDtoFromJSONTyped(
  json: any,
  _ignoreDiscriminator: boolean,
): CompletionMessageDto {
  if (json == null) {
    return json;
  }
  return {
    id: json.id,
    chat_id: json.chat_id,
    created_at: json.created_at,
    updated_at: json.updated_at,
    role: MessageRoleEnumFromJSON(json.role),
    status: MessageStatusEnumFromJSON(json.status),
    content: json.content == null ? undefined : json.content,
    origin: json.origin == null ? undefined : ChatOriginTypeEnumFromJSON(json.origin),
    selection: json.selection == null ? undefined : json.selection,
    at_references: json.at_references == null ? undefined : json.at_references,
    board_id: json.board_id == null ? undefined : json.board_id,
    tools: json.tools == null ? undefined : (json.tools as Array<any>).map(UseToolsDtoFromJSON),
    command: json.command == null ? undefined : EditCommandDtoFromJSON(json.command),
    mode: MessageModeEnumFromJSON(json.mode),
    shortcut:
      json.shortcut == null ? undefined : (json.shortcut as Array<any>).map(UseToolsDtoFromJSON),
  };
}

export function CompletionMessageDtoToJSON(json: any): CompletionMessageDto {
  return CompletionMessageDtoToJSONTyped(json, false);
}

export function CompletionMessageDtoToJSONTyped(
  value?: CompletionMessageDto | null,
  _ignoreDiscriminator: boolean = false,
): any {
  if (value == null) {
    return value;
  }

  return {
    id: value.id,
    chat_id: value.chat_id,
    created_at: value.created_at,
    updated_at: value.updated_at,
    role: MessageRoleEnumToJSON(value.role),
    status: MessageStatusEnumToJSON(value.status),
    content: value.content,
    origin: ChatOriginTypeEnumToJSON(value.origin),
    selection: value.selection,
    at_references: value.at_references,
    board_id: value.board_id,
    tools: value.tools == null ? undefined : (value.tools as Array<any>).map(UseToolsDtoToJSON),
    command: EditCommandDtoToJSON(value.command),
    mode: MessageModeEnumToJSON(value.mode),
    shortcut:
      value.shortcut == null ? undefined : (value.shortcut as Array<any>).map(UseToolsDtoToJSON),
  };
}

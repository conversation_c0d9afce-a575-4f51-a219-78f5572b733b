/* tslint:disable */
/* eslint-disable */
/**
 * <PERSON><PERSON><PERSON> (Snake Case)
 *   This documentation uses snake_case naming convention for compatibility with legacy clients.
 *
 * The version of the OpenAPI document: 1.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../../../runtime';
import type { PlaylistItemSourceEntityType } from './PlaylistItemSourceEntityType';
import {
  PlaylistItemSourceEntityTypeFromJSON,
  PlaylistItemSourceEntityTypeFromJSONTyped,
  PlaylistItemSourceEntityTypeToJSON,
  PlaylistItemSourceEntityTypeToJSONTyped,
} from './PlaylistItemSourceEntityType';
import type { PlaylistItemStatus } from './PlaylistItemStatus';
import {
  PlaylistItemStatusFromJSON,
  PlaylistItemStatusFromJSONTyped,
  PlaylistItemStatusToJSON,
  PlaylistItemStatusToJSONTyped,
} from './PlaylistItemStatus';

/**
 *
 * @export
 * @interface PlaylistItemDto
 */
export interface PlaylistItemDto {
  /**
   * 播放列表项ID
   * @type {string}
   * @memberof PlaylistItemDto
   */
  id: string;
  /**
   * 创建者ID
   * @type {string}
   * @memberof PlaylistItemDto
   */
  creator_id: string;
  /**
   * 空间ID
   * @type {string}
   * @memberof PlaylistItemDto
   */
  space_id: string;
  /**
   * 创作板ID
   * @type {string}
   * @memberof PlaylistItemDto
   */
  board_id?: string;
  /**
   * 实体类型
   * @type {PlaylistItemSourceEntityType}
   * @memberof PlaylistItemDto
   */
  entity_type: PlaylistItemSourceEntityType;
  /**
   * 实体ID
   * @type {string}
   * @memberof PlaylistItemDto
   */
  entity_id: string;
  /**
   * 标题
   * @type {string}
   * @memberof PlaylistItemDto
   */
  title: string;
  /**
   * 播放地址
   * @type {string}
   * @memberof PlaylistItemDto
   */
  play_url: string;
  /**
   * 音频时长（秒）
   * @type {number}
   * @memberof PlaylistItemDto
   */
  duration: number;
  /**
   * 状态
   * @type {PlaylistItemStatus}
   * @memberof PlaylistItemDto
   */
  status: PlaylistItemStatus;
  /**
   * 专辑封面URL
   * @type {string}
   * @memberof PlaylistItemDto
   */
  album_cover_url?: string;
  /**
   * 转录文本
   * @type {string}
   * @memberof PlaylistItemDto
   */
  transcript?: string;
  /**
   * 排序
   * @type {string}
   * @memberof PlaylistItemDto
   */
  rank?: string;
  /**
   * 播放进度
   * @type {number}
   * @memberof PlaylistItemDto
   */
  playback_progress?: number;
  /**
   * 创建时间
   * @type {Date}
   * @memberof PlaylistItemDto
   */
  created_at: Date;
  /**
   * 更新时间
   * @type {Date}
   * @memberof PlaylistItemDto
   */
  updated_at: Date;
}

/**
 * Check if a given object implements the PlaylistItemDto interface.
 */
export function instanceOfPlaylistItemDto(value: object): value is PlaylistItemDto {
  if (!('id' in value) || value.id === undefined) return false;
  if (!('creator_id' in value) || value.creator_id === undefined) return false;
  if (!('space_id' in value) || value.space_id === undefined) return false;
  if (!('entity_type' in value) || value.entity_type === undefined) return false;
  if (!('entity_id' in value) || value.entity_id === undefined) return false;
  if (!('title' in value) || value.title === undefined) return false;
  if (!('play_url' in value) || value.play_url === undefined) return false;
  if (!('duration' in value) || value.duration === undefined) return false;
  if (!('status' in value) || value.status === undefined) return false;
  if (!('created_at' in value) || value.created_at === undefined) return false;
  if (!('updated_at' in value) || value.updated_at === undefined) return false;
  return true;
}

export function PlaylistItemDtoFromJSON(json: any): PlaylistItemDto {
  return PlaylistItemDtoFromJSONTyped(json, false);
}

export function PlaylistItemDtoFromJSONTyped(
  json: any,
  _ignoreDiscriminator: boolean,
): PlaylistItemDto {
  if (json == null) {
    return json;
  }
  return {
    id: json.id,
    creator_id: json.creator_id,
    space_id: json.space_id,
    board_id: json.board_id == null ? undefined : json.board_id,
    entity_type: PlaylistItemSourceEntityTypeFromJSON(json.entity_type),
    entity_id: json.entity_id,
    title: json.title,
    play_url: json.play_url,
    duration: json.duration,
    status: PlaylistItemStatusFromJSON(json.status),
    album_cover_url: json.album_cover_url == null ? undefined : json.album_cover_url,
    transcript: json.transcript == null ? undefined : json.transcript,
    rank: json.rank == null ? undefined : json.rank,
    playback_progress: json.playback_progress == null ? undefined : json.playback_progress,
    created_at: new Date(json.created_at),
    updated_at: new Date(json.updated_at),
  };
}

export function PlaylistItemDtoToJSON(json: any): PlaylistItemDto {
  return PlaylistItemDtoToJSONTyped(json, false);
}

export function PlaylistItemDtoToJSONTyped(
  value?: PlaylistItemDto | null,
  _ignoreDiscriminator: boolean = false,
): any {
  if (value == null) {
    return value;
  }

  return {
    id: value.id,
    creator_id: value.creator_id,
    space_id: value.space_id,
    board_id: value.board_id,
    entity_type: PlaylistItemSourceEntityTypeToJSON(value.entity_type),
    entity_id: value.entity_id,
    title: value.title,
    play_url: value.play_url,
    duration: value.duration,
    status: PlaylistItemStatusToJSON(value.status),
    album_cover_url: value.album_cover_url,
    transcript: value.transcript,
    rank: value.rank,
    playback_progress: value.playback_progress,
    created_at: value.created_at.toISOString(),
    updated_at: value.updated_at.toISOString(),
  };
}

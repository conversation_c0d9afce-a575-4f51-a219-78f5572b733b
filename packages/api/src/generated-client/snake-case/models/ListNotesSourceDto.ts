/* tslint:disable */
/* eslint-disable */
/**
 * You<PERSON><PERSON> (Snake Case)
 *   This documentation uses snake_case naming convention for compatibility with legacy clients.
 *
 * The version of the OpenAPI document: 1.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../../../runtime';
import type { NoteSourceEntityType } from './NoteSourceEntityType';
import {
  NoteSourceEntityTypeFromJSON,
  NoteSourceEntityTypeFromJSONTyped,
  NoteSourceEntityTypeToJSON,
  NoteSourceEntityTypeToJSONTyped,
} from './NoteSourceEntityType';

/**
 *
 * @export
 * @interface ListNotesSourceDto
 */
export interface ListNotesSourceDto {
  /**
   * 来源实体类型
   * @type {NoteSourceEntityType}
   * @memberof ListNotesSourceDto
   */
  entity_type?: NoteSourceEntityType;
  /**
   * 来源实体ID
   * @type {string}
   * @memberof ListNotesSourceDto
   */
  entity_id?: string;
}

/**
 * Check if a given object implements the ListNotesSourceDto interface.
 */
export function instanceOfListNotesSourceDto(value: object): value is ListNotesSourceDto {
  return true;
}

export function ListNotesSourceDtoFromJSON(json: any): ListNotesSourceDto {
  return ListNotesSourceDtoFromJSONTyped(json, false);
}

export function ListNotesSourceDtoFromJSONTyped(
  json: any,
  _ignoreDiscriminator: boolean,
): ListNotesSourceDto {
  if (json == null) {
    return json;
  }
  return {
    entity_type:
      json.entity_type == null ? undefined : NoteSourceEntityTypeFromJSON(json.entity_type),
    entity_id: json.entity_id == null ? undefined : json.entity_id,
  };
}

export function ListNotesSourceDtoToJSON(json: any): ListNotesSourceDto {
  return ListNotesSourceDtoToJSONTyped(json, false);
}

export function ListNotesSourceDtoToJSONTyped(
  value?: ListNotesSourceDto | null,
  _ignoreDiscriminator: boolean = false,
): any {
  if (value == null) {
    return value;
  }

  return {
    entity_type: NoteSourceEntityTypeToJSON(value.entity_type),
    entity_id: value.entity_id,
  };
}

/* tslint:disable */
/* eslint-disable */
/**
 * YouAPI (Snake Case)
 *   This documentation uses snake_case naming convention for compatibility with legacy clients.
 *
 * The version of the OpenAPI document: 1.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

/**
 * 实体类型
 * @export
 * @enum {string}
 */
export enum PlaylistItemSourceEntityType {
  snip = 'snip',
  thought = 'thought',
  unknownDefaultOpenApi = '11184809',
}

export function instanceOfPlaylistItemSourceEntityType(value: any): boolean {
  for (const key in PlaylistItemSourceEntityType) {
    if (Object.hasOwn(PlaylistItemSourceEntityType, key)) {
      if (
        PlaylistItemSourceEntityType[key as keyof typeof PlaylistItemSourceEntityType] === value
      ) {
        return true;
      }
    }
  }
  return false;
}

export function PlaylistItemSourceEntityTypeFromJSON(json: any): PlaylistItemSourceEntityType {
  return PlaylistItemSourceEntityTypeFromJSONTyped(json, false);
}

export function PlaylistItemSourceEntityTypeFromJSONTyped(
  json: any,
  _ignoreDiscriminator: boolean,
): PlaylistItemSourceEntityType {
  return json as PlaylistItemSourceEntityType;
}

export function PlaylistItemSourceEntityTypeToJSON(
  value?: PlaylistItemSourceEntityType | null,
): any {
  return value as any;
}

export function PlaylistItemSourceEntityTypeToJSONTyped(
  value: any,
  _ignoreDiscriminator: boolean,
): PlaylistItemSourceEntityType {
  return value as PlaylistItemSourceEntityType;
}

/* tslint:disable */
/* eslint-disable */
/**
 * <PERSON><PERSON><PERSON> (Snake Case)
 *   This documentation uses snake_case naming convention for compatibility with legacy clients.
 *
 * The version of the OpenAPI document: 1.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

/**
 * AI model to use for regeneration
 * @export
 * @enum {string}
 */
export enum LLMs {
  gpt4o = 'gpt-4o',
  gpt4oMini = 'gpt-4o-mini',
  gpt41 = 'gpt-4.1',
  gpt41Mini = 'gpt-4.1-mini',
  gpt41Nano = 'gpt-4.1-nano',
  o1Mini = 'o1-mini',
  o1Preview = 'o1-preview',
  o3Mini = 'o3-mini',
  o4Mini = 'o4-mini',
  textEmbedding3Large = 'text-embedding-3-large',
  tts1 = 'tts-1',
  tts1Hd = 'tts-1-hd',
  gpt4oMiniTts = 'gpt-4o-mini-tts',
  gptImage1 = 'gpt-image-1',
  claude4Sonnet = 'claude-4-sonnet',
  claude37Sonnet = 'claude-3-7-sonnet',
  claude37SonnetThinking = 'claude-3-7-sonnet-thinking',
  claude35Sonnet = 'claude-3-5-sonnet',
  claude35Haiku = 'claude-3-5-haiku',
  deepseekDeepseekV3 = 'deepseek/deepseek_v3',
  deepseekDeepseekR1 = 'deepseek/deepseek-r1',
  gemini25Pro = 'gemini-2.5-pro',
  gemini25Flash = 'gemini-2.5-flash',
  gemini25FlashLite = 'gemini-2.5-flash-lite',
  gemini20Flash = 'gemini-2.0-flash',
  gemini20FlashLite = 'gemini-2.0-flash-lite',
  qwenPlus = 'qwen-plus',
  qwenMax = 'qwen-max',
  qwenTurbo = 'qwen-turbo',
  speech02Hd = 'speech-02-hd',
  speech02Turbo = 'speech-02-turbo',
  speech01Hd = 'speech-01-hd',
  speech01Turbo = 'speech-01-turbo',
  unknownDefaultOpenApi = '11184809',
}

export function instanceOfLLMs(value: any): boolean {
  for (const key in LLMs) {
    if (Object.hasOwn(LLMs, key)) {
      if (LLMs[key as keyof typeof LLMs] === value) {
        return true;
      }
    }
  }
  return false;
}

export function LLMsFromJSON(json: any): LLMs {
  return LLMsFromJSONTyped(json, false);
}

export function LLMsFromJSONTyped(json: any, _ignoreDiscriminator: boolean): LLMs {
  return json as LLMs;
}

export function LLMsToJSON(value?: LLMs | null): any {
  return value as any;
}

export function LLMsToJSONTyped(value: any, _ignoreDiscriminator: boolean): LLMs {
  return value as LLMs;
}

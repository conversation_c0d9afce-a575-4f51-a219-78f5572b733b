/* tslint:disable */
/* eslint-disable */
/**
 * <PERSON><PERSON><PERSON> (Snake Case)
 *   This documentation uses snake_case naming convention for compatibility with legacy clients.
 *
 * The version of the OpenAPI document: 1.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../../../runtime';
import type { DiffNodeDto } from './DiffNodeDto';
import {
  DiffNodeDtoFromJSON,
  DiffNodeDtoFromJSONTyped,
  DiffNodeDtoToJSON,
  DiffNodeDtoToJSONTyped,
} from './DiffNodeDto';
import type { DiffReviewActionType } from './DiffReviewActionType';
import {
  DiffReviewActionTypeFromJSON,
  DiffReviewActionTypeFromJSONTyped,
  DiffReviewActionTypeToJSON,
  DiffReviewActionTypeToJSONTyped,
} from './DiffReviewActionType';
import type { ResolveVersionDto } from './ResolveVersionDto';
import {
  ResolveVersionDtoFromJSON,
  ResolveVersionDtoFromJSONTyped,
  ResolveVersionDtoToJSON,
  ResolveVersionDtoToJSONTyped,
} from './ResolveVersionDto';

/**
 *
 * @export
 * @interface ReportDiffReviewEventDto
 */
export interface ReportDiffReviewEventDto {
  /**
   * 想法ID
   * @type {string}
   * @memberof ReportDiffReviewEventDto
   */
  thought_id: string;
  /**
   * 操作类型
   * @type {DiffReviewActionType}
   * @memberof ReportDiffReviewEventDto
   */
  action: DiffReviewActionType;
  /**
   * 差异节点列表
   * @type {Array<DiffNodeDto>}
   * @memberof ReportDiffReviewEventDto
   */
  nodes: Array<DiffNodeDto>;
  /**
   * 解决版本数据（可选）
   * @type {ResolveVersionDto}
   * @memberof ReportDiffReviewEventDto
   */
  resolve_version?: ResolveVersionDto;
}

/**
 * Check if a given object implements the ReportDiffReviewEventDto interface.
 */
export function instanceOfReportDiffReviewEventDto(
  value: object,
): value is ReportDiffReviewEventDto {
  if (!('thought_id' in value) || value.thought_id === undefined) return false;
  if (!('action' in value) || value.action === undefined) return false;
  if (!('nodes' in value) || value.nodes === undefined) return false;
  return true;
}

export function ReportDiffReviewEventDtoFromJSON(json: any): ReportDiffReviewEventDto {
  return ReportDiffReviewEventDtoFromJSONTyped(json, false);
}

export function ReportDiffReviewEventDtoFromJSONTyped(
  json: any,
  _ignoreDiscriminator: boolean,
): ReportDiffReviewEventDto {
  if (json == null) {
    return json;
  }
  return {
    thought_id: json.thought_id,
    action: DiffReviewActionTypeFromJSON(json.action),
    nodes: (json.nodes as Array<any>).map(DiffNodeDtoFromJSON),
    resolve_version:
      json.resolve_version == null ? undefined : ResolveVersionDtoFromJSON(json.resolve_version),
  };
}

export function ReportDiffReviewEventDtoToJSON(json: any): ReportDiffReviewEventDto {
  return ReportDiffReviewEventDtoToJSONTyped(json, false);
}

export function ReportDiffReviewEventDtoToJSONTyped(
  value?: ReportDiffReviewEventDto | null,
  _ignoreDiscriminator: boolean = false,
): any {
  if (value == null) {
    return value;
  }

  return {
    thought_id: value.thought_id,
    action: DiffReviewActionTypeToJSON(value.action),
    nodes: (value.nodes as Array<any>).map(DiffNodeDtoToJSON),
    resolve_version: ResolveVersionDtoToJSON(value.resolve_version),
  };
}

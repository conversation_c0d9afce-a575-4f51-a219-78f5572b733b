/* tslint:disable */
/* eslint-disable */
/**
 * You<PERSON><PERSON> (Snake Case)
 *   This documentation uses snake_case naming convention for compatibility with legacy clients.
 *
 * The version of the OpenAPI document: 1.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

/**
 * Message mode
 * @export
 * @enum {string}
 */
export enum MessageModeEnum {
  ask = 'ask',
  agent = 'agent',
  unknownDefaultOpenApi = '11184809',
}

export function instanceOfMessageModeEnum(value: any): boolean {
  for (const key in MessageModeEnum) {
    if (Object.hasOwn(MessageModeEnum, key)) {
      if (MessageModeEnum[key as keyof typeof MessageModeEnum] === value) {
        return true;
      }
    }
  }
  return false;
}

export function MessageModeEnumFromJSON(json: any): MessageModeEnum {
  return MessageModeEnumFromJSONTyped(json, false);
}

export function MessageModeEnumFromJSONTyped(
  json: any,
  _ignoreDiscriminator: boolean,
): MessageModeEnum {
  return json as MessageModeEnum;
}

export function MessageModeEnumToJSON(value?: MessageModeEnum | null): any {
  return value as any;
}

export function MessageModeEnumToJSONTyped(
  value: any,
  _ignoreDiscriminator: boolean,
): MessageModeEnum {
  return value as MessageModeEnum;
}

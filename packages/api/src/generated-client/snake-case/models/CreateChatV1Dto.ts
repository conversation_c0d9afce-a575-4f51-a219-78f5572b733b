/* tslint:disable */
/* eslint-disable */
/**
 * You<PERSON><PERSON> (Snake Case)
 *   This documentation uses snake_case naming convention for compatibility with legacy clients.
 *
 * The version of the OpenAPI document: 1.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../../../runtime';
import type { ChatOriginDto } from './ChatOriginDto';
import {
  ChatOriginDtoFromJSON,
  ChatOriginDtoFromJSONTyped,
  ChatOriginDtoToJSON,
  ChatOriginDtoToJSONTyped,
} from './ChatOriginDto';

/**
 *
 * @export
 * @interface CreateChatV1Dto
 */
export interface CreateChatV1Dto {
  /**
   * Chat origin context
   * @type {ChatOriginDto}
   * @memberof CreateChatV1Dto
   */
  origin: ChatOriginDto;
  /**
   * Initial message for the chat
   * @type {string}
   * @memberof CreateChatV1Dto
   */
  message: string;
  /**
   * AI model to use
   * @type {string}
   * @memberof CreateChatV1Dto
   */
  chat_model: string;
  /**
   * Selected text context
   * @type {string}
   * @memberof CreateChatV1Dto
   */
  selection?: string;
  /**
   * Enable web search
   * @type {boolean}
   * @memberof CreateChatV1Dto
   */
  web_search?: boolean;
}

/**
 * Check if a given object implements the CreateChatV1Dto interface.
 */
export function instanceOfCreateChatV1Dto(value: object): value is CreateChatV1Dto {
  if (!('origin' in value) || value.origin === undefined) return false;
  if (!('message' in value) || value.message === undefined) return false;
  if (!('chat_model' in value) || value.chat_model === undefined) return false;
  return true;
}

export function CreateChatV1DtoFromJSON(json: any): CreateChatV1Dto {
  return CreateChatV1DtoFromJSONTyped(json, false);
}

export function CreateChatV1DtoFromJSONTyped(
  json: any,
  _ignoreDiscriminator: boolean,
): CreateChatV1Dto {
  if (json == null) {
    return json;
  }
  return {
    origin: ChatOriginDtoFromJSON(json.origin),
    message: json.message,
    chat_model: json.chat_model,
    selection: json.selection == null ? undefined : json.selection,
    web_search: json.web_search == null ? undefined : json.web_search,
  };
}

export function CreateChatV1DtoToJSON(json: any): CreateChatV1Dto {
  return CreateChatV1DtoToJSONTyped(json, false);
}

export function CreateChatV1DtoToJSONTyped(
  value?: CreateChatV1Dto | null,
  _ignoreDiscriminator: boolean = false,
): any {
  if (value == null) {
    return value;
  }

  return {
    origin: ChatOriginDtoToJSON(value.origin),
    message: value.message,
    chat_model: value.chat_model,
    selection: value.selection,
    web_search: value.web_search,
  };
}

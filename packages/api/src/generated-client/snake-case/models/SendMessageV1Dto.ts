/* tslint:disable */
/* eslint-disable */
/**
 * You<PERSON><PERSON> (Snake Case)
 *   This documentation uses snake_case naming convention for compatibility with legacy clients.
 *
 * The version of the OpenAPI document: 1.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../../../runtime';
import type { LLMs } from './LLMs';
import { LLMsFromJSON, LLMsFromJSONTyped, LLMsToJSON, LLMsToJSONTyped } from './LLMs';

/**
 *
 * @export
 * @interface SendMessageV1Dto
 */
export interface SendMessageV1Dto {
  /**
   * Chat ID to send message to
   * @type {string}
   * @memberof SendMessageV1Dto
   */
  chat_id: string;
  /**
   * Message content
   * @type {string}
   * @memberof SendMessageV1Dto
   */
  message: string;
  /**
   * Selected text context
   * @type {string}
   * @memberof SendMessageV1Dto
   */
  selection?: string;
  /**
   * AI model to use
   * @type {LLMs}
   * @memberof SendMessageV1Dto
   */
  chat_model?: LLMs;
  /**
   * Enable web search
   * @type {boolean}
   * @memberof SendMessageV1Dto
   */
  web_search?: boolean;
}

/**
 * Check if a given object implements the SendMessageV1Dto interface.
 */
export function instanceOfSendMessageV1Dto(value: object): value is SendMessageV1Dto {
  if (!('chat_id' in value) || value.chat_id === undefined) return false;
  if (!('message' in value) || value.message === undefined) return false;
  return true;
}

export function SendMessageV1DtoFromJSON(json: any): SendMessageV1Dto {
  return SendMessageV1DtoFromJSONTyped(json, false);
}

export function SendMessageV1DtoFromJSONTyped(
  json: any,
  _ignoreDiscriminator: boolean,
): SendMessageV1Dto {
  if (json == null) {
    return json;
  }
  return {
    chat_id: json.chat_id,
    message: json.message,
    selection: json.selection == null ? undefined : json.selection,
    chat_model: json.chat_model == null ? undefined : LLMsFromJSON(json.chat_model),
    web_search: json.web_search == null ? undefined : json.web_search,
  };
}

export function SendMessageV1DtoToJSON(json: any): SendMessageV1Dto {
  return SendMessageV1DtoToJSONTyped(json, false);
}

export function SendMessageV1DtoToJSONTyped(
  value?: SendMessageV1Dto | null,
  _ignoreDiscriminator: boolean = false,
): any {
  if (value == null) {
    return value;
  }

  return {
    chat_id: value.chat_id,
    message: value.message,
    selection: value.selection,
    chat_model: LLMsToJSON(value.chat_model),
    web_search: value.web_search,
  };
}

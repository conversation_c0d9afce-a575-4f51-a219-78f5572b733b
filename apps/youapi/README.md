## 本地启动

本地启动依赖 Redis 服务，需安装 docker 后在 youapi 项目根目录执行

```bash
npm run docker:up
```

首次启动 youapi 时，执行以下脚本初始化 Redis 配置

```bash
echo "REDIS_ENDPOINT=127.0.0.1" >> .env.preview.local
```

## 查看接口文档

启动服务后访问 http://localhost:4000/doc 查看 Swagger 文档

## 生成请求 SDK

启动服务后在 youniverse 项目根目录下执行 `npm run generate:api` 生成文档

## 排查循环依赖

madge + graphviz 方案只适合静态依赖分析。NestJS 中可以引入一个服务文件中的类型定义也会被识别成循环依赖。

~~
先安装个 `graphviz`

```bash
brew install graphviz
```

再安装一个全局依赖

```bash
pnpm -g install madge
```

生成依赖图，可以安装一个 Graphviz 插件进行预览

```bash
pnpm run debug:cd
```
~~

推荐使用 NestJS 官方推出的 DevTool 进行查验，启动本地服务后访问 https://devtools.nestjs.com/ 查看依赖
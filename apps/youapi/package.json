{"name": "you<PERSON>i", "version": "0.0.0", "private": true, "_deployment_trigger": "force redeploy 2025-07-23 to fix OpenTelemetry issue", "scripts": {"dev": "YOUMIND_ENV=preview NODE_ENV=development npm start", "dev:local": "npm run docker:up && npm run dev", "dev:with-deps": "npm run build:deps && npm run dev", "dev:prod": "YOUMIND_ENV=production NODE_ENV=production npm start", "build": "NODE_ENV=production ../../devops/scripts/with-doppler nest build youapi", "build:deps": "pnpm exec turbo build --filter=youapi^...", "start": "NODE_OPTIONS='--experimental-require-module' ../../devops/scripts/with-doppler nest start youapi --watch", "lint": "biome check . --diagnostic-level=error", "lint:fix": "biome check . --write --diagnostic-level=error", "format": "biome format . --write", "format:check": "biome format .", "migrate:generate": "drizzle-kit generate", "migrate:local": "NODE_ENV=preview ../../devops/scripts/with-doppler tsx scripts/migrate.ts", "type-check": "tsc --noEmit", "test": "../../devops/scripts/with-doppler jest", "test:watch": "../../devops/scripts/with-doppler jest --watch", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "../../devops/scripts/with-doppler jest --config ./test/jest.config.js", "debug:cd": "madge --dot --extensions ts --circular --exclude '.*types\\.ts$' src > graph.gv", "env": "node ../../devops/scripts/doppler-pull.js youapi", "env:prod": "node ../../devops/scripts/doppler-pull.js youapi --env=production", "config": "node ../../devops/scripts/doppler-pull-config.js", "config:prod": "node ../../devops/scripts/doppler-pull-config.js --env=production", "test:material": "jest --config ./src/modules/material-mng/tests/jest.config.js", "test:material:youapp": "TEST_TARGET=youapp jest --config ./src/modules/material-mng/tests/jest.config.js", "test:material:youapi": "TEST_TARGET=youapi jest --config ./src/modules/material-mng/tests/jest.config.js", "test:iam": "jest --config ./src/modules/iam/tests/jest.config.js", "test:iam:youapp": "TEST_TARGET=youapp jest --config ./src/modules/iam/tests/jest.config.js", "test:iam:youapi": "TEST_TARGET=youapi jest --config ./src/modules/iam/tests/jest.config.js", "test:search": "jest --config ./src/modules/search/tests/jest.config.js", "test:search:youapp": "TEST_TARGET=youapp jest --config ./src/modules/search/tests/jest.config.js", "test:search:youapi": "TEST_TARGET=youapi jest --config ./src/modules/search/tests/jest.config.js", "test:chat": "NODE_ENV=test ../../devops/scripts/with-doppler jest --config ./src/modules/chat/tests/jest.config.js", "test:ai": "NODE_ENV=test ../../devops/scripts/with-doppler jest --config ./src/modules/ai/tests/jest.config.js", "docker:up": "docker compose up -d"}, "dependencies": {"@ai-sdk/amazon-bedrock": "^2.2.12", "@ai-sdk/anthropic": "^1.2.12", "@ai-sdk/azure": "^1.3.24", "@ai-sdk/elevenlabs": "^0.0.3", "@ai-sdk/google-vertex": "^2.2.27", "@ai-sdk/openai": "^1.3.23", "@ai-sdk/provider": "^1.1.3", "@ai-sdk/provider-utils": "^2.2.8", "@alicloud/openapi-client": "^0.4.15", "@alicloud/tingwu20230930": "^2.0.22", "@anthropic-ai/sdk": "^0.56.0", "@apple/app-store-server-library": "^1.6.0", "@aws-sdk/client-bedrock-runtime": "^3.842.0", "@aws-sdk/client-s3": "^3.842.0", "@aws-sdk/cloudfront-signer": "^3.821.0", "@aws-sdk/credential-providers": "^3.840.0", "@aws-sdk/lib-storage": "^3.842.0", "@aws-sdk/s3-request-presigner": "^3.842.0", "@clickhouse/client": "^1.11.2", "@elevenlabs/elevenlabs-js": "^2.5.0", "@google-analytics/data": "^5.1.0", "@google-cloud/vertexai": "^1.10.0", "@googleapis/customsearch": "^4.0.1", "@nestjs/bullmq": "^11.0.2", "@nestjs/common": "^11.0.1", "@nestjs/config": "^4.0.2", "@nestjs/core": "^11.0.1", "@nestjs/cqrs": "^11.0.3", "@nestjs/platform-express": "^11.0.1", "@nestjs/schedule": "^6.0.0", "@opentelemetry/api": "^1.9.0", "@opentelemetry/auto-instrumentations-node": "^0.60.1", "@opentelemetry/core": "^2.0.1", "@opentelemetry/exporter-logs-otlp-http": "^0.203.0", "@opentelemetry/exporter-trace-otlp-http": "^0.203.0", "@opentelemetry/instrumentation-pino": "^0.49.0", "@opentelemetry/resources": "^2.0.1", "@opentelemetry/sdk-logs": "^0.203.0", "@opentelemetry/sdk-node": "^0.202.0", "@opentelemetry/semantic-conventions": "^1.34.0", "@repo/server-common": "workspace:*", "@stripe/stripe-js": "^7.4.0", "@supabase/ssr": "catalog:", "@supabase/supabase-js": "catalog:", "@youmindinc/youicon": "catalog:", "ai": "^4.3.19", "axios": "^1.7.7", "blurhash": "^2.0.5", "class-transformer": "^0.5.1", "class-validator": "^0.14.2", "cookie-parser": "^1.4.7", "date-fns": "^3.6.0", "drizzle-orm": "^0.44.3", "fractional-indexing": "^3.2.0", "franc-min": "^6.2.0", "google-auth-library": "^10.1.0", "hbs": "^4.2.0", "ioredis": "^5.6.1", "iso-639-3": "^3.0.1", "jose": "^5.9.6", "js-tiktoken": "^1.0.20", "jsonrepair": "^3.12.0", "langfuse": "^3.38.2", "langfuse-core": "^3.38.2", "lodash": "^4.17.21", "marked": "^16.0.0", "nanoid": "^5.0.9", "nestjs-cls": "^6.0.1", "nestjs-pino": "^4.4.0", "openai": "^5.5.1", "optional": "^0.1.4", "p-limit": "^6.2.0", "postgres": "^3.4.5", "redlock": "5.0.0-beta.2", "reflect-metadata": "^0.2.2", "resend": "^4.6.0", "rxjs": "^7.8.1", "sharp": "^0.34.3", "stripe": "^18.3.0", "striptags": "^3.2.0", "turndown": "^7.2.0", "typesense": "^2.0.3", "uuidv7": "catalog:", "validator": "catalog:", "zod": "catalog:", "zod-validation-error": "^3.4.0"}, "devDependencies": {"@nestjs/cli": "^11.0.0", "@nestjs/devtools-integration": "^0.2.0", "@nestjs/swagger": "^11.2.0", "@nestjs/testing": "^11.1.3", "@repo/common": "workspace:*", "@repo/config": "workspace:*", "@repo/jest-config": "workspace:*", "@repo/typescript-config": "workspace:*", "@swc/jest": "^0.2.39", "@types/express": "^5.0.0", "@types/jest": "^29.5.14", "@types/lodash": "^4.17.13", "@types/multer": "^2.0.0", "@types/node": "catalog:", "@types/redlock": "^4.0.7", "@types/supertest": "^6.0.2", "drizzle-kit": "^0.31.4", "globals": "^16.0.0", "jest": "^29.7.0", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "ts-jest": "^29.2.5", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "catalog:"}, "jest": {"rootDir": "src", "transform": {"^.+\\.(t|j)s$": "@swc/jest"}, "transformIgnorePatterns": ["^(?!.*(nanoid|franc-min|trigram-utils|n-gram|collapse-white-space|iso-639-3|p-limit|yocto-queue|fractional-indexing|marked)).*node_modules.*$"], "moduleNameMapper": {"^@/(.*)$": "<rootDir>/$1"}, "collectCoverageFrom": ["**/*.ts"], "coverageDirectory": "../coverage", "coveragePathIgnorePatterns": ["/node_modules/", "/dist/", "/coverage/", "/tests/"], "testEnvironment": "node", "testRegex": ".*\\.spec\\.ts$", "moduleFileExtensions": ["js", "json", "ts"]}}
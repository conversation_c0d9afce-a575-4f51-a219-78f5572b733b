# 枚举字段使用数组字面量问题报告

根据规则：
- **规则 1**：对于特定的枚举引用（如 `enum: SnipType`），需要加上 `enumName: 'EnumName'`
- **规则 2**：对于数组枚举（如 `enum: ['option1', 'option2']`），不需要更改

## 发现的问题

以下是字段类型确实是枚举类或 const 断言，但 `@ApiProperty` 装饰器中的 `enum` 使用了数组字面量格式的情况。这些应该按照规则 1 处理：

### 1. ThoughtDto (thought.dto.ts)

**当前情况：**
```typescript
@ApiProperty({
  description: '标题类型',
  enum: ['default', 'ai', 'manual'],
  example: 'manual',
})
titleType: TitleType;
```

**问题说明：**
- `TitleType` 是一个 const 断言类型
- 应该使用 `enum: TitleType` 而不是数组字面量

**应该修改为：**
```typescript
@ApiProperty({
  description: '标题类型',
  enum: TitleType,
  enumName: 'TitleType',
  example: 'manual',
})
titleType: TitleType;
```

### 2. UpdateThoughtDto (update-thought.dto.ts)

**当前情况：**
```typescript
@ApiProperty({
  description: '标题类型',
  enum: ['default', 'ai', 'manual'],
  example: 'manual',
  required: false,
})
titleType?: TitleType;
```

**问题说明：**
- `TitleType` 是一个 const 断言类型
- 应该使用 `enum: TitleType` 而不是数组字面量

**应该修改为：**
```typescript
@ApiProperty({
  description: '标题类型',
  enum: TitleType,
  enumName: 'TitleType',
  example: 'manual',
  required: false,
})
titleType?: TitleType;
```

### 3. PlaylistItemDto (playlist-item.dto.ts)

**当前情况：**
```typescript
@ApiProperty({ description: '状态', enum: ['generating', 'success', 'failed'] })
status: string;
```

**问题说明：**
- 应该使用 `PlaylistItemStatus` 类型而不是 `string`
- 应该使用 `enum: PlaylistItemStatus` 而不是数组字面量

**应该修改为：**
```typescript
@ApiProperty({ 
  description: '状态',
  enum: PlaylistItemStatus,
  enumName: 'PlaylistItemStatus'
})
status: PlaylistItemStatus;
```

### 4. CreatePlaylistItemDto (playlist-item.dto.ts)

**当前情况：**
```typescript
@ApiProperty({ description: '实体类型', enum: ['snip', 'thought'] })
@IsEnum(['snip', 'thought'])
entityType: string;
```

**问题说明：**
- 应该使用 `PlaylistItemSourceEntityType` 类型
- 应该使用 `enum: PlaylistItemSourceEntityType` 而不是数组字面量

**应该修改为：**
```typescript
@ApiProperty({ 
  description: '实体类型',
  enum: PlaylistItemSourceEntityType,
  enumName: 'PlaylistItemSourceEntityType'
})
@IsEnum(PlaylistItemSourceEntityType)
entityType: PlaylistItemSourceEntityType;
```

### 5. ShortLinkDto (short-link.dto.ts)

**当前情况：**
```typescript
@ApiProperty({
  description: '实体类型',
  example: 'snip',
  enum: ['snip', 'thought', 'board'],
})
entityType: string;
```

**问题说明：**
- 应该使用 `EntityType` 类型而不是 `string`
- 应该使用 `enum: EntityType` 而不是数组字面量

**应该修改为：**
```typescript
@ApiProperty({
  description: '实体类型',
  enum: EntityType,
  enumName: 'EntityType',
  example: 'snip',
})
entityType: EntityType;
```

### 6. ReportDiffReviewEventDto (report-diff-review-event.dto.ts)

**当前情况：**
```typescript
@ApiProperty({
  description: '操作类型',
  example: 'accept',
  enum: [DiffReviewAction.ACCEPT, DiffReviewAction.REJECT],
})
@IsIn([DiffReviewAction.ACCEPT, DiffReviewAction.REJECT])
action: string;
```

**问题说明：**
- 应该使用 `DiffReviewAction` 类型而不是 `string`
- 应该使用 `enum: DiffReviewAction` 而不是数组字面量

**应该修改为：**
```typescript
@ApiProperty({
  description: '操作类型',
  enum: DiffReviewAction,
  enumName: 'DiffReviewAction',
  example: 'accept',
})
@IsEnum(DiffReviewAction)
action: DiffReviewAction;
```

### 7. ListNotesSourceDto (list-notes.dto.ts)

**当前情况：**
```typescript
@ApiPropertyOptional({
  description: '来源实体类型',
  enum: ['snip', 'thought'],
  example: 'snip',
})
@IsOptional()
@IsIn(['snip', 'thought'])
entityType?: NoteSourceEntityType;
```

**问题说明：**
- 字段类型已经是 `NoteSourceEntityType`，但装饰器使用数组字面量
- 应该使用 `enum: NoteSourceEntityType` 而不是数组字面量

**应该修改为：**
```typescript
@ApiPropertyOptional({
  description: '来源实体类型',
  enum: NoteSourceEntityType,
  enumName: 'NoteSourceEntityType',
  example: 'snip',
})
@IsOptional()
@IsEnum(NoteSourceEntityType)
entityType?: NoteSourceEntityType;
```

## 不需要修改的情况

以下情况使用了字符串字面量类型，符合规则 2，不需要修改：

### SearchResponseDto (search-response.dto.ts)
```typescript
@ApiProperty({ description: '文档类型', enum: ['snip', 'thought'] })
type: 'snip' | 'thought';
```
✅ 使用字符串字面量类型，符合规则 2

### Chat 模块的各种工具 DTO 文件
这些文件中的枚举字段大多使用字符串字面量类型，如：
```typescript
useTool: 'required' | 'auto' | 'none';
size?: 'square' | 'portrait' | 'landscape';
```
✅ 使用字符串字面量类型，符合规则 2

### AI 模块的 edit-image.dto.ts
```typescript
size: '1024x1024' | '1536x1024' | '1024x1536' | 'auto';
```
✅ 使用字符串字面量类型，符合规则 2

## 总结

需要修改的 7 个问题：
1. **ThoughtDto.titleType** - 使用 TitleType 枚举
2. **UpdateThoughtDto.titleType** - 使用 TitleType 枚举
3. **PlaylistItemDto.status** - 使用 PlaylistItemStatus 枚举
4. **CreatePlaylistItemDto.entityType** - 使用 PlaylistItemSourceEntityType 枚举
5. **ShortLinkDto.entityType** - 使用 EntityType 枚举
6. **ReportDiffReviewEventDto.action** - 使用 DiffReviewAction 枚举
7. **ListNotesSourceDto.entityType** - 使用 NoteSourceEntityType 枚举

这些修改将确保：
- 类型安全性：字段类型与枚举定义一致
- 文档准确性：Swagger 文档正确显示枚举值和名称
- 代码一致性：遵循统一的枚举使用规范

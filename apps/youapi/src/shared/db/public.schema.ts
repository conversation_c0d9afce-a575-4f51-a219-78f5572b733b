import { isNull, relations, sql } from 'drizzle-orm';
import {
  bigint,
  boolean,
  doublePrecision,
  index,
  integer,
  jsonb,
  pgTable,
  text,
  timestamp,
  uniqueIndex,
  uuid,
  varchar,
} from 'drizzle-orm/pg-core';
import { uuidv7 } from 'uuidv7';
import {
  ChatModeEnum,
  ChatOriginTypeEnum,
  CompletionBlockStatusEnum,
  CompletionBlockTypeEnum,
  MessageModeEnum,
  MessageRoleEnum,
  MessageStatusEnum,
} from '@/common/types';
import { SpaceMetadata } from '@/domain/space/types';
import {
  CreditType,
  TransactionType,
} from '@/modules/iam/domain/credit/models/credit-transaction.types';
import { SpaceStatus } from '@/modules/iam/domain/space/models/space.entity';
import {
  BillingInterval,
  ProductTier,
  StripePriceLookupKey,
  SubscriptionProvider,
  SubscriptionStatus,
} from '@/modules/iam/domain/subscription/models/subscription.types';
import { BoardStatus, BoardType } from '@/modules/material-mng/domain/board/models/board.entity';
import {
  Snip<PERSON><PERSON>,
  SnipStatus,
  SnipType,
} from '@/modules/material-mng/domain/snip/models/snip.entity';

function getEnumValues(enumObj: Record<string, string>): [string, ...string[]] {
  return Object.values(enumObj) as [string, ...string[]];
}

// Enum definitions
export const DisplayLanguageEnum = {
  EN: 'en',
  ZH: 'zh',
  JA: 'ja',
  AUTO: 'auto',
} as const;

export const AILanguageEnum = {
  system: 'system',
  en: 'en',
  zh: 'zh',
  ja: 'ja',
} as const;

export const LanguageEnum = {
  en: 'en',
  zh: 'zh',
  ja: 'ja',
  ko: 'ko',
  fr: 'fr',
  de: 'de',
  es: 'es',
  pt: 'pt',
  it: 'it',
  ru: 'ru',
} as const;

export const QuotaResourceEnum = {
  STORAGE: 'storage',
  COMPUTE: 'compute',
} as const;

export const ContentFormatEnum = {
  MARKDOWN: 'markdown',
  HTML: 'html',
  TEXT: 'text',
} as const;

export const ProcessStatusEnum = {
  PENDING: 'pending',
  PROCESSING: 'processing',
  COMPLETED: 'completed',
  FAILED: 'failed',
} as const;

export const SnipFeatureEnum = {
  OVERVIEW: 'overview',
  SUMMARY: 'summary',
  EXPLAIN: 'explain',
  TRANSCRIPT: 'transcript',
} as const;

export const BlockDisplayEnum = {
  SHOW: 'show',
  HIDE: 'hide',
} as const;

export const BlockHeightEnum = {
  AUTO: 'auto',
  SMALL: 'small',
  MEDIUM: 'medium',
  LARGE: 'large',
} as const;

export const ActionTypeEnum = {
  LLM: 'llm',
  WORKFLOW: 'workflow',
} as const;

export const ActionStatusEnum = {
  ACTIVE: 'active',
  INACTIVE: 'inactive',
} as const;

export const EntityType = {
  SNIP: 'snip',
  BOARD: 'board',
  THOUGHT: 'thought',
  CHAT: 'chat',
} as const;

export type EntityType = (typeof EntityType)[keyof typeof EntityType];

export const Visibility = {
  PRIVATE: 'private',
  PUBLIC: 'public',
} as const;

export type Visibility = (typeof Visibility)[keyof typeof Visibility];

export const AssistantTypeEnum = {
  PROMPT_EXECUTER: 'prompt-executer',
} as const;

export const AssistantIconTypeEnum = {
  EMOJI: 'emoji',
  ICON: 'icon',
} as const;

export const AssistantVisibilityEnum = {
  PRIVATE: 'private',
  PUBLIC: 'public',
} as const;

export const AssistantRunModeEnum = {
  MANUAL: 'manual',
  AUTO: 'auto',
} as const;

export const PlaylistItemStatusEnum = {
  PENDING: 'pending',
  PLAYING: 'playing',
  PAUSED: 'paused',
  COMPLETED: 'completed',
} as const;

export const DiffReviewEventActionEnum = {
  ACCEPT: 'accept',
  REJECT: 'reject',
} as const;

// export const TransactionTypeEnum = {
//   GRANT: 'grant',
//   CONSUME: 'consume',
//   FORFEIT: 'forfeit',
// } as const;

// export const CreditTypeEnum = {
//   MONTHLY: 'monthly',
// } as const;

// DiffReviewEventNode type - need to import from common types
export type DiffReviewEventNode = any; // placeholder

export const notes = pgTable(
  'notes',
  {
    id: uuid('id')
      .primaryKey()
      .$default(() => uuidv7()),
    createdAt: timestamp('created_at', { withTimezone: true }).notNull().defaultNow(),
    updatedAt: timestamp('updated_at', { withTimezone: true }).notNull().defaultNow(),
    deletedAt: timestamp('deleted_at', { withTimezone: true }),
    spaceId: uuid('space_id').notNull(),
    creatorId: uuid('creator_id').notNull(),
    boardId: uuid('board_id').notNull(),
    entityType: varchar('entity_type', {
      length: 20,
      enum: getEnumValues(EntityType),
    }),
    entityId: uuid('entity_id'),
    selection: text('selection'),
    quoteRaw: text('quote_raw'),
    quotePlain: text('quote_plain'),
    contentRaw: text('content_raw'),
    contentPlain: text('content_plain'),
  },
  (t) => {
    return {
      a: index('idx_deleted_at_space_id_board_id_entity_type_entity_id').on(
        t.deletedAt,
        t.spaceId,
        t.boardId,
        t.entityType,
        t.entityId,
      ),
    };
  },
);

export const spaces = pgTable(
  'spaces',
  {
    id: uuid('id')
      .primaryKey()
      .$default(() => uuidv7()),
    createdAt: timestamp('created_at', { withTimezone: true }).notNull().defaultNow(),
    updatedAt: timestamp('updated_at', { withTimezone: true }).notNull().defaultNow(),
    deletedAt: timestamp('deleted_at', { withTimezone: true }),
    creatorId: uuid('creator_id').notNull(),
    activatedAt: timestamp('activated_at', { withTimezone: true }),
    status: varchar('status', {
      length: 32,
      enum: getEnumValues(SpaceStatus),
    })
      .notNull()
      .default(SpaceStatus.UNINITIALIZED),
    subscriptionPrice: varchar('subscription_price', {
      enum: getEnumValues(StripePriceLookupKey),
      length: 64,
    }),
    trialDays: integer('trial_days'),
    trialExpiresAt: timestamp('trial_expires_at', { withTimezone: true }),
    subscriptionId: varchar('subscription_id', { length: 32 }),
    subscriptionStatus: varchar('subscription_status', {
      length: 32,
      enum: Object.values(SubscriptionStatus) as [string, ...string[]],
    }),
    subscriptionNextBillingDate: timestamp('subscription_next_billing_date', {
      withTimezone: true,
    }),
    subscriptionCreatedAt: timestamp('subscription_created_at', {
      withTimezone: true,
    }),
    subscriptionCancelAtPeriodEnd: boolean('subscription_cancel_at_period_end'),
    // 订阅来源：stripe 或 apple
    subscriptionSource: varchar('subscription_source', {
      length: 16,
      enum: getEnumValues(SubscriptionProvider),
    }),
    productTier: varchar('product_tier', {
      length: 32,
      enum: getEnumValues(ProductTier),
    })
      .notNull()
      .default(ProductTier.FREE),
    metadata: jsonb('metadata').$type<SpaceMetadata>().notNull().default({}),
  },
  (t) => {
    return {
      // 目前只支持每个用户自动初始化一个 space，因此暂时要 creator_id 作统一索引（考虑软删除），未来删除掉这个索引
      a: uniqueIndex('spaces_creator_id_idx')
        .on(t.creatorId)
        /**
         * BUG: 这里虽然支持 where 语句，但生成的 SQL 里却缺失了，暂时先手工补上
         * https://github.com/drizzle-team/drizzle-orm/issues/1519
         */
        .where(isNull(t.deletedAt)),
      b: uniqueIndex('idx_spaces_subscription_id').on(t.subscriptionId).where(isNull(t.deletedAt)),
    };
  },
);

export const boards = pgTable(
  'boards',
  {
    id: uuid('id')
      .primaryKey()
      .$default(() => uuidv7()),
    createdAt: timestamp('created_at', { withTimezone: true }).notNull().defaultNow(),
    updatedAt: timestamp('updated_at', { withTimezone: true }).notNull().defaultNow(),
    deletedAt: timestamp('deleted_at', { withTimezone: true }),
    spaceId: uuid('space_id').notNull(),
    creatorId: uuid('creator_id').notNull(),
    name: varchar('name', { length: 255 }).notNull(),
    description: varchar('description', { length: 2048 }).notNull(),
    iconName: varchar('icon_name', { length: 255 }).notNull(),
    iconColor: varchar('icon_color', { length: 255 }).notNull(),
    pinnedAt: timestamp('pinned_at', { withTimezone: true }),
    filter: jsonb('filter'),
    status: varchar('status', {
      length: 32,
      enum: getEnumValues(BoardStatus),
    })
      .notNull()
      .default(BoardStatus.InProgress),
    heroImageUrls: jsonb('hero_image_urls').$type<string[]>(),
    intro: text('intro'),
    rank: varchar('rank', { length: 255 }).notNull().default('~'),
    type: varchar('type', {
      length: 32,
      enum: getEnumValues(BoardType),
    })
      .notNull()
      .default(BoardType.Normal),
  },
  (table) => {
    return {
      a: index('idx_boards_deleted_at_space_id').on(table.deletedAt, table.spaceId),
    };
  },
);

// Title type enum
export const TitleTypeEnum = {
  DEFAULT: 'default',
  AI: 'ai',
  MANUAL: 'manual',
} as const;

// Visibility enum 已统一到文件前面的 Visibility 定义

// Board group type enum
export const BoardGroupTypeEnum = {
  NORMAL: 'normal',
  UPLOADS: 'uploads',
} as const;

export const thoughts = pgTable(
  'thoughts',
  {
    id: uuid('id')
      .primaryKey()
      .$default(() => uuidv7()),
    createdAt: timestamp('created_at', { withTimezone: true }).notNull().defaultNow(),
    updatedAt: timestamp('updated_at', { withTimezone: true }).notNull().defaultNow(),
    deletedAt: timestamp('deleted_at', { withTimezone: true }),
    spaceId: uuid('space_id').notNull(),
    creatorId: uuid('creator_id').notNull(),
    title: varchar('title', { length: 255 }),
    titleType: varchar('title_type', {
      length: 32,
      enum: getEnumValues(TitleTypeEnum),
    })
      .notNull()
      .default(TitleTypeEnum.MANUAL),
    contentRaw: text('content_raw').notNull(),
    contentPlain: text('content_plain'),
    visibility: varchar('visibility', {
      length: 10,
      enum: getEnumValues(Visibility),
    })
      .notNull()
      .default(Visibility.PRIVATE),
  },
  (t) => {
    return {
      a: index('idx_thoughts_deleted_at_space_id').on(t.deletedAt, t.spaceId),
    };
  },
);

// Thought version type enum
export const ThoughtVersionTypeEnum = {
  AI: 'ai',
  MANUAL: 'manual',
  AUTO: 'auto',
  RESOLVE: 'resolve',
  BACKUP: 'backup',
} as const;

export const thoughtVersions = pgTable(
  'thought_versions',
  {
    id: uuid('id')
      .primaryKey()
      .$default(() => uuidv7()),
    createdAt: timestamp('created_at', { withTimezone: true }).notNull().defaultNow(),
    updatedAt: timestamp('updated_at', { withTimezone: true }).notNull().defaultNow(),
    deletedAt: timestamp('deleted_at', { withTimezone: true }),
    thoughtId: uuid('thought_id').notNull(),
    type: varchar('type', {
      length: 20,
      enum: getEnumValues(ThoughtVersionTypeEnum),
    }).notNull(),
    title: varchar('title', { length: 255 }).notNull(),
    description: varchar('description', { length: 2048 }),
    thoughtTitle: varchar('thought_title', { length: 255 }).notNull(),
    contentRaw: text('content_raw').notNull(),
    contentPlain: text('content_plain'),
  },
  (t) => {
    return {
      a: index('idx_thought_id_type').on(t.thoughtId, t.type).where(isNull(t.deletedAt)),
    };
  },
);

// Diff review action enum
export const DiffReviewActionEnum = {
  ACCEPT: 'accept',
  REJECT: 'reject',
} as const;

export const diffReviewEvents = pgTable(
  'diff_review_events',
  {
    id: uuid('id')
      .primaryKey()
      .$default(() => uuidv7()),
    createdAt: timestamp('created_at', { withTimezone: true }).notNull().defaultNow(),
    thoughtId: uuid('thought_id').notNull(),
    action: varchar('action', {
      length: 20,
      enum: getEnumValues(DiffReviewActionEnum),
    }).notNull(),
    nodes: jsonb('nodes').$type<DiffReviewEventNode[]>(),
    resolveVersionId: uuid('resolve_version_id'),
  },
  (t) => {
    return {
      a: index('idx_thought_id_created_at').on(t.thoughtId, t.createdAt),
    };
  },
);

export const boardItems = pgTable(
  'board_items',
  {
    id: uuid('id')
      .primaryKey()
      .$default(() => uuidv7()),
    createdAt: timestamp('created_at', { withTimezone: true }).notNull().defaultNow(),
    updatedAt: timestamp('updated_at', { withTimezone: true }).notNull().defaultNow(),
    deletedAt: timestamp('deleted_at', { withTimezone: true }),
    boardId: uuid('board_id').notNull(),
    parentBoardGroupId: uuid('parent_board_group_id'),
    snipId: uuid('snip_id'),
    thoughtId: uuid('thought_id'),
    boardGroupId: uuid('board_group_id'),
    chatId: uuid('chat_id'),
    rank: varchar('rank', { length: 255 }).notNull().default('~'),
  },
  (t) => {
    return {
      a: uniqueIndex('idx_board_items_board_id_snip_id')
        .on(t.boardId, t.snipId)
        .where(isNull(t.deletedAt)),
      b: uniqueIndex('idx_board_items_board_id_thought_id')
        .on(t.boardId, t.thoughtId)
        .where(isNull(t.deletedAt)),
      c: uniqueIndex('idx_board_items_board_id_board_group_id')
        .on(t.boardId, t.boardGroupId)
        .where(isNull(t.deletedAt)),
      d: uniqueIndex('idx_board_items_board_id_chat_id')
        .on(t.boardId, t.chatId)
        .where(isNull(t.deletedAt)),
      e: index('idx_board_items_deleted_at_board_id_parent_board_group_id_rank').on(
        t.deletedAt,
        t.boardId,
        t.parentBoardGroupId,
        t.rank,
      ),
    };
  },
);

export const boardGroups = pgTable('board_groups', {
  id: uuid('id')
    .primaryKey()
    .$default(() => uuidv7()),
  createdAt: timestamp('created_at', { withTimezone: true }).notNull().defaultNow(),
  updatedAt: timestamp('updated_at', { withTimezone: true }).notNull().defaultNow(),
  deletedAt: timestamp('deleted_at', { withTimezone: true }),
  creatorId: uuid('creator_id').notNull(),
  boardId: uuid('board_id').notNull(),
  name: varchar('name', { length: 255 }).notNull(),
  iconName: varchar('icon_name', { length: 255 }),
  iconColor: varchar('icon_color', { length: 255 }),
  type: varchar('type', {
    length: 32,
    enum: getEnumValues(BoardGroupTypeEnum),
  })
    .notNull()
    .default(BoardGroupTypeEnum.NORMAL),
});

// Favorite entity type enum
export const FavoriteEntityTypeEnum = {
  BOARD: 'board',
  SNIP: 'snip',
  THOUGHT: 'thought',
  BOARD_GROUP: 'board_group',
} as const;

export const favorites = pgTable(
  'favorites',
  {
    id: uuid('id')
      .primaryKey()
      .$default(() => uuidv7()),
    createdAt: timestamp('created_at', { withTimezone: true }).notNull().defaultNow(),
    updatedAt: timestamp('updated_at', { withTimezone: true }).notNull().defaultNow(),
    deletedAt: timestamp('deleted_at', { withTimezone: true }),
    spaceId: uuid('space_id').notNull(),
    creatorId: uuid('creator_id').notNull(),
    entityType: varchar('entity_type', {
      length: 20,
      enum: getEnumValues(FavoriteEntityTypeEnum),
    }).notNull(),
    entityId: uuid('entity_id').notNull(),
    rank: varchar('rank', { length: 255 }).notNull(),
  },
  (t) => {
    return {
      a: uniqueIndex('idx_space_id_entity_type_entity_id')
        .on(t.spaceId, t.entityType, t.entityId)
        .where(isNull(t.deletedAt)),
    };
  },
);

export const snips = pgTable(
  'snips',
  {
    id: uuid('id')
      .primaryKey()
      .$default(() => uuidv7()),
    createdAt: timestamp('created_at', { withTimezone: true }).notNull().defaultNow(),
    updatedAt: timestamp('updated_at', { withTimezone: true }).notNull().defaultNow(),
    deletedAt: timestamp('deleted_at', { withTimezone: true }),
    spaceId: uuid('space_id').notNull(),
    creatorId: uuid('creator_id').notNull(),
    type: varchar('type', {
      length: 32,
      enum: getEnumValues(SnipType),
    }).notNull(),
    from: varchar('from', {
      length: 32,
      enum: getEnumValues(SnipFrom),
    }).notNull(),
    status: varchar('status', {
      length: 32,
      enum: getEnumValues(SnipStatus),
    }),
    // 网页相关元信息
    webpageUrl: varchar('webpage_url', { length: 2048 }),
    webpageNormalizedUrl: varchar('webpage_normalized_url', { length: 2048 }),
    webpageTitle: varchar('webpage_title', { length: 2048 }),
    webpageDescription: varchar('webpage_description'),
    webpageSiteName: varchar('webpage_site_name', { length: 255 }),
    webpageSiteHost: varchar('webpage_site_host', { length: 255 }),
    webpageSiteFaviconUrl: varchar('webpage_site_favicon_url', { length: 2048 }),
    // 文件相关元信息
    fileName: varchar('file_name', { length: 2048 }),
    fileMimeType: varchar('file_mime_type', { length: 255 }),
    fileSize: integer('file_size'),
    fileStorageUrl: varchar('file_storage_url', { length: 2048 }),
    fileOriginalUrl: varchar('file_original_url', { length: 2048 }),
    // 选区相关元信息
    parentId: uuid('parent_id'),
    selection: text('selection'),
    annotationRaw: text('annotation_raw'),
    annotationPlain: text('annotation_plain'),
    // 解析出来的信息
    title: varchar('title', { length: 2048 }),
    authors: jsonb('authors'),
    heroImageUrl: varchar('hero_image_url', { length: 2048 }),
    publishedAt: timestamp('published_at', { withTimezone: true }),
    // 主体内容
    contentFormat: varchar('content_format', { length: 32 }),
    contentRaw: text('content_raw'),
    contentPlain: text('content_plain'),
    contentLanguage: varchar('content_language', { length: 32 }),
    // 主要媒体播放地址
    playUrl: varchar('play_url', { length: 2048 }),
    extra: text('extra'),
    visibility: varchar('visibility', {
      length: 10,
      enum: getEnumValues(Visibility),
    })
      .notNull()
      .default('private'),
  },
  (t) => {
    return {
      a: index('idx_snips_deleted_at_space_id').on(t.deletedAt, t.spaceId),
      b: index('idx_snips_deleted_at_space_id_from').on(t.deletedAt, t.spaceId, t.from),
      c: index('idx_snips_deleted_at_space_id_type').on(t.deletedAt, t.spaceId, t.type),
    };
  },
);

export const systemConfigs = pgTable(
  'system_configs',
  {
    id: uuid('id')
      .primaryKey()
      .$default(() => uuidv7()),
    createdAt: timestamp('created_at', { withTimezone: true }).notNull().defaultNow(),
    updatedAt: timestamp('updated_at', { withTimezone: true }).notNull().defaultNow(),
    deletedAt: timestamp('deleted_at', { withTimezone: true }),
    creatorId: uuid('creator_id').notNull(),
    modifierId: uuid('modifier_id').notNull(),
    key: varchar('key', { length: 255 }).notNull(),
    value: text('value').notNull(),
  },
  (t) => {
    return {
      a: uniqueIndex('system_configs_key_idx').on(t.key).where(isNull(t.deletedAt)),
    };
  },
);

export const userPreferences = pgTable('user_preferences', {
  id: uuid('id').primaryKey(),
  createdAt: timestamp('created_at', { withTimezone: true }).notNull().defaultNow(),
  updatedAt: timestamp('updated_at', { withTimezone: true }).notNull().defaultNow(),
  displayLanguage: varchar('display_language', {
    length: 255,
    enum: getEnumValues(DisplayLanguageEnum),
  }).notNull(),
  aiResponseLanguage: varchar('ai_response_language', {
    length: 255,
    enum: getEnumValues(AILanguageEnum),
  }).notNull(),
  ai2ndResponseLanguage: varchar('ai_2nd_response_language', {
    length: 255,
    enum: getEnumValues(AILanguageEnum),
  })
    .default(AILanguageEnum.system)
    .notNull(),
  enableBilingual: boolean('enable_bilingual').default(true).notNull(),
  detectedLanguage: varchar('detected_language', {
    length: 255,
    enum: getEnumValues(LanguageEnum),
  }),
});

export const usageRecords = pgTable(
  'usage_records',
  {
    id: uuid('id')
      .primaryKey()
      .$default(() => uuidv7()),
    createdAt: timestamp('created_at', { withTimezone: true }).notNull().defaultNow(),
    deletedAt: timestamp('deleted_at', { withTimezone: true }),
    spaceId: uuid('space_id').notNull(),
    userId: uuid('user_id').notNull().default('019222d7-8450-7a24-ae08-c67e6a21b31f'),
    resource: varchar('resource', {
      enum: getEnumValues(QuotaResourceEnum),
    }).notNull(),
    amount: bigint('amount', { mode: 'number' }).notNull(),
    extra: text('extra'),
  },
  (t) => {
    return {
      a: index('usage_records_deleted_at_space_id_resource_idx').on(
        t.deletedAt,
        t.spaceId,
        t.resource,
      ),
    };
  },
);

export const actions = pgTable(
  'actions',
  {
    id: uuid('id')
      .primaryKey()
      .$default(() => uuidv7()),
    createdAt: timestamp('created_at', { withTimezone: true }).notNull().defaultNow(),
    updatedAt: timestamp('updated_at', { withTimezone: true }).notNull().defaultNow(),
    deletedAt: timestamp('deleted_at', { withTimezone: true }),
    name: varchar('name', { length: 255 }).notNull().unique(),
    description: varchar('description', { length: 2048 }),
    type: varchar('type', {
      length: 32,
      enum: getEnumValues(ActionTypeEnum),
    }).notNull(),
    status: varchar('status', {
      length: 32,
      enum: getEnumValues(ActionStatusEnum),
    }).notNull(),
    llmPromptName: varchar('llm_prompt_name', { length: 255 }),
    resultBlockType: varchar('result_block_type', {
      length: 32,
      enum: getEnumValues(SnipFeatureEnum),
    }),
    resultBlockFormat: varchar('result_block_format', {
      length: 32,
      enum: getEnumValues(ContentFormatEnum),
    }),
    spaceId: uuid('space_id'),
  },
  (table) => {
    return {
      actionsName: uniqueIndex('uk_actions_name').on(table.name).where(isNull(table.deletedAt)),
      space: index('idx_actions_space_id').on(table.spaceId),
    };
  },
);

export const blocks = pgTable(
  'blocks',
  {
    id: uuid('id')
      .primaryKey()
      .$default(() => uuidv7()),
    createdAt: timestamp('created_at', { withTimezone: true }).notNull().defaultNow(),
    updatedAt: timestamp('updated_at', { withTimezone: true }).notNull().defaultNow(),
    deletedAt: timestamp('deleted_at', { withTimezone: true }),
    snipId: uuid('snip_id'),
    boardId: uuid('board_id'),
    actionId: uuid('action_id'),
    type: varchar('type', {
      length: 32,
      enum: getEnumValues(SnipFeatureEnum),
    }),
    relatedSnipId: uuid('related_snip_id'),
    display: varchar('display', {
      length: 32,
      enum: getEnumValues(BlockDisplayEnum),
    })
      .notNull()
      .default(BlockDisplayEnum.SHOW),
    currentContentId: uuid('current_content_id'),
    originUrl: varchar('origin_url', { length: 2048 }),
  },
  (table) => {
    return {
      blocksDeletedAt: index('idx_blocks_deleted_at').on(table.deletedAt),
      blocksSnipId: index('idx_blocks_snip_id').on(table.snipId),
      blocksUrl: index('idx_blocks_url').on(table.originUrl),
    };
  },
);

export const placements = pgTable(
  'placements',
  {
    id: uuid('id')
      .primaryKey()
      .$default(() => uuidv7()),
    createdAt: timestamp('created_at', { withTimezone: true }).notNull().defaultNow(),
    updatedAt: timestamp('updated_at', { withTimezone: true }).notNull().defaultNow(),
    deletedAt: timestamp('deleted_at', { withTimezone: true }),
    rank: doublePrecision('rank').notNull(),
    height: varchar('height', {
      length: 255,
      enum: getEnumValues(BlockHeightEnum),
    }),
    snipId: uuid('snip_id').default(sql`null`).$type<string | null>(),
    blockId: uuid('block_id').default(sql`null`).$type<string | null>(),
  },
  (table) => {
    return {
      placementsRank: index('idx_placements_rank').on(table.rank, table.updatedAt),
      placementsBlockId: index('idx_placements_block_id').on(table.blockId),
      placementsSnipBlock: index('idx_placements_snip_block').on(
        table.deletedAt,
        table.snipId,
        table.blockId,
      ),
    };
  },
);

export const contents = pgTable(
  'contents',
  {
    id: uuid('id')
      .primaryKey()
      .$default(() => uuidv7()),
    createdAt: timestamp('created_at', { withTimezone: true }).notNull().defaultNow(),
    updatedAt: timestamp('updated_at', { withTimezone: true }).notNull().defaultNow(),
    deletedAt: timestamp('deleted_at', { withTimezone: true }),
    format: varchar('format', {
      length: 32,
      enum: getEnumValues(ContentFormatEnum),
    }).notNull(),
    raw: text('raw'),
    plain: text('plain'),
    language: varchar('language', {
      length: 32,
      enum: getEnumValues(LanguageEnum),
    }),
    status: varchar('status', {
      length: 32,
      enum: getEnumValues(ProcessStatusEnum),
    }).notNull(),
    blockId: uuid('block_id'),
    snipId: uuid('snip_id'),
    traceId: varchar('trace_id', {
      length: 64,
    }),
  },
  (table) => {
    return {
      contentsDeletedAt: index('idx_contents_deleted_at').on(table.deletedAt),
      contentsBlockId: index('idx_contents_block_id').on(table.blockId),
      contentsSnipId: index('idx_contents_snip_id').on(table.snipId),
    };
  },
);

export const userCustomerRelations = pgTable(
  'user_customer_relations',
  {
    createdAt: timestamp('created_at', { withTimezone: true }).notNull().defaultNow(),
    userId: uuid('user_id').notNull().unique(),
    customerId: varchar('customer_id').notNull(),
  },
  (t) => {
    return {
      a: uniqueIndex('idx_user_id').on(t.userId),
    };
  },
);

export const chats = pgTable(
  'chats',
  {
    id: uuid('id')
      .primaryKey()
      .$default(() => uuidv7()),
    createdAt: timestamp('created_at', { withTimezone: true }).notNull().defaultNow(),
    updatedAt: timestamp('updated_at', { withTimezone: true }).notNull().defaultNow(),
    deletedAt: timestamp('deleted_at', { withTimezone: true }),
    creatorId: uuid('creator_id').notNull(),
    originType: varchar('origin_type', {
      length: 32,
      enum: getEnumValues(ChatOriginTypeEnum),
    }).notNull(),
    originId: uuid('origin_id'),
    title: varchar('title', { length: 255 }),
    originUrl: varchar('origin_url', { length: 2048 }),
    context: text('context'),
    essence: text('essence'),
    essenceLastUpdatedAt: timestamp('essence_last_updated_at', {
      withTimezone: true,
    }),
    essenceTraceId: varchar('essence_trace_id', {
      length: 64,
    }),
    boardId: uuid('board_id'),
    mode: varchar('mode', {
      length: 32,
      enum: getEnumValues(ChatModeEnum),
    }).default(ChatModeEnum.CHAT),
    assistantId: uuid('assistant_id'),
  },
  (table) => {
    return {
      originAndCreator: index('idx_origin_and_creator').on(
        table.deletedAt,
        table.creatorId,
        table.mode,
        table.originType,
        table.originId,
        table.originUrl,
      ),
      boardAndCreator: index('idx_board_and_creator').on(
        table.deletedAt,
        table.creatorId,
        table.mode,
        table.boardId,
      ),
      creator: index('idx_creator').on(table.deletedAt, table.creatorId, table.mode),
      assistant: index('idx_assistant').on(
        table.deletedAt,
        table.originType,
        table.originId,
        table.mode,
        table.assistantId,
      ),
    };
  },
);

export const messages = pgTable(
  'chat_messages',
  {
    id: uuid('id')
      .primaryKey()
      .$default(() => uuidv7()),
    createdAt: timestamp('created_at', { withTimezone: true }).notNull().defaultNow(),
    updatedAt: timestamp('updated_at', { withTimezone: true }).notNull().defaultNow(),
    deletedAt: timestamp('deleted_at', { withTimezone: true }),
    chatId: uuid('chat_id').notNull(),
    role: varchar('role', {
      length: 32,
      enum: getEnumValues(MessageRoleEnum),
    }).notNull(),
    context: jsonb('context'),
    content: text('content'),
    status: varchar('status', {
      length: 32,
      enum: getEnumValues(MessageStatusEnum),
    }).default(MessageStatusEnum.QUEUED),
    traceId: varchar('trace_id', {
      length: 64,
    }),
    reasoning: text('reasoning'),
    model: varchar('model', { length: 255 }),
    error: jsonb('error'),
    tools: jsonb('tools'),
    command: jsonb('command'),
    mode: varchar('mode', {
      length: 32,
      enum: getEnumValues(MessageModeEnum),
    }),
    shortcut: jsonb('shortcut').$type<{
      id: string;
      name: string;
    }>(),
  },
  (table) => {
    return {
      chat: index('idx_chat').on(table.deletedAt, table.chatId, table.role),
      traceId: index('idx_trace_id').on(table.traceId),
    };
  },
);

export const completionBlocks = pgTable(
  'completion_blocks',
  {
    id: uuid('id')
      .primaryKey()
      .$default(() => uuidv7()),
    createdAt: timestamp('created_at', { withTimezone: true }).notNull().defaultNow(),
    updatedAt: timestamp('updated_at', { withTimezone: true }).notNull().defaultNow(),
    deletedAt: timestamp('deleted_at', { withTimezone: true }),
    type: varchar('type', {
      length: 32,
      enum: getEnumValues(CompletionBlockTypeEnum),
    }).notNull(),
    status: varchar('status', {
      length: 32,
      enum: getEnumValues(CompletionBlockStatusEnum),
    }).notNull(),
    messageId: uuid('message_id'),
    data: text('data'),
    toolId: varchar('tool_id', {
      length: 256,
    }),
    toolName: varchar('tool_name', {
      length: 256,
    }),
    toolArguments: jsonb('tool_arguments'),
    toolResult: jsonb('tool_result'),
    toolResponse: text('tool_response'),
    toolGenerateElapsedMs: integer('tool_generate_elapsed_ms'),
    toolExecuteElapsedMs: integer('tool_execute_elapsed_ms'),
    extra: jsonb('extra'),
  },
  (table) => {
    return {
      messageId: index('idx_message_id').on(table.deletedAt, table.messageId),
      updatedAt: index('idx_updated_at').on(table.updatedAt, table.messageId),
    };
  },
);

export const snipThoughtRelations = pgTable(
  'snip_thought_relations',
  {
    id: uuid('id')
      .primaryKey()
      .$default(() => uuidv7()),
    createdAt: timestamp('created_at', { withTimezone: true }).notNull().defaultNow(),
    updatedAt: timestamp('updated_at', { withTimezone: true }).notNull().defaultNow(),
    deletedAt: timestamp('deleted_at', { withTimezone: true }),
    snipId: uuid('snip_id').notNull(),
    thoughtId: uuid('thought_id').notNull(),
  },
  (t) => {
    return {
      a: uniqueIndex('idx_snip_thought_relations_deleted_at_snip_id_thought_id')
        .on(t.snipId, t.thoughtId)
        .where(isNull(t.deletedAt)),
      b: index('idx_snip_thought_relations_deleted_at_thought_id_snip_id').on(
        t.deletedAt,
        t.thoughtId,
        t.snipId,
      ),
    };
  },
);

export const shortLinks = pgTable(
  'short_links',
  {
    id: uuid('id')
      .primaryKey()
      .$default(() => uuidv7()),
    createdAt: timestamp('created_at', { withTimezone: true }).notNull().defaultNow(),
    updatedAt: timestamp('updated_at', { withTimezone: true }).notNull().defaultNow(),
    shortId: varchar('short_id', { length: 14 }).notNull(),
    entityType: varchar('entity_type', {
      length: 20,
      enum: getEnumValues(EntityType),
    }).notNull(),
    entityId: uuid('entity_id').notNull(),
    active: boolean('active').notNull().default(true),
  },
  (t) => {
    return {
      a: uniqueIndex('short_links_short_id_idx').on(t.shortId),
      b: uniqueIndex('short_links_entity_idx').on(t.entityType, t.entityId),
    };
  },
);

export const assistants = pgTable('assistants', {
  id: uuid('id')
    .primaryKey()
    .$default(() => uuidv7()),
  createdAt: timestamp('created_at', { withTimezone: true }).notNull().defaultNow(),
  updatedAt: timestamp('updated_at', { withTimezone: true }).notNull().defaultNow(),
  deletedAt: timestamp('deleted_at', { withTimezone: true }),
  creatorId: uuid('creator_id').notNull(),
  name: varchar('name', { length: 255 }).notNull(),
  description: varchar('description', { length: 2048 }).notNull(),
  instructions: text('instructions'),
  type: varchar('type', {
    length: 20,
    enum: getEnumValues(AssistantTypeEnum),
  })
    .notNull()
    .default(AssistantTypeEnum.PROMPT_EXECUTER),
  model: varchar('model', { length: 255 }),
  temperature: doublePrecision('temperature'),
  iconType: varchar('icon_type', {
    length: 32,
    enum: getEnumValues(AssistantIconTypeEnum),
  }),
  iconValue: varchar('icon_value', { length: 32 }),
  iconBgColor: varchar('icon_bg_color', { length: 32 }),
  visibility: varchar('visibility', {
    length: 20,
    enum: getEnumValues(AssistantVisibilityEnum),
  }).notNull(),
  tools: jsonb('tools').default(sql`'{}'::jsonb`),
  runMode: varchar('run_mode', {
    length: 20,
    enum: getEnumValues(AssistantRunModeEnum),
  })
    .notNull()
    .default(AssistantRunModeEnum.MANUAL),
});

export const spaceAssistantConfigs = pgTable(
  'space_assistant_configs',
  {
    id: uuid('id')
      .primaryKey()
      .$default(() => uuidv7()),
    createdAt: timestamp('created_at', { withTimezone: true }).notNull().defaultNow(),
    updatedAt: timestamp('updated_at', { withTimezone: true }).notNull().defaultNow(),
    deletedAt: timestamp('deleted_at', { withTimezone: true }),
    spaceId: uuid('space_id').notNull(),
    assistantId: uuid('assistant_id').notNull(),
    rank: varchar('rank', { length: 20 }).notNull(),
    enabled: boolean('enabled').notNull().default(true),
  },
  (t) => {
    return {
      a: uniqueIndex('idx_space_id_assistant_id')
        .on(t.spaceId, t.assistantId)
        .where(isNull(t.deletedAt)),
    };
  },
);

export const assistantContents = pgTable(
  'assistant_contents',
  {
    id: uuid('id')
      .primaryKey()
      .$default(() => uuidv7()),
    createdAt: timestamp('created_at', { withTimezone: true }).notNull().defaultNow(),
    updatedAt: timestamp('updated_at', { withTimezone: true }).notNull().defaultNow(),
    deletedAt: timestamp('deleted_at', { withTimezone: true }),
    assistantId: uuid('assistant_id').notNull(),
    entityType: varchar('entity_type', { length: 20 }).notNull(),
    entityId: uuid('entity_id').notNull(),
    content: text('content'),
    traceId: varchar('trace_id', { length: 64 }),
  },
  (t) => {
    return {
      a: uniqueIndex('idx_assistant_id_entity_type_entity_id')
        .on(t.assistantId, t.entityType, t.entityId)
        .where(isNull(t.deletedAt)),
    };
  },
);

export const playlistItems = pgTable('playlist_items', {
  id: uuid('id')
    .primaryKey()
    .$default(() => uuidv7()),
  createdAt: timestamp('created_at', { withTimezone: true }).notNull().defaultNow(),
  updatedAt: timestamp('updated_at', { withTimezone: true }).notNull().defaultNow(),
  deletedAt: timestamp('deleted_at', { withTimezone: true }),
  creatorId: uuid('creator_id').notNull(),
  spaceId: uuid('space_id').notNull(),
  boardId: uuid('board_id'),
  entityType: varchar('entity_type', { length: 20 }),
  entityId: uuid('entity_id'),
  title: varchar('title', { length: 255 }).notNull(),
  playUrl: varchar('play_url', { length: 255 }).notNull(),
  duration: doublePrecision('duration'),
  status: varchar('status', {
    length: 20,
    enum: getEnumValues(PlaylistItemStatusEnum),
  }).notNull(),
  albumCoverUrl: varchar('album_cover_url', { length: 255 }),
  transcript: text('transcript'),
  rank: varchar('rank', { length: 255 }),
  playbackProgress: doublePrecision('playback_progress'),
});

export const subscriptions = pgTable(
  'subscriptions',
  {
    id: uuid('id')
      .primaryKey()
      .$default(() => uuidv7()),
    createdAt: timestamp('created_at', { withTimezone: true }).notNull().defaultNow(),
    updatedAt: timestamp('updated_at', { withTimezone: true }).notNull().defaultNow(),
    deletedAt: timestamp('deleted_at', { withTimezone: true }),

    // 关联空间
    spaceId: uuid('space_id').notNull(),

    // 内部产品定义
    productTier: varchar('product_tier', {
      length: 32,
      enum: getEnumValues(ProductTier),
    })
      .notNull()
      .default(ProductTier.FREE),
    billingInterval: varchar('billing_interval', {
      length: 32,
      enum: getEnumValues(BillingInterval),
    })
      .notNull()
      .default(BillingInterval.NONE),
    status: varchar('status', {
      length: 32,
      enum: getEnumValues(SubscriptionStatus),
    })
      .notNull()
      .default(SubscriptionStatus.ACTIVE),

    // 基于 Stripe 计费周期模型的字段（适用于所有订阅类型）
    renewCycleAnchor: timestamp('renew_cycle_anchor', { withTimezone: true }),
    currentPeriodStart: timestamp('current_period_start', { withTimezone: true }),
    currentPeriodEnd: timestamp('current_period_end', { withTimezone: true }),
    cancelAtPeriodEnd: boolean('cancel_at_period_end').notNull().default(false),

    // 续期时更新的字段
    renewChangeProductTier: varchar('renew_change_product_tier', {
      length: 32,
      enum: getEnumValues(ProductTier),
    }),
    renewChangeBillingInterval: varchar('renew_change_billing_interval', {
      length: 32,
      enum: getEnumValues(BillingInterval),
    }),

    // 外部系统集成
    provider: varchar('provider', {
      length: 32,
      enum: getEnumValues(SubscriptionProvider),
    })
      .notNull()
      .default(SubscriptionProvider.INTERNAL),
    metadata: jsonb('metadata'),
  },
  (t) => {
    return {
      // 每个空间只能有一个活跃订阅
      a: uniqueIndex('idx_subscriptions_space_id').on(t.spaceId).where(isNull(t.deletedAt)),
      b: index('idx_subscriptions_status').on(t.status),
    };
  },
);

export const creditAccounts = pgTable(
  'credit_accounts',
  {
    id: uuid('id')
      .primaryKey()
      .$default(() => uuidv7()),
    createdAt: timestamp('created_at', { withTimezone: true }).notNull().defaultNow(),
    updatedAt: timestamp('updated_at', { withTimezone: true }).notNull().defaultNow(),

    // 业务主键，与 space 一对一关系
    spaceId: uuid('space_id').notNull(),

    // 核心积分字段（极简设计）
    monthlyBalance: integer('monthly_balance').notNull().default(0), // 月度积分余额

    // 周期管理字段（新设计）
    refreshCycleAnchor: timestamp('refresh_cycle_anchor', { withTimezone: true })
      .notNull()
      .defaultNow(), // 刷新周期锚点时间
    currentPeriodStart: timestamp('current_period_start', { withTimezone: true })
      .notNull()
      .defaultNow(), // 当前积分周期开始时间
    currentPeriodEnd: timestamp('current_period_end', { withTimezone: true })
      .notNull()
      .defaultNow(), // 当前积分周期结束时间
    productTier: varchar('product_tier', {
      length: 32,
      enum: getEnumValues(ProductTier),
    })
      .notNull()
      .default(ProductTier.FREE), // 当前权益等级
    metadata: jsonb('metadata').default('{}'),
  },
  (t) => ({
    // 业务主键唯一索引
    a: uniqueIndex('idx_credit_accounts_space_id').on(t.spaceId),
    // 查询即将到期账户的索引
    b: index('idx_credit_accounts_current_period_end').on(t.currentPeriodEnd),
  }),
);

export const creditTransactions = pgTable(
  'credit_transactions',
  {
    id: uuid('id')
      .primaryKey()
      .$default(() => uuidv7()),
    createdAt: timestamp('created_at', { withTimezone: true }).notNull().defaultNow(),

    // 关联字段
    creditAccountId: uuid('credit_account_id').notNull(), // 关联积分账户
    spaceId: uuid('space_id').notNull(), // 冗余字段，支持跨账户查询

    // 交易分类（简化设计）
    type: varchar('type', {
      length: 20,
      enum: getEnumValues(TransactionType),
    }).notNull(),
    creditType: varchar('credit_type', {
      length: 20,
      enum: getEnumValues(CreditType),
    })
      .notNull()
      .default(CreditType.MONTHLY),

    // 金额和余额快照
    amount: integer('amount').notNull(), // 变动数量：grant 为正数，consume/forfeit 为负数
    balanceBefore: integer('balance_before').notNull(), // 交易前余额
    balanceAfter: integer('balance_after').notNull(), // 交易后余额

    // 业务信息
    reason: varchar('reason', { length: 255 }).notNull(), // 交易原因（简短描述）
    metadata: jsonb('metadata').default('{}'), // 详细信息：Token 使用、成本计算、上下文等
  },
  (t) => ({
    // 主要查询索引
    a: index('idx_credit_transactions_credit_account_id_created_at').on(
      t.creditAccountId,
      t.createdAt,
    ),
    b: index('idx_credit_transactions_space_id_created_at').on(t.spaceId, t.createdAt),
    // 类型筛选查询优化索引
    c: index('idx_credit_transactions_account_type_created_at').on(
      t.creditAccountId,
      t.type,
      t.createdAt,
    ),
  }),
);

// Relations
export const snipRelations = relations(snips, ({ one }) => ({
  space: one(spaces, {
    fields: [snips.spaceId],
    references: [spaces.id],
  }),
}));

export const actionRelations = relations(actions, ({ one, many }) => ({
  blocks: many(blocks),
  space: one(spaces, {
    fields: [actions.spaceId],
    references: [spaces.id],
  }),
}));

export const blockRelations = relations(blocks, ({ one, many }) => ({
  action: one(actions, {
    fields: [blocks.actionId],
    references: [actions.id],
  }),
  snip: one(snips, {
    fields: [blocks.snipId],
    references: [snips.id],
  }),
  placements: many(placements),
  contents: many(contents),
}));

export const placementRelations = relations(placements, ({ one }) => ({
  snip: one(snips, {
    fields: [placements.snipId],
    references: [snips.id],
  }),
  block: one(blocks, {
    fields: [placements.blockId, placements.snipId],
    references: [blocks.id, blocks.snipId],
  }),
}));

export const contentRelations = relations(contents, ({ one }) => ({
  snip: one(snips, {
    fields: [contents.snipId],
    references: [snips.id],
  }),
  block: one(blocks, {
    fields: [contents.blockId],
    references: [blocks.id],
  }),
}));

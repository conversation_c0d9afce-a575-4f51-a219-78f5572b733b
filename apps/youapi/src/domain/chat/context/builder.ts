import { forwardRef, Inject, Injectable } from '@nestjs/common';
import { type AtReference, MessageAtReferenceTypeEnum, MessageRoleEnum } from '@/common/types';
import { estimateTokens, runConcurrently } from '@/common/utils';
import { type Message, SourceTypeEnum } from '@/domain/chat/types';
import {
  BoardContextBuilder,
  BoardGroupContextBuilder,
  ContextManager,
  SnipContextBuilder,
  ThoughtContextBuilder,
} from '@/domain/llm';
import { BaseContextBuilder } from '@/domain/llm/context/base';
import { SearchDomainService } from '@/domain/search';
import type { ContainerContentItem, MessageAnalyzer } from './analyzer';
import { LLMMessageAnalyzer } from './analyzer';
import type { ContentRetriever } from './retriever';
import { DefaultContentRetriever } from './retriever';
import {
  type AtReferenceForContextBuilding,
  type ContextBuildingChunk,
  type ContextBuildingFromReferencesRequest,
  type ContextBuildingRequest,
  type ContextBuildingResult,
  ContextBuildingStrategy,
  type RetrievedContent,
} from './types';

/**
 * Context builder that orchestrates the context building process
 */
@Injectable()
export class ContextBuilder {
  /**
   * Create a new context builder
   * @param analyzer Message analyzer
   * @param retriever Content retriever
   * @param searchDomain Search domain service
   */
  constructor(
    @Inject(forwardRef(() => LLMMessageAnalyzer))
    private analyzer: MessageAnalyzer,
    @Inject(forwardRef(() => DefaultContentRetriever))
    private retriever: ContentRetriever,
    @Inject(forwardRef(() => SearchDomainService))
    private searchDomain: SearchDomainService,
    @Inject(forwardRef(() => ContextManager))
    private contextManager: ContextManager,
    @Inject(forwardRef(() => SnipContextBuilder))
    private snipContextBuilder: SnipContextBuilder,
    @Inject(forwardRef(() => BoardContextBuilder))
    private boardContextBuilder: BoardContextBuilder,
    @Inject(forwardRef(() => ThoughtContextBuilder))
    private thoughtContextBuilder: ThoughtContextBuilder,
    @Inject(forwardRef(() => BoardGroupContextBuilder))
    private boardGroupContextBuilder: BoardGroupContextBuilder,
  ) {}

  /**
   * Map MessageAtReferenceTypeEnum to SourceTypeEnum
   * @param refType Reference type
   * @returns Source type or null if not mappable
   */
  private mapToSourceType(refType: MessageAtReferenceTypeEnum): SourceTypeEnum | null {
    switch (refType) {
      case MessageAtReferenceTypeEnum.BOARD_GROUP:
        return SourceTypeEnum.BOARD_GROUP;
      case MessageAtReferenceTypeEnum.BOARD:
        return SourceTypeEnum.BOARD;
      case MessageAtReferenceTypeEnum.SNIP:
        return SourceTypeEnum.SNIP;
      case MessageAtReferenceTypeEnum.THOUGHT:
        return SourceTypeEnum.THOUGHT;
      default:
        return null;
    }
  }

  /**
   * 为消息构建上下文
   * @param request Context building request
   * @returns Context building result
   */
  async buildContext(request: ContextBuildingRequest): Promise<ContextBuildingResult> {
    const { trace } = request;

    // 支持直接传入 at_references 和 messages，或者从 chat 中提取
    let messages: Message[];
    let atReferences: AtReference[];

    if (request.at_references) {
      messages = [];
      atReferences = request.at_references;
    } else if (request.messages) {
      // 直接使用提供的 messages 和 at_references
      messages = request.messages;
      atReferences =
        request.messages.findLast((m) => m.role === MessageRoleEnum.USER)?.at_references || [];
    } else if (request.chat) {
      // 从 chat 中提取 messages 和 at_references（原有逻辑）
      const chat = request.chat;
      messages = chat.messages || [];
      if (messages.length === 0) {
        throw new Error('No messages found in chat');
      }

      const lastMessage = messages.findLast((m) => m.role === MessageRoleEnum.USER);
      if (!lastMessage) {
        throw new Error('Last message in chat is not from user');
      }

      atReferences = lastMessage.at_references || [];
    } else {
      throw new Error("Either 'chat' or both 'messages' and 'at_references' must be provided");
    }

    return this.buildContextFromReferences({
      messages,
      at_references: atReferences,
      availableTokens: request.availableTokens,
      userId: request.userId,
      trace,
    });
  }

  /**
   * Build context from references and return only the final result
   * @param request Context building request
   * @returns Context building result
   */
  async buildContextResult(
    request: ContextBuildingFromReferencesRequest,
  ): Promise<ContextBuildingResult> {
    const result = await this.buildContextFromReferences(request);
    return result;
  }

  async buildContextFromReferences(
    request: ContextBuildingFromReferencesRequest,
  ): Promise<ContextBuildingResult> {
    const { messages = [], trace, at_references: atReferences = [] } = request;

    // 0. 如果是整个 library 级别的召回，走单独的逻辑
    const isLibraryLevel = atReferences.every((ref) => ref.entity_type === 'library');
    if (isLibraryLevel) {
      const { searchKeywords, contextType } = request.analysis || {};
      // 如果模型判断需要召回整个 library 里的内容，则无能为力
      if (contextType === 'full' || !searchKeywords || searchKeywords.length === 0) {
        return {
          atReferencesRetrieved: [],
          totalTokensUsed: 0,
          strategy: ContextBuildingStrategy.NO_CONTEXT,
        };
      }

      // 否则走搜索逻辑
      const results = await this.searchDomain.hybridSearch({
        query: searchKeywords,
        userId: request.userId,
        boardIds: [],
      });

      // 将搜索结果转换为上下文构建结果
      if (!results.hits || results.hits.length === 0) {
        return {
          atReferencesRetrieved: [],
          totalTokensUsed: 0,
          strategy: ContextBuildingStrategy.SEMANTIC_SEARCH,
          searchKeywords,
        };
      }

      // Define types for the search hit document with required fields
      type SearchHitDocument = {
        id?: string;
        docType?: string;
        content_0?: string;
        title?: string;
        [key: string]: unknown;
      };

      type SearchHighlight = {
        [field: string]: { snippet?: string } | Array<{ snippet?: string }> | unknown;
      };

      let totalTokensUsed = 0;
      const atReferencesRetrieved: Array<{
        atReferenceId: string;
        atReferenceType: string;
        chunks: Array<ContextBuildingChunk>;
      }> = [];

      // Group hits by entity (id + type)
      const entityGroups = new Map<string, typeof results.hits>();
      for (const hit of results.hits) {
        const doc = hit.document as SearchHitDocument;
        const entityKey = `${doc?.id}-${doc?.docType}`;
        if (!entityGroups.has(entityKey)) {
          entityGroups.set(entityKey, []);
        }
        entityGroups.get(entityKey)!.push(hit);
      }

      // Convert each entity group to atReferenceRetrieved
      for (const [entityKey, entityHits] of entityGroups.entries()) {
        const firstHit = entityHits[0];
        const firstDoc = firstHit.document as SearchHitDocument;
        if (!firstDoc?.id || !firstDoc?.docType) continue;

        const chunks: ContextBuildingChunk[] = [];
        let entityTokenCount = 0;

        for (const hit of entityHits) {
          const doc = hit.document as SearchHitDocument;

          // Extract chunk content from highlight or fallback to document content
          let chunkContent = '';
          if (hit.highlight) {
            const highlightValues = Object.values(hit.highlight as SearchHighlight);
            chunkContent = highlightValues
              .map((h) => {
                if (Array.isArray(h)) {
                  return h[0]?.snippet || '';
                } else if (h && typeof h === 'object' && 'snippet' in h) {
                  return (h as { snippet?: string }).snippet || '';
                }
                return '';
              })
              .filter(Boolean)
              .join('\n\n');
          }

          if (!chunkContent && doc?.content_0) {
            chunkContent = doc.content_0.slice(0, 500); // Fallback with truncation
          }

          if (
            chunkContent &&
            entityTokenCount + estimateTokens(chunkContent) <= request.availableTokens
          ) {
            const tokenUsed = estimateTokens(chunkContent);
            entityTokenCount += tokenUsed;
            totalTokensUsed += tokenUsed;

            chunks.push({
              id: doc.id!,
              type: doc.docType!,
              chunk: chunkContent,
              distance: 1 - (hit.text_match || 0), // Convert similarity back to distance
              tokenUsed,
              title: doc?.title,
            });
          }

          // Stop if we've reached the token limit
          if (totalTokensUsed >= request.availableTokens) {
            break;
          }
        }

        if (chunks.length > 0) {
          atReferencesRetrieved.push({
            atReferenceId: firstDoc.id,
            atReferenceType: firstDoc.docType,
            chunks,
          });
        }

        // Stop if we've reached the token limit
        if (totalTokensUsed >= request.availableTokens) {
          break;
        }
      }

      return {
        atReferencesRetrieved,
        totalTokensUsed,
        strategy: ContextBuildingStrategy.SEMANTIC_SEARCH,
        searchKeywords,
      };
    }

    // 1. Check if all at-references are simple entities and try to resolve them fully
    if (atReferences.length > 0) {
      const allSimpleEntities = atReferences.every((ref) => this.isSimpleEntity(ref));

      if (allSimpleEntities) {
        // TypeScript now knows that all refs in atReferences are AtReference with simple entity types
        const simpleReferences = atReferences as AtReference[];
        try {
          trace?.event({
            name: 'attempting-simple-entities-full-content',
            metadata: {
              reference_count: atReferences.length,
              available_tokens: request.availableTokens,
            },
          });

          // 获取所有简单实体的完整内容
          const fullContents = await Promise.all(
            simpleReferences.map((ref) =>
              this.getFullEntityContent(ref.entity_id, ref.entity_type),
            ),
          );

          const totalTokenCount = fullContents.reduce(
            (sum, content) => sum + content.tokenCount,
            0,
          );

          if (totalTokenCount <= request.availableTokens) {
            trace?.event({
              name: 'simple-entities-full-content-fit',
              metadata: {
                total_token_count: totalTokenCount,
                available_tokens: request.availableTokens,
              },
            });

            return {
              atReferencesRetrieved: fullContents.map((content, index) => ({
                atReferenceId: content.atReferenceId,
                atReferenceType: content.atReferenceType,
                chunks: [
                  {
                    id: content.atReferenceId,
                    type: content.atReferenceType,
                    chunk: content.content,
                    distance: 0,
                    tokenUsed: content.tokenCount,
                    title: simpleReferences[index]?.at_name || '',
                  },
                ],
              })),
              totalTokensUsed: totalTokenCount,
              strategy: ContextBuildingStrategy.FULL_CONTENT,
            };
          } else {
            trace?.event({
              name: 'simple-entities-full-content-exceeds-limit',
              metadata: {
                total_token_count: totalTokenCount,
                available_tokens: request.availableTokens,
              },
            });
          }
        } catch (error) {
          console.error('Error getting full content for simple entities:', error);
        }
      }
    }

    // 2. Prepare container contents if we have board group or board references
    const containerRefs = atReferences.filter(
      (ref): ref is AtReference =>
        this.isAtReference(ref) &&
        (ref.entity_type === MessageAtReferenceTypeEnum.BOARD_GROUP ||
          ref.entity_type === MessageAtReferenceTypeEnum.BOARD),
    );
    const boardRefs = atReferences.filter(
      (ref): ref is AtReference =>
        this.isAtReference(ref) && ref.entity_type === MessageAtReferenceTypeEnum.BOARD,
    );
    const finalContainerRefs = [...boardRefs, ...containerRefs];

    let containerContents: Record<string, ContainerContentItem[]> | undefined;
    if (finalContainerRefs.length > 0 && !request.quickMode) {
      const span = trace?.span({
        name: 'fetch-container-contents',
        input: finalContainerRefs,
      });
      containerContents = {};

      await Promise.all(
        finalContainerRefs.map(async (ref) => {
          try {
            const contentItems = await this.fetchContainerContentItems(
              ref.entity_id,
              ref.entity_type,
            );
            containerContents![ref.entity_id] = contentItems;
          } catch (error) {
            console.error(
              `Error fetching content items for ${ref.entity_type} ${ref.entity_id}:`,
              error,
            );
            containerContents![ref.entity_id] = [];
          }
        }),
      );

      span?.end({
        output: containerContents,
      });
    }

    // 3. Analyze the message with container contents or use provided analysis
    const analysis =
      request.analysis || (await this.analyzer.analyze(messages, containerContents, trace));

    // 4. If no context needed, return empty result
    if (!analysis.needsContext) {
      trace?.event({
        name: 'no-context-needed',
      });
      return {
        atReferencesRetrieved: [],
        totalTokensUsed: 0,
        strategy: ContextBuildingStrategy.NO_CONTEXT,
      };
    } else if (analysis.contextType === 'full' && containerRefs.length === 0) {
      trace?.event({
        name: 'full-content-sampling',
      });

      const validAtReferences = atReferences.filter(this.isAtReference.bind(this));
      const sampledContents = await this.getFullContentSamples(
        validAtReferences as AtReference[],
        request.availableTokens,
      );
      return {
        atReferencesRetrieved: sampledContents.map((content) => ({
          atReferenceId: content.atReferenceId,
          atReferenceType: content.atReferenceType,
          chunks: [
            {
              id: content.atReferenceId,
              type: content.atReferenceType,
              chunk: content.content,
              distance: 0,
              tokenUsed: content.tokenCount,
            },
          ],
        })),
        totalTokensUsed: sampledContents.reduce((sum, content) => sum + content.tokenCount, 0),
        strategy: ContextBuildingStrategy.FULL_CONTENT_SAMPLING,
      };
    }

    // 5. Retrieve content from references using semantic search
    const span = trace?.span({
      name: 'retrieve-content',
      input: {
        at_references: atReferences,
        search_keywords: analysis.searchKeywords,
      },
    });
    const validAtReferencesForRetrieval = atReferences.filter(this.isAtReference.bind(this));
    const retrievedContent = await this.retriever.retrieveContent(
      validAtReferencesForRetrieval as AtReference[],
      analysis.searchKeywords,
      request.userId,
      request.availableTokens,
      span,
    );
    span?.end({
      output: retrievedContent,
    });

    // 6. Calculate total tokens used
    const totalTokensUsed = retrievedContent.reduce((sum, content) => sum + content.tokenCount, 0);

    // 7. Return the result
    const result: ContextBuildingResult = {
      atReferencesRetrieved: retrievedContent.map((content) => ({
        atReferenceId: content.atReferenceId,
        atReferenceType: content.atReferenceType,
        chunks: content.chunks,
      })),
      totalTokensUsed,
      strategy: ContextBuildingStrategy.SEMANTIC_SEARCH,
      searchKeywords: analysis.searchKeywords || '',
    };

    return result;
  }

  /**
   * Check if an entity type is simple (can be retrieved in full)
   * @param entityType Entity type to check
   * @returns Whether the entity type is simple
   */
  private isSimpleEntityType(entityType: MessageAtReferenceTypeEnum): boolean {
    return (
      entityType === MessageAtReferenceTypeEnum.SNIP ||
      entityType === MessageAtReferenceTypeEnum.THOUGHT
    );
  }

  /**
   * Type guard to check if a reference is a proper AtReference with MessageAtReferenceTypeEnum
   * @param ref Reference to check
   * @returns Whether the reference is a proper AtReference
   */
  private isAtReference(ref: AtReferenceForContextBuilding): ref is AtReference {
    return ref.entity_type !== 'library' && 'entity_id' in ref;
  }

  /**
   * Type guard to check if a reference is a simple entity type
   * @param ref Reference to check
   * @returns Whether the reference is a simple entity
   */
  private isSimpleEntity(ref: AtReferenceForContextBuilding): ref is AtReference & {
    entity_type: MessageAtReferenceTypeEnum.SNIP | MessageAtReferenceTypeEnum.THOUGHT;
  } {
    return this.isAtReference(ref) && this.isSimpleEntityType(ref.entity_type);
  }

  /**
   * Get the full content of an entity
   * @param entityId Entity ID
   * @param entityType Entity type
   * @returns Retrieved content
   */
  async getFullEntityContent(
    entityId: string,
    entityType: MessageAtReferenceTypeEnum,
  ): Promise<RetrievedContent> {
    const contextBuilder = this.getSimpleEntityContextBuilder(entityType);
    const content = await contextBuilder.getContent(entityId, {
      max_tokens: Number.MAX_SAFE_INTEGER,
    });

    return {
      atReferenceId: entityId,
      atReferenceType: entityType,
      content,
      tokenCount: estimateTokens(content),
      chunks: [],
    };
  }

  private getSimpleEntityContextBuilder(entityType: MessageAtReferenceTypeEnum) {
    let contextBuilder: BaseContextBuilder;

    if (entityType === MessageAtReferenceTypeEnum.SNIP) {
      contextBuilder = this.snipContextBuilder;
    } else if (entityType === MessageAtReferenceTypeEnum.THOUGHT) {
      contextBuilder = this.thoughtContextBuilder;
    } else {
      throw new Error(`Unsupported entity type for full content: ${entityType}`);
    }
    return contextBuilder;
  }

  private async getFullContentSamples(
    atReferences: AtReference[],
    availableTokens: number,
  ): Promise<RetrievedContent[]> {
    const sampledContents = await runConcurrently(
      atReferences.map((ref) => () => {
        const contextBuilder = this.getSimpleEntityContextBuilder(ref.entity_type);
        return contextBuilder.getContent(ref.entity_id, {
          method: 'sample',
          max_tokens: Math.floor(availableTokens / atReferences.length),
        });
      }),
    );

    return sampledContents.map((content, index) => {
      const ref = atReferences[index];
      return {
        atReferenceId: ref.entity_id,
        atReferenceType: ref.entity_type,
        content: content || '',
        tokenCount: estimateTokens(content || ''),
        chunks: [],
      };
    });
  }

  private async fetchContainerContentItems(
    containerId: string,
    entityType: MessageAtReferenceTypeEnum,
  ): Promise<ContainerContentItem[]> {
    try {
      const sourceType = this.mapToSourceType(entityType);

      if (sourceType === SourceTypeEnum.BOARD_GROUP || sourceType === SourceTypeEnum.BOARD) {
        const contextObj = await this.contextManager.getContextObjectBySource({
          entity_id: containerId,
          entity_type: sourceType,
        });

        if (!contextObj) {
          return [];
        }

        const contentItems: ContainerContentItem[] = [];

        if (contextObj.title && typeof contextObj.title === 'string') {
          contentItems.push({
            type: entityType,
            title: contextObj.title,
          });
        }

        if ('sources' in contextObj && Array.isArray(contextObj.sources)) {
          contextObj.sources.forEach((source) => {
            if (source?.fields?.title && typeof source.fields.title === 'string') {
              contentItems.push({
                type: source.fields.type as MessageAtReferenceTypeEnum,
                title: source.fields.title,
              });

              if ('sources' in source.fields && Array.isArray(source.fields.sources)) {
                source.fields.sources.forEach((source) => {
                  if (source?.title && typeof source.title === 'string') {
                    contentItems.push({
                      type: source.type as MessageAtReferenceTypeEnum,
                      title: source.title,
                    });
                  }
                });
              }
            }
          });
        }

        return contentItems.filter((item) => item.title.length > 0);
      }

      const contextBuilder = this.boardGroupContextBuilder;
      const container = await contextBuilder.buildContextObject(containerId);

      if (container?.title) {
        return [
          {
            type: entityType,
            title: container.title,
          },
        ].filter((item) => item.title.length > 0);
      }

      return [];
    } catch (error) {
      console.error(
        `Error fetching container content items for ${entityType} ${containerId}:`,
        error,
      );
      return [];
    }
  }
}

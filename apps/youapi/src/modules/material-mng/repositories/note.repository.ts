import { Injectable, NotFoundException } from '@nestjs/common';
import { and, eq, inArray, isNull } from 'drizzle-orm';
import { SafeParse } from '@/common/utils';
import { DatabaseService } from '@/shared/db/database.service';
import { notes } from '@/shared/db/public.schema';
import { HTMLSelection, Note, NoteContent, NoteSource } from '../domain/note/models/note.entity';
import { NoteSourceEntityType } from '../domain/note/types/note.types';

export interface NoteFilter {
  spaceId: string;
  boardId?: string;
  entityType?: NoteSourceEntityType;
  entityId?: string;
}

// Database types (从 DAO 迁移)
type NoteDO = typeof notes.$inferSelect;
@Injectable()
export class NoteRepository {
  constructor(private readonly databaseService: DatabaseService) {}

  private get db() {
    return this.databaseService.db;
  }

  /**
   * 保存笔记聚合
   */
  async save(note: Note): Promise<void> {
    const noteDO = this.entityToDO(note);
    if (note.isNew) {
      // 插入新记录
      await this.db.insert(notes).values(noteDO);
      note.markAsExisting();
    } else {
      // 更新现有记录
      const { id, ...shouldUpdate } = noteDO;
      await this.db
        .update(notes)
        .set(shouldUpdate)
        .where(and(isNull(notes.deletedAt), eq(notes.id, id)));
    }
  }

  /**
   * 根据 ID 查找笔记
   */
  async findById(id: string): Promise<Note | undefined> {
    const result = await this.db
      .select()
      .from(notes)
      .where(and(isNull(notes.deletedAt), eq(notes.id, id)));

    if (result.length === 0) {
      return undefined;
    }

    return this.doToEntity(result[0]);
  }

  /**
   * 根据 ID 获取笔记（不存在则抛异常）
   */
  async getById(id: string): Promise<Note> {
    const note = await this.findById(id);
    if (!note) {
      throw new NotFoundException(`Note with id ${id} not found`);
    }
    return note;
  }

  /**
   * 根据条件查找笔记列表
   */
  async findByFilter(filter: NoteFilter): Promise<Note[]> {
    const conditions = [isNull(notes.deletedAt), eq(notes.spaceId, filter.spaceId)];

    if (filter.boardId) {
      conditions.push(eq(notes.boardId, filter.boardId));
    }

    if (filter.entityType) {
      conditions.push(eq(notes.entityType, filter.entityType));
      if (filter.entityId) {
        conditions.push(eq(notes.entityId, filter.entityId));
      }
    }

    const noteDOs = await this.db
      .select()
      .from(notes)
      .where(and(...conditions));

    return noteDOs.map((noteDO) => this.doToEntity(noteDO));
  }

  /**
   * 删除笔记（软删除）
   */
  async deleteById(id: string): Promise<void> {
    await this.db
      .update(notes)
      .set({
        deletedAt: new Date(),
      } as any)
      .where(and(isNull(notes.deletedAt), eq(notes.id, id)));
  }

  /**
   * 批量删除笔记（软删除）
   */
  async deleteManyByIds(ids: string[]): Promise<void> {
    await this.db
      .update(notes)
      .set({
        deletedAt: new Date(),
      } as any)
      .where(and(isNull(notes.deletedAt), inArray(notes.id, ids)));
  }

  /**
   * 实体转换为 DO
   */
  private entityToDO(note: Note): NoteDO {
    return {
      id: note.id,
      createdAt: note.createdAt,
      updatedAt: note.updatedAt,
      deletedAt: null, // 软删除字段由仓储层控制
      creatorId: note.creatorId,
      spaceId: note.spaceId,
      boardId: note.boardId,
      entityType: note.source?.entityType,
      entityId: note.source?.entityId,
      selection: note.source?.selection ? JSON.stringify(note.source.selection) : undefined,
      quoteRaw: note.source?.quote?.raw,
      quotePlain: note.source?.quote?.plain,
      contentRaw: note.content?.raw,
      contentPlain: note.content?.plain,
    };
  }

  private doToEntity(noteDO: NoteDO): Note {
    let source: NoteSource | undefined;
    if (noteDO.entityType && noteDO.entityId) {
      source = {
        entityType: noteDO.entityType as NoteSourceEntityType,
        entityId: noteDO.entityId,
      };

      source.selection = noteDO.selection
        ? (SafeParse(noteDO.selection, false, {}) as HTMLSelection)
        : undefined;

      if (noteDO.quoteRaw) {
        source.quote = {
          raw: noteDO.quoteRaw,
          plain: noteDO.quotePlain || '',
        };
      }
    }

    let content: NoteContent | undefined;
    if (noteDO.contentRaw) {
      content = {
        raw: noteDO.contentRaw,
        plain: noteDO.contentPlain || '',
      };
    }

    // 直接使用构造函数创建实体，从数据库加载的实体不是新的
    const note = new Note(
      noteDO.id,
      noteDO.createdAt,
      noteDO.updatedAt,
      noteDO.creatorId,
      noteDO.spaceId,
      noteDO.boardId,
      source,
      content,
    );

    // 从数据库加载的实体标记为已存在
    note.markAsExisting();
    return note;
  }
}

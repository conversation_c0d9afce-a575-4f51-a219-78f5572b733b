import { Injectable, NotFoundException } from '@nestjs/common';
import { and, desc, eq, isNull } from 'drizzle-orm';
import { DatabaseService } from '@/shared/db/database.service';
import { playlistItems } from '@/shared/db/public.schema';
import { PlaylistItem } from '../domain/playlist-item/models/playlist-item.entity';
import {
  PlaylistItemSourceEntityType,
  PlaylistItemStatus,
} from '../domain/playlist-item/types/playlist-item.types';

export interface PlaylistItemFilter {
  spaceId: string;
  boardId?: string;
  creatorId?: string;
  status?: PlaylistItemStatus;
  entityType?: PlaylistItemSourceEntityType;
}

// Database types
type PlaylistItemDO = typeof playlistItems.$inferSelect;

@Injectable()
export class PlaylistItemRepository {
  constructor(private readonly databaseService: DatabaseService) {}

  private get db() {
    return this.databaseService.db;
  }

  /**
   * 保存播放列表项聚合
   */
  async save(playlistItem: PlaylistItem): Promise<void> {
    const playlistItemDO = this.entityToDO(playlistItem);

    if (playlistItem.isNew) {
      await this.db.insert(playlistItems).values(playlistItemDO);
      playlistItem.markAsExisting();
    } else {
      const { id, ...shouldUpdate } = playlistItemDO;
      await this.db.update(playlistItems).set(shouldUpdate).where(eq(playlistItems.id, id));
    }
  }

  /**
   * 根据 ID 查找播放列表项
   */
  async findById(id: string): Promise<PlaylistItem | undefined> {
    const result = await this.db
      .select()
      .from(playlistItems)
      .where(and(isNull(playlistItems.deletedAt), eq(playlistItems.id, id)));

    if (result.length === 0) {
      return undefined;
    }

    return this.doToEntity(result[0]);
  }

  /**
   * 根据 ID 获取播放列表项（必须存在）
   */
  async getById(id: string): Promise<PlaylistItem> {
    const result = await this.findById(id);
    if (!result) {
      throw new NotFoundException(`PlaylistItem with id ${id} not found`);
    }
    return result;
  }

  /**
   * 根据创作板和空间查找播放列表项
   */
  async findByBoardAndSpace(boardId: string, spaceId: string): Promise<PlaylistItem[]> {
    const result = await this.db
      .select()
      .from(playlistItems)
      .where(
        and(
          isNull(playlistItems.deletedAt),
          eq(playlistItems.boardId, boardId),
          eq(playlistItems.spaceId, spaceId),
        ),
      )
      .orderBy(desc(playlistItems.createdAt));

    return result.map(this.doToEntity.bind(this));
  }

  /**
   * 根据过滤条件查找播放列表项
   */
  async findByFilter(filter: PlaylistItemFilter): Promise<PlaylistItem[]> {
    const conditions = [isNull(playlistItems.deletedAt), eq(playlistItems.spaceId, filter.spaceId)];

    if (filter.boardId) {
      conditions.push(eq(playlistItems.boardId, filter.boardId));
    }

    if (filter.creatorId) {
      conditions.push(eq(playlistItems.creatorId, filter.creatorId));
    }

    if (filter.status) {
      conditions.push(eq(playlistItems.status, filter.status));
    }

    if (filter.entityType) {
      conditions.push(eq(playlistItems.entityType, filter.entityType));
    }

    const result = await this.db
      .select()
      .from(playlistItems)
      .where(and(...conditions))
      .orderBy(desc(playlistItems.createdAt));

    return result.map(this.doToEntity.bind(this));
  }

  /**
   * 软删除播放列表项
   */
  async delete(id: string): Promise<void> {
    await this.db
      .update(playlistItems)
      .set({ deletedAt: new Date() } as any)
      .where(eq(playlistItems.id, id));
  }

  /**
   * 批量软删除播放列表项
   */
  async deleteMany(ids: string[]): Promise<void> {
    if (ids.length === 0) return;

    await this.db
      .update(playlistItems)
      .set({ deletedAt: new Date() } as any)
      .where(and(isNull(playlistItems.deletedAt), eq(playlistItems.id, ids[0])));

    // TODO: 实现批量删除，当前简化实现
    for (const id of ids) {
      await this.delete(id);
    }
  }

  /**
   * 实体转数据库对象
   */
  private entityToDO(entity: PlaylistItem): PlaylistItemDO {
    return {
      id: entity.id,
      createdAt: entity.createdAt,
      updatedAt: entity.updatedAt,
      deletedAt: null,
      creatorId: entity.creatorId,
      spaceId: entity.spaceId,
      boardId: entity.boardId,
      entityType: entity.entityType,
      entityId: entity.entityId,
      title: entity.title,
      playUrl: entity.playUrl,
      duration: entity.duration,
      status: entity.status,
      albumCoverUrl: entity.albumCoverUrl,
      transcript: entity.transcript,
      rank: entity.rank,
      playbackProgress: entity.playbackProgress,
    };
  }

  /**
   * 数据库对象转实体
   */
  private doToEntity(playlistItemDO: PlaylistItemDO): PlaylistItem {
    const entity = new PlaylistItem(
      playlistItemDO.id,
      playlistItemDO.creatorId,
      playlistItemDO.spaceId,
      playlistItemDO.createdAt,
      playlistItemDO.updatedAt,
      playlistItemDO.entityType as 'snip' | 'thought',
      playlistItemDO.entityId!,
      playlistItemDO.title,
      playlistItemDO.playUrl,
      playlistItemDO.duration || 0,
      playlistItemDO.status as PlaylistItemStatus,
      playlistItemDO.boardId || undefined,
      playlistItemDO.albumCoverUrl || undefined,
      playlistItemDO.transcript || undefined,
      playlistItemDO.rank || undefined,
      playlistItemDO.playbackProgress || undefined,
    );
    entity.markAsExisting();
    return entity;
  }
}

/**
 * Block Repository - 区块仓储层
 * 基于 thought repository 规范实现的区块数据访问
 *
 * Migrated from:
 * - /youapp/src/lib/dao/block/index.ts
 */

import { Injectable } from '@nestjs/common';
import { ContentFormatEnum, type LanguageEnum, ProcessStatusEnum } from '@repo/common';
import { and, eq, inArray, isNull, sql } from 'drizzle-orm';
import { DatabaseService } from '@/shared/db/database.service';
import { blocks, contents } from '@/shared/db/public.schema';
import { BlockDisplayEnum, SnipFeatureEnum } from '../../../common/types/snip.types';
import { Block } from '../domain/block/models/block.entity';
import { BlockContent } from '../domain/block/models/block-content.entity';

export interface BlockFilter {
  snipId?: string;
  snipIds?: string[];
  type?: SnipFeatureEnum[];
  status?: ProcessStatusEnum[];
  display?: BlockDisplayEnum[];
  language?: LanguageEnum;
}

// Database types
type BlockDO = typeof blocks.$inferSelect;
type ContentDO = typeof contents.$inferSelect;

interface BlockWithContentsDO extends BlockDO {
  contents?: ContentDO[];
}

@Injectable()
export class BlockRepository {
  constructor(private readonly databaseService: DatabaseService) {}

  private get db() {
    return this.databaseService.db;
  }

  /**
   * 保存区块聚合
   */
  async save(block: Block, trx?: any): Promise<void> {
    const blockDO = this.entityToDO(block);
    const db = trx || this.db;

    if (block.isNew) {
      // 插入新记录
      await db.insert(blocks).values(blockDO);
      block.markAsExisting();
    } else {
      // 更新现有记录
      const { id, ...shouldUpdate } = blockDO;
      await db
        .update(blocks)
        .set({
          ...shouldUpdate,
          updatedAt: new Date(),
        })
        .where(and(isNull(blocks.deletedAt), eq(blocks.id, id)));
    }
  }

  /**
   * 保存内容实体
   */
  async saveContent(content: BlockContent, trx?: any): Promise<void> {
    const contentDO = this.contentEntityToDO(content);
    const db = trx || this.db;

    if (content.isNew) {
      // 插入新记录
      await db.insert(contents).values(contentDO);
      content.markAsExisting();
    } else {
      // 更新现有记录
      const { id, ...shouldUpdate } = contentDO;
      await db
        .update(contents)
        .set({
          ...shouldUpdate,
          updatedAt: new Date(),
        })
        .where(and(isNull(contents.deletedAt), eq(contents.id, id)));
    }
  }

  /**
   * 根据 ID 查找区块
   */
  async findById(id: string): Promise<Block | undefined> {
    const result = await this.db
      .select({
        // block fields
        id: blocks.id,
        createdAt: blocks.createdAt,
        updatedAt: blocks.updatedAt,
        deletedAt: blocks.deletedAt,
        snipId: blocks.snipId,
        type: blocks.type,
        display: blocks.display,
        currentContentId: blocks.currentContentId,
        actionId: blocks.actionId,
        // content fields
        contentId: contents.id,
        contentBlockId: contents.blockId,
        contentLanguage: contents.language,
        contentFormat: contents.format,
        contentRaw: contents.raw,
        contentPlain: contents.plain,
        contentStatus: contents.status,
        contentCreatedAt: contents.createdAt,
        contentUpdatedAt: contents.updatedAt,
      })
      .from(blocks)
      .leftJoin(contents, and(eq(contents.blockId, blocks.id), isNull(contents.deletedAt)))
      .where(and(isNull(blocks.deletedAt), eq(blocks.id, id)));

    if (result.length === 0) {
      return undefined;
    }

    return this.aggregateBlockWithContents(result);
  }

  /**
   * 根据内容 ID 查找区块
   */
  async findByContentId(contentId: string): Promise<Block | undefined> {
    const result = await this.db
      .select({
        // block fields
        id: blocks.id,
        createdAt: blocks.createdAt,
        updatedAt: blocks.updatedAt,
        deletedAt: blocks.deletedAt,
        snipId: blocks.snipId,
        type: blocks.type,
        display: blocks.display,
        currentContentId: blocks.currentContentId,
        actionId: blocks.actionId,
        // content fields
        contentId: contents.id,
        contentBlockId: contents.blockId,
        contentLanguage: contents.language,
        contentFormat: contents.format,
        contentRaw: contents.raw,
        contentPlain: contents.plain,
        contentStatus: contents.status,
        contentCreatedAt: contents.createdAt,
        contentUpdatedAt: contents.updatedAt,
      })
      .from(contents)
      .innerJoin(blocks, eq(blocks.id, contents.blockId))
      .where(and(isNull(blocks.deletedAt), isNull(contents.deletedAt), eq(contents.id, contentId)));

    if (result.length === 0) {
      return undefined;
    }

    return this.aggregateBlockWithContents(result);
  }

  /**
   * 根据 snip ID 查找区块列表
   */
  async findBySnipId(
    snipId: string,
    filter?: Omit<BlockFilter, 'snipId' | 'snipIds'>,
  ): Promise<Block[]> {
    return this.findBySnipIds([snipId], filter);
  }

  /**
   * 根据多个 snip ID 查找区块列表
   */
  async findBySnipIds(
    snipIds: string[],
    filter?: Omit<BlockFilter, 'snipId' | 'snipIds'>,
  ): Promise<Block[]> {
    // 构建查询条件
    const conditions = [isNull(blocks.deletedAt), inArray(blocks.snipId, snipIds)];

    if (filter?.type) {
      conditions.push(inArray(blocks.type, filter.type));
    }

    if (filter?.display) {
      conditions.push(inArray(blocks.display, filter.display));
    }

    // 如果有状态过滤，需要子查询处理
    if (filter?.status) {
      const subquery = this.db
        .select({ blockId: contents.blockId })
        .from(contents)
        .where(and(isNull(contents.deletedAt), inArray(contents.status, filter.status)));

      conditions.push(sql`${blocks.id} IN (${subquery})`);
    }

    const result = await this.db
      .select({
        // block fields
        id: blocks.id,
        createdAt: blocks.createdAt,
        updatedAt: blocks.updatedAt,
        deletedAt: blocks.deletedAt,
        snipId: blocks.snipId,
        type: blocks.type,
        display: blocks.display,
        currentContentId: blocks.currentContentId,
        actionId: blocks.actionId,
        // content fields
        contentId: contents.id,
        contentBlockId: contents.blockId,
        contentLanguage: contents.language,
        contentFormat: contents.format,
        contentRaw: contents.raw,
        contentPlain: contents.plain,
        contentStatus: contents.status,
        contentCreatedAt: contents.createdAt,
        contentUpdatedAt: contents.updatedAt,
      })
      .from(blocks)
      .leftJoin(
        contents,
        and(
          eq(contents.blockId, blocks.id),
          isNull(contents.deletedAt),
          filter?.language ? eq(contents.language, filter.language) : sql`true`,
        ),
      )
      .where(and(...conditions));

    // 按区块分组并转换为实体
    return this.groupAndConvertToBlocks(result);
  }

  /**
   * 根据 URL 查找区块（用于去重检查）
   */
  async findByUrl(url: string): Promise<Block[]> {
    // 这需要与 snip 表关联，暂时返回空数组
    // TODO: 实现 URL 查询逻辑
    return [];
  }

  /**
   * 删除区块（软删除）
   */
  async delete(id: string, trx?: any): Promise<void> {
    const db = trx || this.db;
    const now = new Date();

    // 软删除区块
    await db
      .update(blocks)
      .set({ deletedAt: now })
      .where(and(isNull(blocks.deletedAt), eq(blocks.id, id)));

    // 软删除相关内容
    await db
      .update(contents)
      .set({ deletedAt: now })
      .where(and(isNull(contents.deletedAt), eq(contents.blockId, id)));
  }

  /**
   * 删除内容（软删除）
   */
  async deleteContent(id: string, trx?: any): Promise<void> {
    const db = trx || this.db;

    await db
      .update(contents)
      .set({ deletedAt: new Date() })
      .where(and(isNull(contents.deletedAt), eq(contents.id, id)));
  }

  /**
   * 检查区块是否存在
   */
  async exists(id: string): Promise<boolean> {
    const result = await this.db
      .select({ id: blocks.id })
      .from(blocks)
      .where(and(isNull(blocks.deletedAt), eq(blocks.id, id)))
      .limit(1);

    return result.length > 0;
  }

  /**
   * 根据区块 ID 获取内容列表
   */
  async getContentsByBlockId(blockId: string): Promise<BlockContent[]> {
    const result = await this.db
      .select()
      .from(contents)
      .where(and(isNull(contents.deletedAt), eq(contents.blockId, blockId)))
      .orderBy(contents.createdAt);

    return result.map((contentDO) => {
      const content = new BlockContent(
        contentDO.id,
        contentDO.createdAt,
        contentDO.updatedAt,
        contentDO.blockId!,
        contentDO.snipId || undefined,
        contentDO.language! as LanguageEnum,
        contentDO.format as ContentFormatEnum,
        contentDO.raw!,
        contentDO.plain,
        contentDO.status as ProcessStatusEnum,
        contentDO.traceId || undefined,
      );
      content.markAsExisting();
      return content;
    });
  }

  /**
   * 根据 snip ID 和类型查找区块
   */
  async findBySnipIdAndType(snipId: string, type: SnipFeatureEnum): Promise<Block[]> {
    return this.findBySnipId(snipId, { type: [type] });
  }

  /**
   * 创建概览区块
   */
  async createOverview(params: {
    snipId: string;
    type: SnipFeatureEnum;
    display: string;
  }): Promise<Block> {
    const block = Block.create({
      snipId: params.snipId,
      type: params.type,
      display: params.display as BlockDisplayEnum,
      currentContentId: undefined,
      actionId: undefined,
    });

    await this.save(block);
    return block;
  }

  /**
   * 更新区块的当前内容 ID
   */
  async updateCurrentContentId(blockId: string, contentId: string): Promise<void> {
    await this.db
      .update(blocks)
      .set({
        currentContentId: contentId,
        updatedAt: new Date(),
      })
      .where(and(isNull(blocks.deletedAt), eq(blocks.id, blockId)));
  }

  // ========================================
  // 私有方法
  // ========================================

  /**
   * 将实体转换为数据对象
   */
  private entityToDO(block: Block): BlockDO {
    return {
      id: block.id,
      createdAt: block.createdAt,
      updatedAt: block.updatedAt,
      deletedAt: null,
      snipId: block.snipId,
      boardId: null, // Block 实体目前不包含 boardId
      actionId: block.actionId || null,
      type: block.type,
      relatedSnipId: null, // Block 实体目前不包含 relatedSnipId
      display: block.display,
      currentContentId: block.currentContentId || null,
      originUrl: null, // Block 实体目前不包含 originUrl
    };
  }

  /**
   * 将内容实体转换为数据对象
   */
  private contentEntityToDO(content: BlockContent): ContentDO {
    return {
      id: content.id,
      createdAt: content.createdAt,
      updatedAt: content.updatedAt,
      deletedAt: null,
      format: content.format,
      raw: content.raw,
      plain: content.plain || null,
      language: content.language,
      status: content.status,
      blockId: content.blockId,
      snipId: null, // Content 实体目前不包含 snipId
      traceId: null, // Content 实体目前不包含 traceId
    };
  }

  /**
   * 聚合区块和内容数据
   */
  private aggregateBlockWithContents(result: any[]): Block {
    if (result.length === 0) {
      throw new Error('Empty result set');
    }

    const firstRow = result[0];
    const block = new Block(
      firstRow.id,
      firstRow.createdAt,
      firstRow.updatedAt,
      firstRow.snipId,
      firstRow.type,
      firstRow.display,
      firstRow.currentContentId,
      firstRow.actionId,
    );

    // 聚合内容
    const contents: BlockContent[] = [];
    for (const row of result) {
      if (row.contentId) {
        const content = new BlockContent(
          row.contentId,
          row.contentCreatedAt,
          row.contentUpdatedAt,
          row.contentBlockId,
          row.contentLanguage,
          row.contentFormat,
          row.contentRaw,
          row.contentPlain,
          row.contentStatus,
        );
        content.markAsExisting();
        contents.push(content);
      }
    }

    block.contents = contents;
    block.markAsExisting();

    return block;
  }

  /**
   * 将查询结果分组并转换为区块实体列表
   */
  private groupAndConvertToBlocks(result: any[]): Block[] {
    const blockMap = new Map<string, any[]>();

    // 按区块 ID 分组
    for (const row of result) {
      if (!blockMap.has(row.id)) {
        blockMap.set(row.id, []);
      }
      blockMap.get(row.id)!.push(row);
    }

    // 转换为区块实体
    const blocks: Block[] = [];
    for (const rows of Array.from(blockMap.values())) {
      const block = this.aggregateBlockWithContents(rows);
      blocks.push(block);
    }

    return blocks;
  }

  /**
   * 查询指定 snip 的 summary 内容
   * 迁移自 youapp blockDomain.querySummaryContentBySnip
   */
  async querySummaryContentBySnip(snipId: string): Promise<BlockContent[]> {
    const result = await this.db
      .select({
        id: contents.id,
        blockId: contents.blockId,
        snipId: contents.snipId,
        format: contents.format,
        raw: contents.raw,
        plain: contents.plain,
        language: contents.language,
        status: contents.status,
        createdAt: contents.createdAt,
        updatedAt: contents.updatedAt,
        deletedAt: contents.deletedAt,
        traceId: contents.traceId,
      })
      .from(contents)
      .innerJoin(blocks, eq(blocks.id, contents.blockId))
      .where(
        and(
          isNull(contents.deletedAt),
          isNull(blocks.deletedAt),
          eq(blocks.snipId, snipId),
          eq(blocks.type, SnipFeatureEnum.RAW_SUMMARY),
        ),
      );

    return result.map((row) => this.doToContentEntity(row));
  }

  /**
   * 查询指定 snip 的 transcript 内容
   * 迁移自 youapp blockDomain.queryTranscriptContentBySnip
   */
  async queryTranscriptContentBySnip(snipId: string): Promise<BlockContent[]> {
    const result = await this.db
      .select({
        id: contents.id,
        blockId: contents.blockId,
        snipId: contents.snipId,
        format: contents.format,
        raw: contents.raw,
        plain: contents.plain,
        language: contents.language,
        status: contents.status,
        createdAt: contents.createdAt,
        updatedAt: contents.updatedAt,
        deletedAt: contents.deletedAt,
        traceId: contents.traceId,
      })
      .from(contents)
      .innerJoin(blocks, eq(blocks.id, contents.blockId))
      .where(
        and(
          isNull(contents.deletedAt),
          isNull(blocks.deletedAt),
          eq(blocks.snipId, snipId),
          eq(blocks.type, SnipFeatureEnum.TRANSCRIPT),
        ),
      );

    return result.map((row) => this.doToContentEntity(row));
  }

  private doToContentEntity(contentDO: any): BlockContent {
    const content = new BlockContent(
      contentDO.id,
      contentDO.createdAt,
      contentDO.updatedAt,
      contentDO.blockId,
      contentDO.snipId,
      contentDO.language,
      contentDO.format,
      contentDO.raw || '',
      contentDO.plain,
      contentDO.status,
      contentDO.traceId,
    );
    content.isNew = false; // 从数据库查出来的都不是新对象
    return content;
  }
}

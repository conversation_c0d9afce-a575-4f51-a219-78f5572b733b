/**
 * BlockContent Repository - 区块内容仓储层
 * 处理内容实体的数据访问
 *
 * Migrated from:
 * - /youapp/src/lib/dao/content/index.ts
 */

import { Injectable, NotFoundException } from '@nestjs/common';
import { type ContentFormatEnum, type LanguageEnum, ProcessStatusEnum } from '@repo/common';
import { and, eq, inArray, isNull, sql } from 'drizzle-orm';
import { DatabaseService } from '@/shared/db/database.service';
import { contents } from '@/shared/db/public.schema';
import { BlockContent } from '../domain/block/models/block-content.entity';

export interface ContentFilter {
  blockId?: string;
  blockIds?: string[];
  language?: LanguageEnum;
  format?: ContentFormatEnum;
  status?: ProcessStatusEnum[];
}

// Database types
type ContentDO = typeof contents.$inferSelect;

@Injectable()
export class BlockContentRepository {
  constructor(private readonly databaseService: DatabaseService) {}

  private get db() {
    return this.databaseService.db;
  }

  /**
   * 保存内容实体
   */
  async save(content: BlockContent, trx?: any): Promise<void> {
    const contentDO = this.entityToDO(content);
    const db = trx || this.db;

    if (content.isNew) {
      // 插入新记录
      await db.insert(contents).values(contentDO);
      content.markAsExisting();
    } else {
      // 更新现有记录
      const { id, ...shouldUpdate } = contentDO;
      await db
        .update(contents)
        .set({
          ...shouldUpdate,
          updatedAt: new Date(),
        })
        .where(and(isNull(contents.deletedAt), eq(contents.id, id)));
    }
  }

  /**
   * 批量保存内容
   */
  async saveMany(contentList: BlockContent[], trx?: any): Promise<void> {
    const db = trx || this.db;

    const newContents = contentList.filter((c) => c.isNew);
    const existingContents = contentList.filter((c) => !c.isNew);

    // 批量插入新内容
    if (newContents.length > 0) {
      const insertData = newContents.map((c) => this.entityToDO(c));
      await db.insert(contents).values(insertData);
      newContents.forEach((c) => c.markAsExisting());
    }

    // 更新现有内容
    for (const content of existingContents) {
      await this.save(content, db);
    }
  }

  /**
   * 根据 ID 查找内容
   */
  async findById(id: string): Promise<BlockContent | undefined> {
    const result = await this.db
      .select()
      .from(contents)
      .where(and(isNull(contents.deletedAt), eq(contents.id, id)))
      .limit(1);

    if (result.length === 0) {
      return undefined;
    }

    return this.doToEntity(result[0]);
  }

  /**
   * 根据 ID 获取内容（不存在时抛出异常）
   */
  async getById(id: string): Promise<BlockContent> {
    const content = await this.findById(id);
    if (!content) {
      throw new NotFoundException(`Content with id ${id} not found`);
    }
    return content;
  }

  /**
   * 根据区块 ID 查找内容列表
   */
  async findByBlockId(
    blockId: string,
    filter?: Omit<ContentFilter, 'blockId' | 'blockIds'>,
  ): Promise<BlockContent[]> {
    return this.findByBlockIds([blockId], filter);
  }

  /**
   * 根据多个区块 ID 查找内容列表
   */
  async findByBlockIds(
    blockIds: string[],
    filter?: Omit<ContentFilter, 'blockId' | 'blockIds'>,
  ): Promise<BlockContent[]> {
    const conditions = [isNull(contents.deletedAt), inArray(contents.blockId, blockIds)];

    // 应用过滤条件
    if (filter?.language) {
      conditions.push(eq(contents.language, filter.language));
    }

    if (filter?.format) {
      conditions.push(eq(contents.format, filter.format));
    }

    if (filter?.status) {
      conditions.push(inArray(contents.status, filter.status));
    }

    const result = await this.db
      .select()
      .from(contents)
      .where(and(...conditions));

    return result.map((row) => this.doToEntity(row));
  }

  /**
   * 根据过滤条件查找内容列表
   */
  async findByFilter(filter: ContentFilter): Promise<BlockContent[]> {
    const conditions = [isNull(contents.deletedAt)];

    if (filter.blockId) {
      conditions.push(eq(contents.blockId, filter.blockId));
    }

    if (filter.blockIds) {
      conditions.push(inArray(contents.blockId, filter.blockIds));
    }

    if (filter.language) {
      conditions.push(eq(contents.language, filter.language));
    }

    if (filter.format) {
      conditions.push(eq(contents.format, filter.format));
    }

    if (filter.status) {
      conditions.push(inArray(contents.status, filter.status));
    }

    const result = await this.db
      .select()
      .from(contents)
      .where(and(...conditions));

    return result.map((row) => this.doToEntity(row));
  }

  /**
   * 删除内容（软删除）
   */
  async delete(id: string, trx?: any): Promise<void> {
    const db = trx || this.db;

    await db
      .update(contents)
      .set({ deletedAt: new Date() })
      .where(and(isNull(contents.deletedAt), eq(contents.id, id)));
  }

  /**
   * 批量删除内容（软删除）
   */
  async deleteMany(ids: string[], trx?: any): Promise<void> {
    const db = trx || this.db;

    await db
      .update(contents)
      .set({ deletedAt: new Date() })
      .where(and(isNull(contents.deletedAt), inArray(contents.id, ids)));
  }

  /**
   * 根据区块 ID 删除所有内容（软删除）
   */
  async deleteByBlockId(blockId: string, trx?: any): Promise<void> {
    const db = trx || this.db;

    await db
      .update(contents)
      .set({ deletedAt: new Date() })
      .where(and(isNull(contents.deletedAt), eq(contents.blockId, blockId)));
  }

  /**
   * 检查内容是否存在
   */
  async exists(id: string): Promise<boolean> {
    const result = await this.db
      .select({ id: contents.id })
      .from(contents)
      .where(and(isNull(contents.deletedAt), eq(contents.id, id)))
      .limit(1);

    return result.length > 0;
  }

  /**
   * 统计区块的内容数量
   */
  async countByBlockId(blockId: string): Promise<number> {
    const result = await this.db
      .select({ count: sql<number>`count(*)` })
      .from(contents)
      .where(and(isNull(contents.deletedAt), eq(contents.blockId, blockId)));

    return result[0]?.count || 0;
  }

  /**
   * 根据 Snip ID 查找所有相关内容
   */
  async findBySnipId(
    snipId: string,
    filter?: Omit<ContentFilter, 'blockId' | 'blockIds'>,
  ): Promise<BlockContent[]> {
    const conditions = [isNull(contents.deletedAt), eq(contents.snipId, snipId)];

    // 应用过滤条件
    if (filter?.language) {
      conditions.push(eq(contents.language, filter.language));
    }

    if (filter?.format) {
      conditions.push(eq(contents.format, filter.format));
    }

    if (filter?.status) {
      conditions.push(inArray(contents.status, filter.status));
    }

    const result = await this.db
      .select()
      .from(contents)
      .where(and(...conditions));

    return result.map((row) => this.doToEntity(row));
  }

  /**
   * 创建新内容
   */
  async create(params: {
    blockId: string;
    snipId: string;
    format: ContentFormatEnum;
    raw: string;
    plain: string;
    language: LanguageEnum;
    status: ProcessStatusEnum;
  }): Promise<BlockContent> {
    const content = BlockContent.create({
      blockId: params.blockId,
      language: params.language,
      format: params.format,
      raw: params.raw,
      plain: params.plain,
      status: params.status,
    });

    await this.save(content);
    return content;
  }

  /**
   * 更新内容字段
   */
  async updateContent(
    contentId: string,
    updates: {
      raw?: string;
      plain?: string;
      status?: ProcessStatusEnum;
    },
  ): Promise<void> {
    const updateData: any = {
      updatedAt: new Date(),
    };

    if (updates.raw !== undefined) {
      updateData.raw = updates.raw;
    }
    if (updates.plain !== undefined) {
      updateData.plain = updates.plain;
    }
    if (updates.status !== undefined) {
      updateData.status = updates.status;
    }

    await this.db
      .update(contents)
      .set(updateData)
      .where(and(isNull(contents.deletedAt), eq(contents.id, contentId)));
  }

  /**
   * 清空内容（重置为空字符串和进行中状态）
   */
  async clearContent(contentId: string): Promise<void> {
    await this.updateContent(contentId, {
      raw: '',
      plain: '',
      status: ProcessStatusEnum.ING,
    });
  }

  // ========================================
  // 私有方法
  // ========================================

  /**
   * 将实体转换为数据对象
   */
  private entityToDO(content: BlockContent): ContentDO {
    return {
      id: content.id,
      createdAt: content.createdAt,
      updatedAt: content.updatedAt,
      deletedAt: null,
      format: content.format,
      raw: content.raw,
      plain: content.plain || null,
      language: content.language,
      status: content.status,
      blockId: content.blockId,
      snipId: content.snipId || null,
      traceId: content.traceId || null,
    };
  }

  /**
   * 将数据对象转换为实体
   */
  private doToEntity(contentDO: ContentDO): BlockContent {
    const content = new BlockContent(
      contentDO.id,
      contentDO.createdAt,
      contentDO.updatedAt,
      contentDO.blockId,
      contentDO.snipId || undefined,
      contentDO.language as LanguageEnum,
      contentDO.format as ContentFormatEnum,
      contentDO.raw,
      contentDO.plain,
      contentDO.status as ProcessStatusEnum,
      contentDO.traceId || undefined,
    );

    content.markAsExisting();
    return content;
  }
}

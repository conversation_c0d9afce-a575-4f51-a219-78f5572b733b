/**
 * Webhook Subtitle Transcribed Handler - 字幕转录完成处理器
 * 从 youapp 迁移，对应原文件：/youapp/src/lib/app/snip/index.ts (onSubtitleTranscribed)
 */

import { Logger } from '@nestjs/common';
import { CommandHandler, ICommandHandler } from '@nestjs/cqrs';
import { NotImplemented } from '@/common/errors';
import { Video } from '../../../domain/snip/models/video.entity';
import { Voice } from '../../../domain/snip/models/voice.entity';
import {
  YougetErrorEventDto,
  YougetSubtitleTranscribedEventDto,
} from '../../../dto/webhook/webhook-subtitle-transcribed.dto';
import { BlockContentRepository } from '../../../repositories/block-content.repository';
import { SnipRepository } from '../../../repositories/snip.repository';
import { WebhookSubtitleTranscribedCommand } from '../../commands/webhook/webhook-subtitle-transcribed.command';

@CommandHandler(WebhookSubtitleTranscribedCommand)
export class WebhookSubtitleTranscribedHandler
  implements ICommandHandler<WebhookSubtitleTranscribedCommand>
{
  private readonly logger = new Logger(WebhookSubtitleTranscribedHandler.name);

  constructor(
    private readonly snipRepository: SnipRepository,
    private readonly contentRepository: BlockContentRepository,
    // private readonly actionDomainService: ActionDomainService,
  ) {}

  async execute(command: WebhookSubtitleTranscribedCommand): Promise<void> {
    // TODO 需要确认后再实现，流程有点复杂
    throw new NotImplemented('Not implemented');
    const { snipId, requestBody, queryParams, headers } = command;

    this.logger.debug(`Processing subtitle transcribed event for snip: ${snipId}`);

    // 获取 snip
    const snip = await this.snipRepository.getById(snipId);

    // 检查是否为 Video 或 Voice 类型
    if (!(snip instanceof Video) && !(snip instanceof Voice)) {
      this.logger.warn(`Snip ${snipId} is not a Video or Voice type, skipping`);
      return;
    }

    // TODO: 实现 Content 实体的查找和更新逻辑
    // 原 youapp 逻辑涉及 content_id 参数和 Block/Content 系统
    const { contentId } = queryParams;
    if (!contentId) {
      this.logger.warn(`No contentId provided for snip ${snipId}, skipping content update`);
    }

    // 检查是否为错误事件
    if (this.isErrorEvent(requestBody.data)) {
      const errorEvent = requestBody.data as YougetErrorEventDto;
      this.logger.warn(`Subtitle transcribe event has error for snip ${snipId}:`, errorEvent);

      // TODO: 更新 Content 状态为失败
      if (contentId) {
        this.logger.debug(`Should update content ${contentId} status to failed`);
      }
      return;
    }

    // 处理成功的转录事件
    const event = requestBody.data as YougetSubtitleTranscribedEventDto;
    this.logger.debug(`handleSubtitleTranscribedEvent`, event);

    // 检查是否为部分结果或完整结果
    const isPartialResult = headers['x-youget-partial-result'] === 'true';
    const isDone = queryParams.done === '1';
    const isComplete = !isPartialResult && isDone;

    this.logger.debug(
      `Processing subtitle: partial=${isPartialResult}, done=${isDone}, complete=${isComplete}`,
    );

    // TODO: 实现字幕数据处理逻辑
    // 原 youapp 逻辑包括：
    // 1. 从 Whisper 格式转换为内部字幕格式
    // 2. 更新 Content 实体状态和数据
    // 3. 提取实体名称用于说话人映射
    // 4. 记录 ASR 使用量
    // 5. 触发后续操作（如生成概述）

    if (isComplete) {
      this.logger.debug(`Transcription complete for snip ${snipId}`);

      // TODO: 后处理逻辑
      // - 提取实体名称用于说话人检测
      // - 更新 snip 的 extra 字段存储说话人提示
      // - 记录 ASR 使用量用于计费
      // - 根据 onCompletion 参数触发后续操作

      const { onCompletion } = queryParams;
      if (onCompletion === 'call_overview') {
        this.logger.debug(`Should trigger overview generation for snip ${snipId}`);
        // TODO: 触发概述生成
      }
    }

    // TODO: 更新 Content 实体
    if (contentId) {
      // 应该根据转录结果更新 Content 实体的状态和数据
      this.logger.debug(`Should update content ${contentId} with subtitle data`);
    }

    this.logger.debug(`Successfully processed subtitle transcribed event for snip: ${snipId}`);
  }

  /**
   * 检查是否为错误事件
   */
  private isErrorEvent(data: any): data is YougetErrorEventDto {
    return data && typeof data === 'object' && 'error' in data && data.error === true;
  }
}

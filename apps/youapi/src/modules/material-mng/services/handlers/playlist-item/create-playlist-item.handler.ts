import { Injectable, Logger } from '@nestjs/common';
import { CommandBus, CommandHandler, ICommandHandler, QueryBus } from '@nestjs/cqrs';
import { AllLanguageEnumKeys, iso6391ToLanguage, LanguageNameMap } from '@repo/common';
import { runInBackground } from '@/common/errors';
import { LLMs, MessageAtReferenceTypeEnum } from '@/common/types';
import { SafeParse } from '@/common/utils';
import { detectLanguageCode } from '@/domain/search/utils';
import { YLTTSOptions } from '@/infra/youllm';
import { TextRunnerService } from '@/modules/ai/runners';
import { ImageService } from '@/modules/ai/services/image.service';
import { SpeechService } from '@/modules/ai/services/speech.service';
import { SnipRepository } from '@/modules/material-mng/repositories/snip.repository';
import { ThoughtRepository } from '@/modules/material-mng/repositories/thought.repository';
import { PlaylistItem } from '../../../domain/playlist-item/models/playlist-item.entity';
import {
  AudioGenerationOptions,
  PlaylistItemStatus,
} from '../../../domain/playlist-item/types/playlist-item.types';

type PlaylistItemEntityType = 'snip' | 'thought';

import { ContextBuilder } from '@/domain/chat/context';
import { PlaylistItemDto } from '../../../dto/playlist-item.dto';
import { PlaylistItemRepository } from '../../../repositories/playlist-item.repository';
import { CreatePlaylistItemCommand } from '../../commands/playlist-item/create-playlist-item.command';
import { PlaylistItemDtoService } from '../../dto-services/playlist-item-dto.service';

/**
 * 暂且先放这儿，后续有更好的位置再挪
 * [{
 *   "text": "埃隆·马斯克",
 *   "time_begin": 0,
 *   "time_end": 9327.301587301587,
 *   "text_begin": 0,
 *   "text_end": 44
 * }]
 */
export interface MinimaxSubtitle {
  text: string;
  time_begin: number;
  time_end: number;
  text_begin: number;
  text_end: number;
}

@CommandHandler(CreatePlaylistItemCommand)
@Injectable()
export class CreatePlaylistItemHandler implements ICommandHandler<CreatePlaylistItemCommand> {
  private readonly logger = new Logger(CreatePlaylistItemHandler.name);

  constructor(
    private readonly playlistItemRepository: PlaylistItemRepository,
    private readonly playlistItemDtoService: PlaylistItemDtoService,
    private readonly commandBus: CommandBus,
    private readonly queryBus: QueryBus,
    private readonly snipRepository: SnipRepository,
    private readonly thoughtRepository: ThoughtRepository,
    private readonly textRunnerService: TextRunnerService,
    private readonly speechService: SpeechService,
    private readonly imageService: ImageService,
    private readonly contextBuilder: ContextBuilder,
  ) {}

  async execute(command: CreatePlaylistItemCommand): Promise<PlaylistItemDto> {
    const { userId, spaceId, entityType, entityId, boardId, audioOptions } = command;

    try {
      // 2. 创建播放列表项聚合（初始状态为GENERATING）
      const playlistItem = PlaylistItem.create({
        creatorId: userId,
        spaceId,
        entityType,
        entityId,
        boardId,
      });

      // 3. 保存聚合（立即返回给用户）
      await this.playlistItemRepository.save(playlistItem);

      // 4. 提交领域事件
      playlistItem.commit();

      // 5. 异步处理音频生成（不阻塞响应）
      runInBackground(
        this.processAudioGenerationAsync(
          playlistItem.id,
          entityType,
          entityId,
          audioOptions || undefined,
          userId,
        ),
      );

      // 6. 立即返回播放列表项DTO
      return this.playlistItemDtoService.toDto(playlistItem);
    } catch (error) {
      this.logger.error('Failed to create playlist item', {
        error: error.message,
        userId,
        entityType,
        entityId,
      });
      throw error;
    }
  }

  /**
   * 异步处理音频生成（后台任务）
   */
  private async processAudioGenerationAsync(
    playlistItemId: string,
    entityType: PlaylistItemEntityType,
    entityId: string,
    audioOptions: AudioGenerationOptions | undefined,
    _userId: string,
  ): Promise<void> {
    this.logger.log('Starting audio generation', { playlistItemId, entityType, entityId });

    // 1. 获取源内容和标题
    const { content, title } = await this.getSourceContent(entityType, entityId);

    if (!content) {
      await this.updatePlaylistItemStatus(playlistItemId, PlaylistItemStatus.FAILED);
      this.logger.error('No content found for entity', { entityType, entityId });
      return;
    }

    // 检测内容语言
    const languageCode = detectLanguageCode(content);
    const locale = iso6391ToLanguage[languageCode] ?? 'en-US';
    const aiLanguage = LanguageNameMap[locale as AllLanguageEnumKeys];

    // 调用 LLM 服务生成音频概述
    const runner = await this.textRunnerService.getAudioOverviewRunner({
      language: aiLanguage,
      content: content,
    });
    const result = await runner.generateOnce();
    const audioScript = result.text;

    try {
      const { audioUrl, subtitleFile, extraInfo } = await this.speechService.generateSpeech({
        prompt: audioScript,
        voice: (audioOptions?.voice as YLTTSOptions['voice']) || 'Wise_Woman',
        emotion: (audioOptions?.emotion as YLTTSOptions['emotion']) || 'neutral',
        model: audioOptions?.model as LLMs,
        subtitleEnable: false,
      });
      const coverImage = await this.imageService.generatePlaylistCover({
        title,
        text: audioScript,
      });

      let transcript = '';
      if (subtitleFile) {
        // 下载字幕文件
        const response = await fetch(subtitleFile);
        const text = await response.text();
        const subtitles = SafeParse(text, false, []) as MinimaxSubtitle[];
        transcript = JSON.stringify(
          subtitles.map((subtitle: MinimaxSubtitle) => ({
            start: subtitle.time_begin,
            end: subtitle.time_end,
            speaker: '',
            content: [subtitle.text],
          })),
        );
      }

      // 3. 更新播放列表项状态为成功
      await this.updatePlaylistItemWithAudioData(playlistItemId, {
        title,
        status: PlaylistItemStatus.SUCCESS,
        playUrl: audioUrl,
        duration: Number(extraInfo?.audio_length || 0) / 1000,
        albumCoverUrl: coverImage.imageUrl,
        transcript,
        rank: '',
      });

      this.logger.log('Audio generation completed successfully', { playlistItemId });
    } catch (error) {
      this.logger.error('Audio generation failed', {
        error: error.message,
        playlistItemId,
        entityType,
        entityId,
      });
      // 更新状态为失败
      await this.updatePlaylistItemStatus(playlistItemId, PlaylistItemStatus.FAILED);
    }
  }

  /**
   * 获取源内容（从 Snip 或 Thought）
   */
  private async getSourceContent(
    entityType: PlaylistItemEntityType,
    entityId: string,
  ): Promise<{ content: string; title: string }> {
    const playlistItemEntityTypeToMessageAtReferenceTypeEnumMap = {
      snip: MessageAtReferenceTypeEnum.SNIP,
      thought: MessageAtReferenceTypeEnum.THOUGHT,
    };
    const context = await this.contextBuilder.getFullEntityContent(
      entityId,
      playlistItemEntityTypeToMessageAtReferenceTypeEnumMap[entityType],
    );

    if (context.content) {
      if (entityType === 'thought') {
        const thought = await this.thoughtRepository.getById(entityId);
        return {
          content: context.content,
          title: thought.title || 'Untitled Thought',
        };
      } else if (entityType === 'snip') {
        const snip = await this.snipRepository.getById(entityId);
        return {
          content: context.content,
          title: snip.title || 'Untitled Snip',
        };
      }
    }

    throw new Error(`Unsupported entity type: ${entityType}`);
  }

  /**
   * 更新播放列表项状态
   */
  private async updatePlaylistItemStatus(
    playlistItemId: string,
    status: PlaylistItemStatus,
  ): Promise<void> {
    const playlistItem = await this.playlistItemRepository.getById(playlistItemId);
    playlistItem.updateStatus(status);
    await this.playlistItemRepository.save(playlistItem);
    playlistItem.commit();
  }

  /**
   * 更新播放列表项的音频数据
   */
  private async updatePlaylistItemWithAudioData(
    playlistItemId: string,
    audioData: {
      title: string;
      status: PlaylistItemStatus;
      playUrl: string;
      duration: number;
      albumCoverUrl?: string;
      transcript?: string;
      rank: string;
    },
  ): Promise<void> {
    const playlistItem = await this.playlistItemRepository.getById(playlistItemId);

    // 更新所有音频相关字段
    playlistItem.title = audioData.title;
    playlistItem.status = audioData.status;
    playlistItem.playUrl = audioData.playUrl;
    playlistItem.duration = audioData.duration;
    playlistItem.rank = audioData.rank;

    if (audioData.albumCoverUrl) {
      playlistItem.albumCoverUrl = audioData.albumCoverUrl;
    }

    if (audioData.transcript) {
      playlistItem.transcript = audioData.transcript;
    }

    await this.playlistItemRepository.save(playlistItem);
    playlistItem.commit();
  }
}

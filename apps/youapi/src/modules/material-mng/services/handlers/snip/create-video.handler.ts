/**
 * Create Video Handler - 创建视频处理器
 * 从 youapp 迁移的创建视频处理器
 *
 * Migrated from:
 * - /youapp/src/lib/app/snip/index.ts (createVideo)
 */

import { BadRequestException, Logger } from '@nestjs/common';
import { CommandBus, CommandHandler, EventBus, ICommandHandler } from '@nestjs/cqrs';
import {
  ContentFormatEnum,
  createContentHandler,
  DefaultLanguageKey,
  ProcessStatusEnum,
} from '@repo/common';
import { BlockDisplayEnum, SnipFeatureEnum } from '@/common/types/snip.types';
import { getCallbackOrigin } from '@/common/utils/callback';
import { ChatDomainService } from '@/domain/chat';
import { Youget } from '@/infra/youget';
import { Block } from '@/modules/material-mng/domain/block/models/block.entity';
import { BlockContent } from '@/modules/material-mng/domain/block/models/block-content.entity';
import { VideoDto } from '@/modules/material-mng/dto/snip/video.dto';
import { BlockRepository } from '@/modules/material-mng/repositories/block.repository';
import { BlockContentRepository } from '@/modules/material-mng/repositories/block-content.repository';
import { BoardRepository } from '@/modules/material-mng/repositories/board.repository';
import { BoardPositionDomainService } from '../../../domain/shared/board-position.service';
import { SnipStatus } from '../../../domain/snip/models/snip.entity';
import { Video } from '../../../domain/snip/models/video.entity';
import { SnipRepository } from '../../../repositories/snip.repository';
import { CreateSnipBlocksCommand } from '../../commands/snip/create-snip-blocks.command';
import { CreateVideoCommand } from '../../commands/snip/create-video.command';
import { SnipDtoService } from '../../dto-services/snip-dto.service';

@CommandHandler(CreateVideoCommand)
export class CreateVideoHandler implements ICommandHandler<CreateVideoCommand> {
  private static readonly logger = new Logger(CreateVideoHandler.name);

  constructor(
    private readonly snipRepository: SnipRepository,
    private readonly boardPositionService: BoardPositionDomainService,
    private readonly snipDtoService: SnipDtoService,
    private readonly eventBus: EventBus,
    private readonly boardRepository: BoardRepository,
    private readonly youget: Youget,
    private readonly blockRepository: BlockRepository,
    private readonly contentRepository: BlockContentRepository,
    private readonly chatDomainService: ChatDomainService,
    private readonly commandBus: CommandBus,
  ) {}

  async execute(command: CreateVideoCommand): Promise<VideoDto> {
    const {
      spaceId,
      creatorId,
      title,
      webpage,
      description,
      authors,
      heroImageUrl,
      publishedAt,
      playUrl,
      extra,
      boardId: originalBoardId,
      parentBoardGroupId,
      chatId,
    } = command;

    // 验证必填字段
    if (!title) {
      throw new BadRequestException('Title is required');
    }

    if (!webpage || !description) {
      throw new BadRequestException('Webpage and description are required for video');
    }

    // 确定 boardId：如果指定了则使用，否则获取默认 board
    let boardId: string;
    if (originalBoardId) {
      await this.boardRepository.getById(originalBoardId);
      boardId = originalBoardId;
    } else {
      boardId = (await this.boardRepository.getDefaultBoardBySpaceId(spaceId)).id;
    }

    // 提取 transcript/overview 数据并准备用于 Video.create 的 extra 字段
    let videoExtra = extra;
    let snipBlocksData = null;

    // 如果 extra 是对象且包含 transcript 或 overview 数据，提取并处理
    if (
      extra &&
      typeof extra === 'object' &&
      (extra.transcript_contents || extra.overview_contents)
    ) {
      snipBlocksData = {
        transcript_contents: extra.transcript_contents || [],
        overview_contents: extra.overview_contents || [],
      };
      videoExtra = undefined; // Video 不需要存储这些数据到 extra 字段
    }

    // 创建视频实体，与 youapp 保持一致
    const video = await Video.create(
      {
        spaceId,
        creatorId,
        title,
        webpage,
        description,
        authors,
        heroImageUrl,
        publishedAt,
        playUrl,
        extra: typeof videoExtra === 'string' ? videoExtra : undefined,
        boardId,
        parentBoardGroupId,
      },
      boardId ? this.boardPositionService : undefined,
    );

    // 检查是否需要转存图片
    const imageUrls = video.extractImageUrls() || [];
    CreateVideoHandler.logger.debug(`before save Video: ${JSON.stringify(video)}`);

    if (imageUrls.length > 0) {
      // 设置状态为图片转存中
      video.update({ status: SnipStatus.IMAGE_TRANSFERING });

      // 保存视频
      await this.snipRepository.save(video);

      // 实现图片转存功能
      this.transferImagesInBackground(imageUrls, video.id!, creatorId);
    } else {
      // 直接保存视频
      await this.snipRepository.save(video);
    }

    // 创建视频内容 block
    await this.createVideoContentBlock(video);

    // 如果有 transcript 或 overview 内容，调用 CreateSnipBlocksHandler
    if (
      snipBlocksData &&
      (snipBlocksData.transcript_contents.length > 0 || snipBlocksData.overview_contents.length > 0)
    ) {
      const createSnipBlocksCommand = new CreateSnipBlocksCommand(video.id!, snipBlocksData);

      await this.commandBus.execute(createSnipBlocksCommand);
      CreateVideoHandler.logger.log(`Created snip blocks for video: ${video.id}`);
    }

    // 绑定聊天
    if (chatId && video.id) {
      await this.chatDomainService.bindWebpageChatToSnip({
        snip_id: video.id!,
        chat_id: chatId,
      });
    }

    CreateVideoHandler.logger.debug(`Video snip created successfully: ${video.id}`);
    // 返回 DTO，与 youapp 的 snip2vo 功能保持一致
    const dto = this.snipDtoService.toDto(video);

    return {
      ...dto,
      boardIds: boardId ? [boardId] : [], // 兼容旧数据，实际取 boardId 即可
    } as VideoDto;
  }

  /**
   * 创建视频内容 block
   * 基于 youapp 的 createSnipBlocks 实现
   */
  private async createVideoContentBlock(video: Video): Promise<void> {
    // 使用 createContentHandler 创建内容处理器
    const contentHandler = createContentHandler({
      format: ContentFormatEnum.READER_HTML,
      raw: video.description.getRaw(),
      plain: video.description.getPlain(),
      language: video.description.getLanguage() || DefaultLanguageKey,
    });

    // 创建 block
    const block = Block.create({
      snipId: video.id,
      type: SnipFeatureEnum.RAW_SUMMARY, // 视频描述使用 RAW_SUMMARY 类型
      display: BlockDisplayEnum.SHOW,
    });

    // 保存 block
    await this.blockRepository.save(block);

    // 创建 content
    const content = BlockContent.create({
      blockId: block.id,
      language: contentHandler.toVO().language || DefaultLanguageKey,
      format: contentHandler.toVO().format,
      raw: contentHandler.toVO().raw,
      plain: contentHandler.toVO().plain,
      status: ProcessStatusEnum.DONE,
    });
    // 目前领域实体未直接 add content 方法，需要手动添加
    block.addContent(content);

    // 保存 content
    await this.contentRepository.save(content);

    // 设置当前内容
    block.setCurrentContent(content.id);
    await this.blockRepository.save(block);

    CreateVideoHandler.logger.debug(`Video content block created for snip: ${video.id}`);
  }

  /**
   * 将图片转存到后台
   */
  transferImagesInBackground(imageUrls: string[], snip_id: string, user_id: string) {
    CreateVideoHandler.logger.debug(
      'this.youget.executeExtractTextTaskInBackground = ' +
        this.youget.executeSaveImageTaskInBackground,
    );
    this.youget.executeSaveImageTaskInBackground(
      {
        image_urls: imageUrls,
        youget_callback_url: `${getCallbackOrigin()}/webhook/v1/images-transfered?snip_id=${snip_id}`,
        youget_callback_method: 'PUT',
      },
      user_id,
    );
  }
}

/**
 * Create Article Handler - 创建文章处理器
 * 从 youapp 迁移的创建文章处理器
 *
 * Migrated from:
 * - /youapp/src/lib/app/snip/index.ts (createArticle)
 */

import { BadRequestException, Logger } from '@nestjs/common';
import { CommandBus, CommandHandler, EventBus, ICommandHandler } from '@nestjs/cqrs';
import {
  ContentFormatEnum,
  createContentHandler,
  DefaultLanguageKey,
  ProcessStatusEnum,
} from '@repo/common';
import { BlockDisplayEnum, SnipFeatureEnum } from '@/common/types/snip.types';
import { getCallbackOrigin } from '@/common/utils/callback';
import { ChatDomainService } from '@/domain/chat';
import { Youget } from '@/infra/youget';
import { Block } from '@/modules/material-mng/domain/block/models/block.entity';
import { BlockContent } from '@/modules/material-mng/domain/block/models/block-content.entity';
import { ArticleDto } from '@/modules/material-mng/dto/snip/article.dto';
import { BlockRepository } from '@/modules/material-mng/repositories/block.repository';
import { BlockContentRepository } from '@/modules/material-mng/repositories/block-content.repository';
import { BoardRepository } from '@/modules/material-mng/repositories/board.repository';
import { BoardPositionDomainService } from '../../../domain/shared/board-position.service';
import { Article } from '../../../domain/snip/models/article.entity';
import { SnipStatus } from '../../../domain/snip/models/snip.entity';
import { SnipRepository } from '../../../repositories/snip.repository';
import { CreateArticleCommand } from '../../commands/snip/create-article.command';
import { CreateSnipBlocksCommand } from '../../commands/snip/create-snip-blocks.command';
import { SnipDtoService } from '../../dto-services/snip-dto.service';

@CommandHandler(CreateArticleCommand)
export class CreateArticleHandler implements ICommandHandler<CreateArticleCommand> {
  private static readonly logger = new Logger(CreateArticleHandler.name);

  constructor(
    private readonly snipRepository: SnipRepository,
    private readonly boardPositionService: BoardPositionDomainService,
    private readonly snipDtoService: SnipDtoService,
    private readonly eventBus: EventBus,
    private readonly boardRepository: BoardRepository,
    private readonly youget: Youget,
    private readonly blockRepository: BlockRepository,
    private readonly contentRepository: BlockContentRepository,
    private readonly chatDomainService: ChatDomainService,
    private readonly commandBus: CommandBus,
  ) {}

  async execute(command: CreateArticleCommand): Promise<ArticleDto> {
    const {
      spaceId,
      creatorId,
      title,
      webpage,
      content,
      authors,
      heroImageUrl,
      publishedAt,
      playUrl,
      extra,
      boardId: originalBoardId,
      parentBoardGroupId,
      chatId,
    } = command;

    // 验证必填字段
    if (!title) {
      throw new BadRequestException('Title is required');
    }

    if (!webpage || !content) {
      throw new BadRequestException('Webpage and content are required for article');
    }

    // 确定 boardId：如果指定了则使用，否则获取默认 board
    let boardId: string;
    if (originalBoardId) {
      await this.boardRepository.getById(originalBoardId);
      boardId = originalBoardId;
    } else {
      boardId = (await this.boardRepository.getDefaultBoardBySpaceId(spaceId)).id;
    }

    // 提取 transcript/overview 数据并准备用于 Article.create 的 extra 字段
    let articleExtra = extra;
    let snipBlocksData = null;

    // 如果 extra 是对象且包含 transcript 或 overview 数据，提取并处理
    if (
      extra &&
      typeof extra === 'object' &&
      (extra.transcript_contents || extra.overview_contents)
    ) {
      snipBlocksData = {
        transcript_contents: extra.transcript_contents || [],
        overview_contents: extra.overview_contents || [],
      };
      articleExtra = undefined; // Article 不需要存储这些数据到 extra 字段
    }

    // 创建文章实体，与 youapp 保持一致
    const article = await Article.create(
      {
        spaceId,
        creatorId,
        title,
        webpage,
        content,
        authors,
        heroImageUrl,
        publishedAt,
        playUrl,
        extra: typeof articleExtra === 'string' ? articleExtra : undefined,
        boardId,
        parentBoardGroupId,
      },
      boardId ? this.boardPositionService : undefined,
    );

    // 检查是否需要转存图片
    const imageUrls = article.extractImageUrls() || [];
    CreateArticleHandler.logger.debug(`before save Article: ${JSON.stringify(article)}`);

    if (imageUrls.length > 0) {
      // 设置状态为图片转存中
      article.update({ status: SnipStatus.IMAGE_TRANSFERING });

      // 保存文章
      await this.snipRepository.save(article);

      // 实现图片转存功能
      this.transferImagesInBackground(imageUrls, article.id!, creatorId);
    } else {
      // 直接保存文章
      await this.snipRepository.save(article);
    }

    // // 创建文章内容 block
    await this.createArticleContentBlock(article);

    // 如果有 transcript 或 overview 内容，调用 CreateSnipBlocksHandler
    if (
      snipBlocksData &&
      (snipBlocksData.transcript_contents.length > 0 || snipBlocksData.overview_contents.length > 0)
    ) {
      const createSnipBlocksCommand = new CreateSnipBlocksCommand(article.id!, snipBlocksData);

      await this.commandBus.execute(createSnipBlocksCommand);
      CreateArticleHandler.logger.log(`Created snip blocks for article: ${article.id}`);
    }

    // 绑定聊天
    if (chatId) {
      await this.chatDomainService.bindWebpageChatToSnip({
        snip_id: article.id!,
        chat_id: chatId,
      });
    }

    // this.logger.debug(`Article snip created successfully: ${article.id}`);

    CreateArticleHandler.logger.debug(`Article snip created successfully: ${article.id}`);
    // 返回 DTO，与 youapp 的 snip2vo 功能保持一致
    const dto = this.snipDtoService.toDto(article);

    return {
      ...dto,
      boardIds: boardId ? [boardId] : [], // 兼容旧数据，实际取 boardId 即可
    };
  }

  /**
   * 创建文章内容 block
   * 基于 youapp 的 createSnipBlocks 实现
   */
  private async createArticleContentBlock(article: Article): Promise<void> {
    // 使用 createContentHandler 创建内容处理器
    const contentHandler = createContentHandler({
      format: ContentFormatEnum.READER_HTML,
      raw: article.content.getRaw(),
      plain: article.content.getPlain(),
      language: article.content.getLanguage() || DefaultLanguageKey,
    });

    // 创建 block
    const block = Block.create({
      snipId: article.id,
      type: SnipFeatureEnum.RAW_SUMMARY, // 文章内容使用 RAW_SUMMARY 类型
      display: BlockDisplayEnum.SHOW,
    });

    // 保存 block
    await this.blockRepository.save(block);

    // 创建 content
    const content = BlockContent.create({
      blockId: block.id,
      language: contentHandler.toVO().language || DefaultLanguageKey,
      format: contentHandler.toVO().format,
      raw: contentHandler.toVO().raw,
      plain: contentHandler.toVO().plain,
      status: ProcessStatusEnum.DONE,
    });
    // 目前领域实体未直接 add content 方法，需要手动添加
    block.addContent(content);

    // 保存 content
    await this.contentRepository.save(content);

    // 设置当前内容
    block.setCurrentContent(content.id);
    await this.blockRepository.save(block);

    CreateArticleHandler.logger.debug(`Article content block created for snip: ${article.id}`);
  }

  /**
   * 将图片转存到后台
   */
  transferImagesInBackground(imageUrls: string[], snip_id: string, user_id: string) {
    CreateArticleHandler.logger.debug(
      'this.youget.executeExtractTextTaskInBackground = ' +
        this.youget.executeSaveImageTaskInBackground,
    );
    this.youget.executeSaveImageTaskInBackground(
      {
        image_urls: imageUrls,
        youget_callback_url: `${getCallbackOrigin()}/webhook/v1/images-transfered?snip_id=${snip_id}`,
        youget_callback_method: 'PUT',
      },
      user_id,
    );
  }
}

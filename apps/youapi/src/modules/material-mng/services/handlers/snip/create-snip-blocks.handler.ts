/**
 * Material Management CQRS Handler - Create Snip Blocks
 * 处理创建 Snip 内容块的命令
 *
 * Migrated from:
 * - youapp/lib/domain/snip/index.ts (createSnipBlocks method)
 */

import { BadRequestException, Logger } from '@nestjs/common';
import { CommandHandler, ICommandHandler } from '@nestjs/cqrs';
import {
  ContentFormatEnum,
  createContentHandler,
  LanguageEnum,
  ProcessStatusEnum,
} from '@repo/common';
import { BlockDisplayEnum } from '@/common/types';
import { ActionDAO } from '@/dao/action';
import { BlockContent } from '@/modules/material-mng/domain/block/models/block-content.entity';
import { Overview } from '@/modules/material-mng/domain/block/models/overview.entity';
import { Transcript } from '@/modules/material-mng/domain/block/models/transcript.entity';
import { BlockRepository } from '@/modules/material-mng/repositories/block.repository';
import { BlockContentRepository } from '@/modules/material-mng/repositories/block-content.repository';
import { SnipRepository } from '@/modules/material-mng/repositories/snip.repository';
import {
  CreateContentParams,
  CreateSnipBlocksCommand,
} from '../../commands/snip/create-snip-blocks.command';

@CommandHandler(CreateSnipBlocksCommand)
export class CreateSnipBlocksHandler implements ICommandHandler<CreateSnipBlocksCommand> {
  private readonly logger = new Logger(CreateSnipBlocksHandler.name);

  constructor(
    private readonly actionDAO: ActionDAO,
    private readonly blockRepository: BlockRepository,
    private readonly contentRepository: BlockContentRepository,
    private readonly snipRepository: SnipRepository,
  ) {}

  async execute(command: CreateSnipBlocksCommand): Promise<void> {
    const { snipId, params } = command;

    this.logger.log(`Creating snip blocks for snip: ${snipId}`);

    // 验证 snip 是否存在
    const snip = await this.snipRepository.findById(snipId);
    if (!snip) {
      throw new BadRequestException(`Snip with id ${snipId} not found`);
    }

    try {
      // 创建 transcript 内容块
      if (params.transcriptContents && params.transcriptContents.length > 0) {
        await this.createTranscriptBlocks(snipId, params.transcriptContents);
      }

      // 创建 overview 内容块
      if (params.overviewContents && params.overviewContents.length > 0) {
        await this.createOverviewBlocks(snipId, params.overviewContents);
      }

      this.logger.log(`Successfully created snip blocks for snip: ${snipId}`);
    } catch (error) {
      this.logger.error(`Failed to create snip blocks for snip: ${snipId}`, error);
      throw error;
    }
  }

  /**
   * 创建转录内容块
   */
  private async createTranscriptBlocks(
    snipId: string,
    transcriptContents: CreateContentParams[],
  ): Promise<void> {
    // 获取 transcript action
    const transcriptAction = await this.actionDAO.selectOneByName('transcript');
    if (!transcriptAction) {
      throw new BadRequestException('Transcript action not found');
    }

    // 按 format 分组，每个 format 类型最多只有一个 content
    const contentsByFormat = new Map<ContentFormatEnum, CreateContentParams>();

    for (const content of transcriptContents) {
      const format = content.format ?? ContentFormatEnum.SUBTITLE;

      // 如果已存在相同 format，跳过后续的（保留第一个）
      if (!contentsByFormat.has(format)) {
        contentsByFormat.set(format, content);
      } else {
        this.logger.warn(`Duplicate transcript content format ${format} found, skipping`);
      }
    }

    // 创建 Transcript 实体
    const transcript = Transcript.create({
      snipId,
      display: BlockDisplayEnum.SHOW,
      actionId: transcriptAction.id,
    });

    // 保存 Transcript 实体
    await this.blockRepository.save(transcript);

    // 为每个唯一的 format 创建内容并保存
    for (const format of contentsByFormat.keys()) {
      const contentParam = contentsByFormat.get(format);
      if (contentParam) {
        const content = await this.createAndSaveContent(transcript.id, contentParam, format);
        if (content) {
          transcript.addContent(content);
        }
      }
    }

    // 如果有内容，设置第一个为当前内容
    if (transcript.contents.length > 0) {
      transcript.setCurrentContent(transcript.contents[0].id);
      await this.blockRepository.save(transcript);
    }
  }

  /**
   * 创建概览内容块 - 最多只有一个 content
   */
  private async createOverviewBlocks(
    snipId: string,
    overviewContents: CreateContentParams[],
  ): Promise<void> {
    // 获取 overview action
    const overviewAction = await this.actionDAO.selectOneByName('overview');
    if (!overviewAction) {
      throw new BadRequestException('Overview action not found');
    }

    // Overview 最多只有一个 content，取第一个
    if (overviewContents.length > 1) {
      this.logger.warn(
        `Overview block should have at most 1 content, but got ${overviewContents.length}. Using the first one.`,
      );
    }

    const firstContent = overviewContents[0];
    if (!firstContent) {
      this.logger.warn('No overview content provided');
      return;
    }

    // 创建 Overview 实体
    const overview = Overview.create({
      snipId,
      display: BlockDisplayEnum.SHOW,
      actionId: overviewAction.id,
    });

    // 保存 Overview 实体
    await this.blockRepository.save(overview);

    // 创建单个内容并保存
    const content = await this.createAndSaveContent(
      overview.id,
      firstContent,
      ContentFormatEnum.LLM_OUTPUT,
    );

    // 添加内容到 overview
    if (content) {
      overview.addContent(content);
      await this.blockRepository.save(overview);
    }
  }

  /**
   * 创建并保存单个内容实体
   */
  private async createAndSaveContent(
    blockId: string,
    contentParam: CreateContentParams,
    defaultFormat: ContentFormatEnum,
  ): Promise<BlockContent | null> {
    if (!contentParam) {
      return null;
    }

    const format = contentParam.format ?? defaultFormat;
    const language = contentParam.language ?? LanguageEnum['zh-CN'];

    // 使用 createContentHandler 处理内容
    const contentHandler = createContentHandler({
      format,
      raw: contentParam.raw,
      plain: contentParam.plain,
    });

    const processedContent = contentHandler.toVO();

    // 创建 BlockContent 实体
    const content = BlockContent.create({
      blockId,
      language,
      format,
      raw: processedContent.raw,
      plain: processedContent.plain,
      status: ProcessStatusEnum.DONE,
    });

    // 保存内容到数据库
    await this.contentRepository.save(content);

    return content;
  }
}

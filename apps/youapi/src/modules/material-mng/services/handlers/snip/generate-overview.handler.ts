import { Injectable, NotFoundException } from '@nestjs/common';
import { CommandHandler, ICommandHandler } from '@nestjs/cqrs';
import { throttle } from 'lodash';
import { Observable } from 'rxjs';
import { AILanguageEnum, ProcessStatusEnum } from '@/common/types';
import { OverviewService } from '@/modules/ai/services/overview.service';
import { UserPreferenceRepository } from '@/modules/iam/repositories/user-preference.repository';
import { BlockContent } from '@/modules/material-mng/domain/block/models/block-content.entity';
import { Overview } from '../../../domain/block/models/overview.entity';
import { BlockRepository } from '../../../repositories/block.repository';
import { BlockContentRepository } from '../../../repositories/block-content.repository';
import { SnipRepository } from '../../../repositories/snip.repository';
import { GenerateOverviewCommand } from '../../commands/snip/generate-overview.command';

@Injectable()
@CommandHandler(GenerateOverviewCommand)
export class GenerateOverviewHandler implements ICommandHandler<GenerateOverviewCommand> {
  private readonly THROTTLE_TIME = 1000; // 1 second

  constructor(
    private readonly snipRepository: SnipRepository,
    private readonly blockRepository: BlockRepository,
    private readonly contentRepository: BlockContentRepository,
    private readonly overviewService: OverviewService,
    private readonly userPreferenceRepository: UserPreferenceRepository,
  ) {}

  async execute(command: GenerateOverviewCommand) {
    const { snipId, userId, spaceId, responseLanguage, regenerate } = command;

    // 1. 获取 snip 实体并验证权限
    const snip = await this.snipRepository.findById(snipId);
    if (!snip || snip.spaceId !== spaceId) {
      throw new NotFoundException(`Snip with id ${snipId} not found`);
    }

    // 2. 获取用户偏好设置
    const userPreference = await this.userPreferenceRepository.getByUserId(userId);

    // 5. 准备页面数据和语言设置
    const pageData = await this.preparePageData(snip);
    const aiLanguage =
      AILanguageEnum[responseLanguage] ||
      userPreference?.aiResponseLanguage ||
      AILanguageEnum.system;
    const aiSecondLanguage = userPreference?.ai2ndResponseLanguage;
    const enableBilingual = userPreference?.enableBilingual ?? true;

    // 6. 生成概览并处理流式输出
    const observable = await this.overviewService.overview({
      pageData,
      aiLanguage,
      aiSecondLanguage,
      enableBilingual,
      options: {
        regenerate,
        useCache: true,
      },
    });

    // 启动数据库持久化（作为另一个订阅者）
    //   if (shouldGenerate) {
    //     // 3. 获取或创建 overview block
    //     const { block, content, shouldGenerate } = await this.getOrCreateOverviewBlock(
    //       snipId,
    //       responseLanguage || userPreference?.aiResponseLanguage || AILanguageEnum.system,
    //       regenerate
    //     );
    // this.subscribeToDatabasePersistence(stream, content, block);
    //   }

    // 返回原始流供控制器使用
    return observable;
  }

  // private async getOrCreateOverviewBlock(
  //   snipId: string,
  //   language: AILanguageEnumKeys,
  //   regenerate: boolean
  // ): Promise<{ block: Overview; content: Content; shouldGenerate: boolean }> {
  //   // 查找现有的 overview block
  //   const existingBlocks = await this.blockRepository.findBySnipIdAndType(snipId, SnipFeatureEnum.OVERVIEW);

  //   let block: Overview;
  //   let content: Content;
  //   let shouldGenerate = true;

  //   if (existingBlocks.length > 0) {
  //     block = existingBlocks[0] as Overview;

  //     // 查找对应语言的 content
  //     const contents = await this.contentRepository.findByBlockId(block.id);
  //     const existingContent = contents.find(c =>
  //       c.language === language &&
  //       [ProcessStatusEnum.ING, ProcessStatusEnum.DONE].includes(c.status as ProcessStatusEnum)
  //     );

  //     if (existingContent) {
  //       content = existingContent;
  //       if (regenerate) {
  //         // 重新生成：清空现有内容并重置状态
  //         await this.contentRepository.clearContent(content.id);
  //         content = await this.contentRepository.getById(content.id);
  //         shouldGenerate = true;
  //       } else if (content.status === ProcessStatusEnum.DONE) {
  //         // 已完成且不重新生成：直接返回
  //         shouldGenerate = false;
  //       }
  //     } else {
  //       // 创建新的 content
  //       content = await this.contentRepository.create({
  //         blockId: block.id,
  //         snipId: snipId,
  //         format: ContentFormatEnum.LLM_OUTPUT,
  //         raw: '',
  //         plain: '',
  //         language: language as LanguageEnum,
  //         status: ProcessStatusEnum.ING,
  //       });
  //     }
  //   } else {
  //     // 创建新的 block 和 content
  //     block = await this.blockRepository.createOverview({
  //       snipId,
  //       type: SnipFeatureEnum.OVERVIEW,
  //       display: 'SHOW',
  //     });

  //     content = await this.contentRepository.create({
  //       blockId: block.id,
  //       snipId: snipId,
  //       format: ContentFormatEnum.LLM_OUTPUT,
  //       raw: '',
  //       plain: '',
  //       language: language as LanguageEnum,
  //       status: ProcessStatusEnum.ING,
  //     });
  //   }

  //   return { block, content, shouldGenerate };
  // }

  /**
   * 订阅流进行数据库持久化
   * 这是对生成流的一个独立订阅，不会影响原始流
   */
  private subscribeToDatabasePersistence(
    stream: Observable<any>,
    content: BlockContent,
    block: Overview,
  ): void {
    let accumulatedText = '';

    // 创建节流的数据库更新函数
    const throttledUpdateContent = throttle(
      async () => {
        if (accumulatedText) {
          await this.contentRepository.updateContent(content.id, {
            raw: accumulatedText,
            plain: accumulatedText, // For overview, raw and plain are the same
          });
        }
      },
      this.THROTTLE_TIME,
      {
        leading: false,
        trailing: true,
      },
    );

    // 订阅流进行数据库持久化
    stream.subscribe({
      next: (chunk: any) => {
        // 只处理文本内容
        if (chunk && chunk.data && typeof chunk.data === 'string') {
          accumulatedText += chunk.data;
          // 节流更新数据库
          throttledUpdateContent();
        }
      },
      error: async (error: any) => {
        console.error('Error during overview generation (DB persistence):', error);

        // 标记内容为失败状态
        await this.contentRepository
          .updateContent(content.id, {
            status: ProcessStatusEnum.FAIL,
          })
          .catch((e) => console.error('Failed to update content status to failed:', e));
      },
      complete: async () => {
        try {
          // 确保最后一次更新被执行
          await throttledUpdateContent.flush();

          // 标记内容为完成状态
          await this.contentRepository.updateContent(content.id, {
            status: ProcessStatusEnum.DONE,
            raw: accumulatedText,
            plain: accumulatedText,
          });

          // 更新 block 的当前内容 ID
          await this.blockRepository.updateCurrentContentId(block.id, content.id);

          console.log(`Overview generation completed and persisted for content ${content.id}`);
        } catch (dbError) {
          console.error('Error finalizing database persistence:', dbError);
        }
      },
    });
  }

  private async preparePageData(snip: any): Promise<any> {
    // 准备与 youapp 兼容的页面数据结构
    const pageData: any = {
      id: snip.id,
      type: snip.type,
      title: snip.title,
      url: snip.webpageUrl,
      webpage: snip.webpageUrl
        ? {
            url: snip.webpageUrl,
            normalized_url: snip.webpageNormalizedUrl,
            title: snip.webpageTitle,
            description: snip.webpageDescription,
            site_name: snip.webpageSiteName,
            site_host: snip.webpageSiteHost,
            site_favicon_url: snip.webpageSiteFaviconUrl,
          }
        : undefined,
    };

    // 添加内容信息
    if (snip.contentRaw || snip.contentPlain) {
      pageData.content = {
        raw: snip.contentRaw,
        plain: snip.contentPlain,
        language: snip.contentLanguage,
      };
      pageData.content_raw = snip.contentRaw;
      pageData.content_plain = snip.contentPlain;
    }

    // 处理特定类型的额外信息
    if (snip.type === 'video' || snip.type === 'voice') {
      // TODO: 获取转录信息
      // const transcript = await this.getTranscript(snip.id);
      // if (transcript) {
      //   pageData.transcript = transcript;
      // }
    }

    // 添加作者、发布日期等元信息
    if (snip.authors) {
      pageData.authors = snip.authors;
    }
    if (snip.publishedAt) {
      pageData.published_at = snip.publishedAt;
    }

    return pageData;
  }
}

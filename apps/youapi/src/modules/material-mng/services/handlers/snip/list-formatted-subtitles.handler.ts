import { <PERSON><PERSON>uery<PERSON><PERSON><PERSON>, QueryHandler } from '@nestjs/cqrs';
import { ContentFormatEnum } from '@repo/common';
import { FormattedSubtitleContentDto } from '../../../dto/snip/list-formatted-subtitles.dto';
import { BlockContentRepository } from '../../../repositories/block-content.repository';
import { SnipRepository } from '../../../repositories/snip.repository';
import { ListFormattedSubtitlesQuery } from '../../queries/snip/list-formatted-subtitles.query';

/**
 * 列出格式化字幕处理器
 *
 * 功能：
 * 1. 验证用户对 snip 的访问权限
 * 2. 查询指定 snip 的所有格式化字幕内容
 * 3. 支持按语言过滤
 * 4. 清理超时的处理中内容（超过5分钟的ING状态内容）
 * 5. 返回格式化字幕列表
 */
@QueryHandler(ListFormattedSubtitlesQuery)
export class ListFormattedSubtitlesHandler implements IQueryHandler<ListFormattedSubtitlesQuery> {
  constructor(
    private readonly contentRepository: BlockContentRepository,
    private readonly snipRepository: SnipRepository,
  ) {}

  async execute(query: ListFormattedSubtitlesQuery): Promise<FormattedSubtitleContentDto[]> {
    const { snipId, language } = query;

    // 1. 验证 snip 是否存在（这也验证了用户权限）
    await this.snipRepository.getById(snipId);
    // TODO 权限验证： await authzDomain.authzSnip(userId, snip);

    // 2. 查询格式化字幕内容
    const contents = await this.contentRepository.findBySnipId(snipId, {
      language,
      format: ContentFormatEnum.SUBTITLE_FORMATTED,
    });

    // 3. 检查并清理超时的处理中内容（模拟原逻辑）
    const now = Date.now();
    const timeoutContents = contents.filter((content) => {
      const diff = now - content.updatedAt.getTime();
      return diff > 1000 * 60 * 5; // 5分钟超时
    });

    if (timeoutContents.length > 0) {
      const timeoutIds = timeoutContents.map((content) => content.id);
      await this.contentRepository.deleteMany(timeoutIds);
    }

    // 4. 过滤掉已超时的内容，转换为 DTO
    const validContents = contents.filter((content) => {
      const diff = now - content.updatedAt.getTime();
      return diff <= 1000 * 60 * 5; // 未超时的内容
    });

    return this.convertToDto(validContents);
  }

  /**
   * 将 Content 实体转换为 DTO
   */
  private convertToDto(contents: any[]): FormattedSubtitleContentDto[] {
    return contents.map((content) => ({
      id: content.id,
      blockId: content.blockId,
      format: content.format,
      raw: content.raw,
      plain: content.plain,
      status: content.status,
      language: content.language,
      traceId: content.traceId,
    }));
  }
}

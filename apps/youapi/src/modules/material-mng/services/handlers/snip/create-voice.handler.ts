/**
 * Create Voice Handler - 创建语音处理器
 * 从 youapp 迁移的创建语音处理器
 *
 * Migrated from:
 * - /youapp/src/lib/app/snip/index.ts (createVoice)
 */

import { BadRequestException, Logger } from '@nestjs/common';
import { CommandBus, CommandHandler, EventBus, ICommandHandler } from '@nestjs/cqrs';
import {
  ContentFormatEnum,
  createContentHandler,
  DefaultLanguageKey,
  ProcessStatusEnum,
} from '@repo/common';
import { runInBackground } from '@/common/errors';
import { BlockDisplayEnum, SnipFeatureEnum } from '@/common/types/snip.types';
import { QuotaResourceEnum } from '@/common/types/subscription.types';
import { getCallbackOrigin } from '@/common/utils/callback';
import { StorageUsageFromEnum } from '@/dao/usage-record/types';
import { ChatDomainService } from '@/domain/chat';
import { FileDomainService } from '@/domain/file';
import { SpaceDomainService } from '@/domain/space';
import { UsageRecordDomainService } from '@/domain/usage-record';
import { Youget } from '@/infra/youget';
import { Block } from '@/modules/material-mng/domain/block/models/block.entity';
import { BlockContent } from '@/modules/material-mng/domain/block/models/block-content.entity';
import { UploadFileMeta } from '@/modules/material-mng/domain/snip/models/type';
import { Voice } from '@/modules/material-mng/domain/snip/models/voice.entity';
import { VoiceDto } from '@/modules/material-mng/dto/snip/voice.dto';
import { BlockRepository } from '@/modules/material-mng/repositories/block.repository';
import { BlockContentRepository } from '@/modules/material-mng/repositories/block-content.repository';
import { BoardRepository } from '@/modules/material-mng/repositories/board.repository';
import { BoardPositionDomainService } from '../../../domain/shared/board-position.service';
import { SnipStatus } from '../../../domain/snip/models/snip.entity';
import { SnipRepository } from '../../../repositories/snip.repository';
import { CreateSnipBlocksCommand } from '../../commands/snip/create-snip-blocks.command';
import { CreateVoiceCommand } from '../../commands/snip/create-voice.command';
import { SnipDtoService } from '../../dto-services/snip-dto.service';

@CommandHandler(CreateVoiceCommand)
export class CreateVoiceHandler implements ICommandHandler<CreateVoiceCommand> {
  private static readonly logger = new Logger(CreateVoiceHandler.name);

  constructor(
    private readonly snipRepository: SnipRepository,
    private readonly boardPositionService: BoardPositionDomainService,
    private readonly snipDtoService: SnipDtoService,
    private readonly eventBus: EventBus,
    private readonly boardRepository: BoardRepository,
    private readonly youget: Youget,
    private readonly blockRepository: BlockRepository,
    private readonly contentRepository: BlockContentRepository,
    private readonly chatDomainService: ChatDomainService,
    private readonly commandBus: CommandBus,
    private readonly fileDomainService: FileDomainService,
    private readonly usageRecordDomainService: UsageRecordDomainService,
    private readonly spaceDomainService: SpaceDomainService,
  ) {}

  async execute(command: CreateVoiceCommand): Promise<VoiceDto> {
    const {
      spaceId,
      creatorId,
      title,
      webpage,
      showNotes,
      authors,
      heroImageUrl,
      publishedAt,
      playUrl,
      extra,
      file,
      boardId: originalBoardId,
      parentBoardGroupId,
      chatId,
    } = command;

    // 检查配额
    const space = await this.spaceDomainService.get(spaceId);
    if (space) {
      await this.usageRecordDomainService.checkQuota(space, QuotaResourceEnum.STORAGE);
    }

    // 确定 boardId：如果指定了则使用，否则获取默认 board
    let boardId: string;
    if (originalBoardId) {
      await this.boardRepository.getById(originalBoardId);
      boardId = originalBoardId;
    } else {
      boardId = (await this.boardRepository.getDefaultBoardBySpaceId(spaceId)).id;
    }

    // 提取 transcript/overview 数据并准备用于 Voice.create 的 extra 字段
    let voiceExtra = extra;
    let snipBlocksData = null;

    // 如果 extra 是对象且包含 transcript 或 overview 数据，提取并处理
    if (
      extra &&
      typeof extra === 'object' &&
      (extra.transcript_contents || extra.overview_contents)
    ) {
      snipBlocksData = {
        transcript_contents: extra.transcript_contents || [],
        overview_contents: extra.overview_contents || [],
      };
      voiceExtra = undefined; // Voice 不需要存储这些数据到 extra 字段
    }

    let voice: Voice;

    if (file?.hash !== undefined) {
      // 本地文件路径
      voice = await this.createLocalVoice(
        {
          spaceId,
          creatorId,
          title,
          heroImageUrl,
          authors,
          publishedAt,
          playUrl,
          file,
          boardId,
          parentBoardGroupId,
        },
        boardId,
      );
    } else if (webpage) {
      // 在线语音路径
      voice = await this.createOnlineVoice(
        {
          spaceId,
          creatorId,
          title,
          webpage,
          showNotes,
          authors,
          heroImageUrl,
          publishedAt,
          playUrl,
          boardId,
          parentBoardGroupId,
        },
        boardId,
      );
    } else {
      throw new BadRequestException('Either file or webpage must be provided for voice creation');
    }

    CreateVoiceHandler.logger.debug(`before save Voice: ${JSON.stringify(voice)}`);

    // 检查是否需要转存图片
    const imageUrls = voice.extractImageUrls() || [];

    if (imageUrls.length > 0) {
      // 设置状态为图片转存中
      voice.update({ status: SnipStatus.IMAGE_TRANSFERING });

      // 保存语音
      await this.snipRepository.save(voice);

      // 实现图片转存功能
      this.transferImagesInBackground(imageUrls, voice.id!, creatorId);
    } else {
      // 直接保存语音
      await this.snipRepository.save(voice);
    }

    // 创建语音内容 block（如果是在线语音且有 showNotes）
    if (showNotes) {
      await this.createVoiceContentBlock(voice, showNotes);
    }

    // 如果有 transcript 或 overview 内容，调用 CreateSnipBlocksHandler
    if (
      snipBlocksData &&
      (snipBlocksData.transcript_contents.length > 0 || snipBlocksData.overview_contents.length > 0)
    ) {
      const createSnipBlocksCommand = new CreateSnipBlocksCommand(voice.id!, snipBlocksData);

      await this.commandBus.execute(createSnipBlocksCommand);
      CreateVoiceHandler.logger.log(`Created snip blocks for voice: ${voice.id}`);
    }

    // 绑定聊天
    if (chatId && voice.id) {
      await this.chatDomainService.bindWebpageChatToSnip({
        snip_id: voice.id!,
        chat_id: chatId,
      });
    }

    CreateVoiceHandler.logger.debug(`Voice snip created successfully: ${voice.id}`);

    // 返回 DTO，与 youapp 的 snip2vo 功能保持一致
    const dto = this.snipDtoService.toDto(voice);

    return {
      ...dto,
      boardIds: boardId ? [boardId] : [], // 兼容旧数据，实际取 boardId 即可
    };
  }

  /**
   * 创建本地语音
   */
  private async createLocalVoice(
    params: {
      spaceId: string;
      creatorId: string;
      title?: string;
      heroImageUrl?: string;
      authors?: any[];
      publishedAt?: Date;
      playUrl?: string;
      file: any;
      boardId: string;
      parentBoardGroupId?: string;
    },
    boardId: string,
  ): Promise<Voice> {
    const { spaceId, creatorId, file } = params;

    // 获取文件元信息
    const fileMeta = await this.fileDomainService.getFileMeta(
      file.name,
      file.hash,
      file.isPublic ? 'public' : 'private',
      file.directory,
    );

    // 记录存储使用量
    runInBackground(
      this.usageRecordDomainService.createStorageUsageRecord({
        space_id: spaceId,
        user_id: creatorId,
        from: StorageUsageFromEnum.SNIP,
        file: {
          name: fileMeta.name,
          mime_type: fileMeta.mime_type,
          size: fileMeta.size,
          storage_url: fileMeta.storage_url,
        },
      }),
    );

    // 创建本地语音实体
    const voice = await Voice.createLocalVoice(
      {
        spaceId,
        creatorId,
        title: params.title,
        file: <UploadFileMeta>{
          name: fileMeta.name,
          mimeType: fileMeta.mime_type,
          size: fileMeta.size,
          storageUrl: fileMeta.storage_url,
        },
        heroImageUrl: params.heroImageUrl,
        authors: params.authors,
        publishedAt: params.publishedAt,
        playUrl: params.playUrl,
        boardId: params.boardId,
        parentBoardGroupId: params.parentBoardGroupId,
      },
      boardId ? this.boardPositionService : undefined,
    );

    return voice;
  }

  /**
   * 创建在线语音
   */
  private async createOnlineVoice(
    params: {
      spaceId: string;
      creatorId: string;
      title?: string;
      webpage: any;
      showNotes?: any;
      authors?: any[];
      heroImageUrl?: string;
      publishedAt?: Date;
      playUrl?: string;
      boardId: string;
      parentBoardGroupId?: string;
    },
    boardId: string,
  ): Promise<Voice> {
    // 创建在线语音实体
    const voice = await Voice.createOnlineVoice(
      {
        spaceId: params.spaceId,
        creatorId: params.creatorId,
        title: params.title,
        webpage: params.webpage,
        showNotes: params.showNotes,
        authors: params.authors,
        heroImageUrl: params.heroImageUrl,
        publishedAt: params.publishedAt,
        playUrl: params.playUrl,
        boardId: params.boardId,
        parentBoardGroupId: params.parentBoardGroupId,
      },
      boardId ? this.boardPositionService : undefined,
    );

    return voice;
  }

  /**
   * 创建语音内容 block
   * 基于 youapp 的 createSnipBlocks 实现
   */
  private async createVoiceContentBlock(voice: Voice, showNotes: any): Promise<void> {
    // 使用 createContentHandler 创建内容处理器
    const contentHandler = createContentHandler({
      format: ContentFormatEnum.READER_HTML,
      raw: showNotes.getRaw(),
      plain: showNotes.getPlain(),
      language: showNotes.getLanguage() || DefaultLanguageKey,
    });

    // 创建 block
    const block = Block.create({
      snipId: voice.id,
      type: SnipFeatureEnum.RAW_SUMMARY, // 语音内容使用 RAW_SUMMARY 类型
      display: BlockDisplayEnum.SHOW,
    });

    // 保存 block
    await this.blockRepository.save(block);

    // 创建 content
    const content = BlockContent.create({
      blockId: block.id,
      language: contentHandler.toVO().language || DefaultLanguageKey,
      format: contentHandler.toVO().format,
      raw: contentHandler.toVO().raw,
      plain: contentHandler.toVO().plain,
      status: ProcessStatusEnum.DONE,
    });
    // 目前领域实体未直接 add content 方法，需要手动添加
    block.addContent(content);

    // 保存 content
    await this.contentRepository.save(content);

    // 设置当前内容
    block.setCurrentContent(content.id);
    await this.blockRepository.save(block);

    CreateVoiceHandler.logger.debug(`Voice content block created for snip: ${voice.id}`);
  }

  /**
   * 将图片转存到后台
   */
  transferImagesInBackground(imageUrls: string[], snip_id: string, user_id: string) {
    CreateVoiceHandler.logger.debug(
      'this.youget.executeExtractTextTaskInBackground = ' +
        this.youget.executeSaveImageTaskInBackground,
    );
    this.youget.executeSaveImageTaskInBackground(
      {
        image_urls: imageUrls,
        youget_callback_url: `${getCallbackOrigin()}/webhook/v1/images-transfered?snip_id=${snip_id}`,
        youget_callback_method: 'PUT',
      },
      user_id,
    );
  }
}

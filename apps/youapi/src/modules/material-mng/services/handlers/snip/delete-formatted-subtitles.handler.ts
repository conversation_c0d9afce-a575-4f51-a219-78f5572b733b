/**
 * Delete Formatted Subtitles Handler - 删除格式化字幕处理器
 * 从 youapp 迁移的删除格式化字幕处理器
 *
 * Migrated from:
 * - /youapp/src/lib/app/snip/index.ts (deleteFormattedSubtitles)
 */

import { Logger } from '@nestjs/common';
import { CommandHandler, ICommandHandler } from '@nestjs/cqrs';
import { ContentFormatEnum } from '@repo/common';
import { BlockRepository } from '../../../repositories/block.repository';
import { BlockContentRepository } from '../../../repositories/block-content.repository';
import { SnipRepository } from '../../../repositories/snip.repository';
import { DeleteFormattedSubtitlesCommand } from '../../commands/snip/delete-formatted-subtitles.command';

@CommandHandler(DeleteFormattedSubtitlesCommand)
export class DeleteFormattedSubtitlesHand<PERSON>
  implements ICommandHandler<DeleteFormattedSubtitlesCommand, number>
{
  private readonly logger = new Logger(DeleteFormattedSubtitlesHandler.name);

  constructor(
    private readonly snipRepository: SnipRepository,
    private readonly blockRepository: BlockRepository,
    private readonly contentRepository: BlockContentRepository,
  ) {}

  async execute(command: DeleteFormattedSubtitlesCommand): Promise<number> {
    const { snipId, spaceId, userId, language } = command;

    this.logger.debug(
      `Deleting formatted subtitles for snip: ${snipId}, language: ${language || 'all'}`,
    );

    // 1. 验证 snip 存在并获取snip
    const snip = await this.snipRepository.getById(snipId);

    // TODO 权限验证，这里应该有权限验证，确保用户有权限修改这个 snip
    // await authzDomain.authzSnip(userId, snip);

    // 2. 获取所有关联的 blocks
    const blocks = await this.blockRepository.findBySnipId(snip.id);

    if (blocks.length === 0) {
      this.logger.debug(`No blocks found for snip: ${snipId}`);
      return 0;
    }

    // 3. 获取所有 block 的 contents
    const blockIds = blocks.map((block) => block.id);
    const allContents = await this.contentRepository.findByFilter({
      blockIds,
    });

    // 4. 过滤出格式化字幕内容
    const formattedSubtitles = allContents.filter((content) => {
      const isFormattedSubtitle = content.format === ContentFormatEnum.SUBTITLE_FORMATTED;

      if (language) {
        return isFormattedSubtitle && content.language === language;
      }

      return isFormattedSubtitle;
    });

    // 5. 删除格式化字幕内容
    if (formattedSubtitles.length > 0) {
      const contentIds = formattedSubtitles.map((content) => content.id);
      await this.contentRepository.deleteMany(contentIds);

      this.logger.debug(
        `Deleted ${formattedSubtitles.length} formatted subtitles for snip: ${snipId}`,
      );
    } else {
      this.logger.debug(`No formatted subtitles found for snip: ${snipId}`);
    }

    // 6. 返回删除的数量
    return formattedSubtitles.length;
  }
}

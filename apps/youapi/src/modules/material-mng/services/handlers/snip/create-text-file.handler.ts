/**
 * Create Text File Handler - 创建文本文件处理器
 * 从 youapp 迁移的创建文本文件处理器
 *
 * Migrated from:
 * - /youapp/src/lib/app/snip/helpers.ts (createTextFile)
 */

import { BadRequestException, Logger } from '@nestjs/common';
import { CommandBus, CommandHandler, EventBus, ICommandHandler } from '@nestjs/cqrs';
import { runInBackground } from '@/common/errors/error-handler';
import { ChatDomainService } from '@/domain/chat';
import { FileDomainService } from '@/domain/file';
import { SpaceDomainService } from '@/domain/space';
import { QuotaResourceEnum } from '@/domain/subscription/types';
import { UsageRecordDomainService } from '@/domain/usage-record';
import { StorageUsageFromEnum } from '@/domain/usage-record/types';
import { Youget } from '@/infra/youget';
import { TextDto } from '@/modules/material-mng/dto/snip/text.dto';
import { BlockRepository } from '@/modules/material-mng/repositories/block.repository';
import { BlockContentRepository } from '@/modules/material-mng/repositories/block-content.repository';
import { BoardRepository } from '@/modules/material-mng/repositories/board.repository';
import { BoardPositionDomainService } from '../../../domain/shared/board-position.service';
import { TextFile } from '../../../domain/snip/models/text-file.entity';
import { SnipRepository } from '../../../repositories/snip.repository';
import { CreateTextFileCommand } from '../../commands/snip/create-text-file.command';
import { SnipDtoService } from '../../dto-services/snip-dto.service';

@CommandHandler(CreateTextFileCommand)
export class CreateTextFileHandler implements ICommandHandler<CreateTextFileCommand> {
  private static readonly logger = new Logger(CreateTextFileHandler.name);

  constructor(
    private readonly snipRepository: SnipRepository,
    private readonly boardPositionService: BoardPositionDomainService,
    private readonly snipDtoService: SnipDtoService,
    private readonly eventBus: EventBus,
    private readonly boardRepository: BoardRepository,
    private readonly youget: Youget,
    private readonly blockRepository: BlockRepository,
    private readonly contentRepository: BlockContentRepository,
    private readonly chatDomainService: ChatDomainService,
    private readonly commandBus: CommandBus,
    private readonly fileDomainService: FileDomainService,
    private readonly usageRecordDomainService: UsageRecordDomainService,
    private readonly spaceDomainService: SpaceDomainService,
  ) {}

  async execute(command: CreateTextFileCommand): Promise<TextDto> {
    const {
      spaceId,
      creatorId,
      file,
      content,
      title,
      fileType,
      boardId: originalBoardId,
      parentBoardGroupId,
      chatId,
    } = command;

    // 验证必填字段
    if (!file || !content) {
      throw new BadRequestException('File meta and content are required for text file');
    }

    // 检查配额
    const space = await this.spaceDomainService.get(spaceId);
    if (space) {
      await this.usageRecordDomainService.checkQuota(space, QuotaResourceEnum.STORAGE);
    }

    // 确定 boardId：如果指定了则使用，否则获取默认 board
    let boardId: string;
    if (originalBoardId) {
      await this.boardRepository.getById(originalBoardId);
      boardId = originalBoardId;
    } else {
      boardId = (await this.boardRepository.getDefaultBoardBySpaceId(spaceId)).id;
    }

    // 获取文件元信息
    const fileMetaRaw = await this.fileDomainService.getFileMeta(
      file.name,
      file.hash,
      file.isPublic ? 'public' : 'private',
    );

    // 转换为 UploadFileMeta 格式（snake_case -> camelCase）
    const fileMeta = {
      name: fileMetaRaw.name,
      mimeType: fileMetaRaw.mime_type,
      size: fileMetaRaw.size,
      storageUrl: fileMetaRaw.storage_url,
    };

    // 创建文本文件实体，与 youapp 保持一致
    const textFile = await TextFile.create(
      {
        spaceId,
        creatorId,
        title,
        file: fileMeta,
        content, // TextFile 内部会处理 content 转换
        extra: undefined, // 文本文件通常不需要额外数据
        boardId,
        parentBoardGroupId,
      },
      boardId ? this.boardPositionService : undefined,
    );

    CreateTextFileHandler.logger.debug(`before save TextFile: ${JSON.stringify(textFile)}`);

    // 保存文本文件
    await this.snipRepository.save(textFile);

    // 记录 usage_record
    runInBackground(
      this.usageRecordDomainService.createStorageUsageRecord({
        space_id: spaceId,
        user_id: creatorId,
        from: StorageUsageFromEnum.SNIP,
        file: {
          name: fileMeta.name,
          mime_type: fileMeta.mimeType,
          size: fileMeta.size,
          storage_url: fileMeta.storageUrl,
        },
      }),
    );

    // 返回 DTO，与 youapp 的 snip2vo 功能保持一致
    const dto = this.snipDtoService.toDto(textFile);

    return {
      ...dto,
      boardIds: boardId ? [boardId] : [], // 兼容旧数据，实际取 boardId 即可
    } as TextDto;
  }
}

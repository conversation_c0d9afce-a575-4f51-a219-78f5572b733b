/**
 * CallOverviewHandler - 调用概览生成处理器
 * 基于 youapp 中 Snip.callOverview 方法的 CQRS 实现
 * 使用专门的 ExtractContentHandler 和 ExtractTranscriptHandler 简化逻辑
 */

import { BadRequestException, Injectable, NotFoundException } from '@nestjs/common';
import { CommandBus, CommandHandler, ICommandHandler } from '@nestjs/cqrs';
import { ContentFormatEnum, ContentHandler, createContentHandler } from '@repo/common';
import { SubtitleHandler } from '@repo/common/content/subtitle';
import { TextStreamPart } from 'ai';
import { Observable } from 'rxjs';
import { AILanguageEnum } from '@/common/types';
import { UserDomainService } from '@/domain/user';
import { OverviewService } from '@/modules/ai/services/overview.service';
import { BlockContent } from '@/modules/material-mng/domain/block/models';
import { Article } from '@/modules/material-mng/domain/snip/models/article.entity';
import { PDF } from '@/modules/material-mng/domain/snip/models/pdf.entity';
import { Video } from '@/modules/material-mng/domain/snip/models/video.entity';
import { Voice } from '@/modules/material-mng/domain/snip/models/voice.entity';
import { Snip, SnipType } from '../../../domain/snip/models/snip.entity';
import { BlockRepository } from '../../../repositories/block.repository';
import { SnipRepository } from '../../../repositories/snip.repository';
import { CallOverviewCommand } from '../../commands/snip/call-overview.command';
import { ExtractContentCommand } from '../../commands/snip/extract-content.command';
import {
  ExtractTranscriptCommand,
  OnWebhookCompletionActionEnum,
} from '../../commands/snip/extract-transcript.command';

// 定义 Processing 异常，用于表示需要等待转录完成
export class ProcessingException extends BadRequestException {
  constructor(message: string = 'Processing in progress') {
    super(message);
  }
}

@Injectable()
@CommandHandler(CallOverviewCommand)
export class CallOverviewHandler implements ICommandHandler<CallOverviewCommand> {
  constructor(
    private readonly snipRepository: SnipRepository,
    private readonly blockRepository: BlockRepository,
    private readonly overviewService: OverviewService,
    private readonly userDomainService: UserDomainService,
    private readonly commandBus: CommandBus,
  ) {}

  async execute(
    command: CallOverviewCommand,
  ): Promise<Observable<TextStreamPart<Record<string, any>>>> {
    const { snipId, userId, spaceId, responseLanguage, secondResponseLanguage, regenerate } =
      command;

    // 1. 获取 snip 实体并验证权限
    const snip = await this.snipRepository.findById(snipId);
    if (!snip || snip.spaceId !== spaceId) {
      throw new NotFoundException(`Snip with id ${snipId} not found`);
    }

    // 2. 获取语言偏好设置
    const { aiLanguage, aiSecondLanguage } = await this.resolveLanguageSettings(
      snip,
      userId,
      spaceId,
      responseLanguage,
      secondResponseLanguage,
    );
    const enableBilingual = !!aiSecondLanguage;

    // 3. 根据 Snip 类型准备页面数据
    // TODO pageData 中包含 snake-case 的 key，比如 show_notes
    const pageData = await this.preparePageData(snip, userId);

    // 如果没有内容（如空 PDF），返回空生成器
    if (!pageData) {
      return new Observable((subscriber) => subscriber.complete());
    }

    // 4. 生成概览并返回流式输出
    return await this.overviewService.overview({
      pageData,
      aiLanguage,
      aiSecondLanguage,
      enableBilingual,
      options: {
        regenerate: regenerate ?? false,
        useCache: true,
      },
    });
  }

  /**
   * 根据 Snip 类型准备页面数据
   */
  private async preparePageData(snip: any, userId: string): Promise<any | null> {
    switch (snip.type) {
      case SnipType.VIDEO:
        return this.prepareVideoPageData(snip, userId);

      case SnipType.VOICE:
        return this.prepareVoicePageData(snip, userId);

      case SnipType.ARTICLE:
        return this.prepareArticlePageData(snip, userId);

      case SnipType.PDF:
        return this.preparePDFPageData(snip, userId);

      default:
        // 默认使用基于内容的方式（Snippet 等）
        return this.prepareDefaultPageData(snip, userId);
    }
  }

  /**
   * 准备 Video 类型的页面数据
   */
  private async prepareVideoPageData(snip: Video, userId: string): Promise<any> {
    const transcript = await this.getTranscriptContent(snip, userId);

    return {
      type: snip.type,
      webpage: snip.webpage,
      show_notes: snip.description,
      transcript: transcript, // 直接传递 transcript 对象，保持与 youapp 一致
      title: snip.webpage?.getTitle() || snip.title,
    };
  }

  /**
   * 准备 Voice 类型的页面数据
   */
  private async prepareVoicePageData(snip: Voice, userId: string): Promise<any> {
    const transcript = await this.getTranscriptContent(snip, userId);

    const pageData: any = {
      type: snip.type,
      webpage: snip.webpage,
      show_notes: snip.showNotes,
      transcript: transcript, // 直接传递 transcript 对象，保持与 youapp 一致
      title: snip.webpage?.getTitle() || snip.title,
    };

    // Voice 类型添加多发言人信息到 pageData 中
    const speakerLabels = this.extractSpeakerLabels(transcript.raw || '');
    if (speakerLabels) {
      pageData.speakerLabels = speakerLabels;
    }

    return pageData;
  }

  /**
   * 准备 Article 类型的页面数据
   */
  private async prepareArticlePageData(snip: Article, userId: string): Promise<any> {
    const content = await this.getContentWithSummaryPriority(snip, userId);

    const pageData: any = {
      type: snip.type,
      content: content,
      title: snip.webpage?.getTitle() || snip.title,
    };

    // Article 类型添加 webpage 信息
    if (snip.webpage) {
      pageData.webpage = snip.webpage;
    }

    return pageData;
  }

  /**
   * 准备 PDF 类型的页面数据
   */
  private async preparePDFPageData(snip: PDF, userId: string): Promise<any> {
    const content = await this.getContentWithSummaryPriority(snip, userId);

    // PDF 特殊处理：如果没有内容则返回 null
    if (!content) {
      return null;
    }

    return {
      type: snip.type,
      title: snip.title,
      content: content,
    };
  }

  /**
   * 准备默认类型（Snippet 等）的页面数据
   */
  private async prepareDefaultPageData(snip: any, userId: string): Promise<any> {
    const content = await this.extractSnipContent(snip.id, userId, snip.spaceId);
    return {
      type: snip.type,
      title: snip.title,
      content: content,
    };
  }

  /**
   * 从转录内容中提取多发言人标签（按照 youapp 逻辑）
   */
  private extractSpeakerLabels(transcriptRaw: string): string | undefined {
    const handler = SubtitleHandler.fromRaw(transcriptRaw);
    const cues = handler.getCues();

    // 多发言人场景处理
    const speakers: string[] = [];
    cues.forEach((cue) => {
      if (cue.speaker && !speakers.includes(cue.speaker)) {
        speakers.push(cue.speaker);
      }
    });

    return speakers.length > 1 ? speakers.length.toString() : undefined;
  }

  /**
   * 获取和解析语言设置，优先级：请求参数 > 用户偏好 > 系统默认
   */
  private async resolveLanguageSettings(
    snip: any,
    userId: string,
    spaceId: string,
    responseLanguage?: string,
    secondResponseLanguage?: string,
  ): Promise<{
    aiLanguage: keyof typeof AILanguageEnum;
    aiSecondLanguage: keyof typeof AILanguageEnum | null;
  }> {
    // 获取用户语言偏好
    const contentHandler = await this.extractSnipContent(snip.id, userId, spaceId);
    const languageResult = await this.userDomainService.getFullLLMResponseLanguage({
      user_id: userId,
      content: contentHandler,
    });

    // 主语言：请求参数 > 用户偏好 > 系统默认
    const aiLanguage =
      (responseLanguage as keyof typeof AILanguageEnum) ||
      ((typeof languageResult === 'object'
        ? languageResult.primary
        : languageResult) as keyof typeof AILanguageEnum) ||
      AILanguageEnum.system;

    // 次语言：请求参数 > 用户偏好
    const aiSecondLanguage =
      (secondResponseLanguage as keyof typeof AILanguageEnum) ||
      ((typeof languageResult === 'object' ? languageResult.secondary : null) as
        | keyof typeof AILanguageEnum
        | null);

    return { aiLanguage, aiSecondLanguage };
  }

  /**
   * 通过 CommandBus 提取 Snip 内容
   */
  private async extractSnipContent(
    snipId: string,
    userId: string,
    spaceId: string,
  ): Promise<ContentHandler | null> {
    const extractCommand = new ExtractContentCommand(snipId, userId, spaceId);
    return this.commandBus.execute(extractCommand);
  }

  /**
   * 获取转录内容，如果不存在则触发转录生成
   */
  private async getTranscriptContent(snip: Snip, userId: string): Promise<BlockContent> {
    // 查询转录内容
    const transcripts = await this.blockRepository.queryTranscriptContentBySnip(snip.id);

    if (!transcripts.length) {
      // 触发转录提取
      const extractCommand = new ExtractTranscriptCommand(
        snip.id,
        userId,
        snip.spaceId,
        OnWebhookCompletionActionEnum.CALL_OVERVIEW,
      );
      await this.commandBus.execute(extractCommand);
      throw new ProcessingException('Transcript is being generated, please try again later');
    }

    // 选择合适的转录内容：优先 SUBTITLE 格式，否则使用第一个
    return transcripts.find((t) => t.format === ContentFormatEnum.SUBTITLE) ?? transcripts[0];
  }

  /**
   * 获取内容，优先使用 summary，否则使用原始内容
   */
  private async getContentWithSummaryPriority(
    snip: Snip,
    userId: string,
  ): Promise<ContentHandler | null> {
    // 优先使用 summary 内容，否则使用原始内容
    const summary = await this.blockRepository.querySummaryContentBySnip(snip.id);

    if (summary?.length > 0) {
      const summaryContent = summary.find((s) => s.plain);
      if (summaryContent) {
        // 如果有 summary，使用 ContentHandler 包装
        return createContentHandler({
          raw: summaryContent.raw || summaryContent.plain || '',
          plain: summaryContent.plain,
          format: summaryContent.format || ContentFormatEnum.READER_HTML,
          language: summaryContent.language,
        });
      }
    }

    // 没有 summary 或 summary 为空，使用原始内容
    return this.extractSnipContent(snip.id, userId, snip.spaceId);
  }
}

/**
 * Create Other Webpage Handler - 创建其他网页处理器
 * 从 youapp 迁移的创建其他网页处理器
 *
 * Migrated from:
 * - /youapp/src/app/api/v1/createOtherWebpage/route.ts
 */

import { BadRequestException, Logger } from '@nestjs/common';
import { CommandBus, CommandHandler, EventBus, ICommandHandler } from '@nestjs/cqrs';
import {
  ContentFormatEnum,
  createContentHandler,
  DefaultLanguageKey,
  ProcessStatusEnum,
} from '@repo/common';
import { BlockDisplayEnum, SnipFeatureEnum } from '@/common/types/snip.types';
import { QuotaResourceEnum } from '@/common/types/subscription.types';
import { ChatDomainService } from '@/domain/chat';
import { SpaceDomainService } from '@/domain/space';
import { UsageRecordDomainService } from '@/domain/usage-record';
import { Block } from '@/modules/material-mng/domain/block/models/block.entity';
import { BlockContent } from '@/modules/material-mng/domain/block/models/block-content.entity';
import { OtherWebpage } from '@/modules/material-mng/domain/snip/models/other-webpage.entity';
import { OtherWebpageDto } from '@/modules/material-mng/dto/snip/other-webpage.dto';
import { BlockRepository } from '@/modules/material-mng/repositories/block.repository';
import { BlockContentRepository } from '@/modules/material-mng/repositories/block-content.repository';
import { BoardRepository } from '@/modules/material-mng/repositories/board.repository';
import { BoardPositionDomainService } from '../../../domain/shared/board-position.service';
import { SnipRepository } from '../../../repositories/snip.repository';
import { CreateOtherWebpageCommand } from '../../commands/snip/create-other-webpage.command';
import { CreateSnipBlocksCommand } from '../../commands/snip/create-snip-blocks.command';
import { SnipDtoService } from '../../dto-services/snip-dto.service';

@CommandHandler(CreateOtherWebpageCommand)
export class CreateOtherWebpageHandler implements ICommandHandler<CreateOtherWebpageCommand> {
  private static readonly logger = new Logger(CreateOtherWebpageHandler.name);

  constructor(
    private readonly snipRepository: SnipRepository,
    private readonly boardPositionService: BoardPositionDomainService,
    private readonly snipDtoService: SnipDtoService,
    private readonly eventBus: EventBus,
    private readonly boardRepository: BoardRepository,
    private readonly blockRepository: BlockRepository,
    private readonly contentRepository: BlockContentRepository,
    private readonly chatDomainService: ChatDomainService,
    private readonly commandBus: CommandBus,
    private readonly spaceDomainService: SpaceDomainService,
    private readonly usageRecordDomainService: UsageRecordDomainService,
  ) {}

  async execute(command: CreateOtherWebpageCommand): Promise<OtherWebpageDto> {
    const {
      spaceId,
      creatorId,
      webpage,
      extra,
      boardId: originalBoardId,
      parentBoardGroupId,
      chatId,
    } = command;

    // 验证必填字段
    if (!webpage) {
      throw new BadRequestException('Webpage metadata is required for other webpage creation');
    }

    // 检查配额
    const space = await this.spaceDomainService.get(spaceId);
    if (space) {
      await this.usageRecordDomainService.checkQuota(space, QuotaResourceEnum.STORAGE);
    }

    // 确定 boardId：如果指定了则使用，否则获取默认 board
    let boardId: string;
    if (originalBoardId) {
      await this.boardRepository.getById(originalBoardId);
      boardId = originalBoardId;
    } else {
      boardId = (await this.boardRepository.getDefaultBoardBySpaceId(spaceId)).id;
    }

    // 提取 transcript/overview 数据并准备用于 OtherWebpage.create 的 extra 字段
    let webpageExtra = extra;
    let snipBlocksData = null;

    // 如果 extra 是对象且包含 transcript 或 overview 数据，提取并处理
    if (
      extra &&
      typeof extra === 'object' &&
      ((extra as any).transcript_contents || (extra as any).overview_contents)
    ) {
      snipBlocksData = {
        transcript_contents: (extra as any).transcript_contents || [],
        overview_contents: (extra as any).overview_contents || [],
      };
      webpageExtra = undefined; // OtherWebpage 不需要存储这些数据到 extra 字段
    }

    // 创建 OtherWebpage 实体，与 youapp 保持一致
    const otherWebpage = await OtherWebpage.create(
      {
        spaceId,
        creatorId,
        title: undefined, // 将使用 webpage 的 title
        webpage,
        extra: webpageExtra,
        boardId,
        parentBoardGroupId,
      },
      boardId ? this.boardPositionService : undefined,
    );

    CreateOtherWebpageHandler.logger.debug(
      `before save OtherWebpage: ${JSON.stringify(otherWebpage)}`,
    );

    // 保存其他网页
    await this.snipRepository.save(otherWebpage);

    // 创建其他网页内容 block（如果有内容）
    await this.createOtherWebpageContentBlock(otherWebpage);

    // 如果有 transcript 或 overview 内容，调用 CreateSnipBlocksHandler
    if (
      snipBlocksData &&
      (snipBlocksData.transcript_contents.length > 0 || snipBlocksData.overview_contents.length > 0)
    ) {
      const createSnipBlocksCommand = new CreateSnipBlocksCommand(otherWebpage.id!, snipBlocksData);

      await this.commandBus.execute(createSnipBlocksCommand);
      CreateOtherWebpageHandler.logger.log(
        `Created snip blocks for other webpage: ${otherWebpage.id}`,
      );
    }

    // 绑定聊天
    if (chatId) {
      await this.chatDomainService.bindWebpageChatToSnip({
        snip_id: otherWebpage.id!,
        chat_id: chatId,
      });
    }

    CreateOtherWebpageHandler.logger.debug(
      `Other webpage snip created successfully: ${otherWebpage.id}`,
    );

    // 返回 DTO，与 youapp 的 snip2vo 功能保持一致
    const dto = this.snipDtoService.toDto(otherWebpage);

    return {
      ...dto,
      boardIds: boardId ? [boardId] : [], // 兼容旧数据，实际取 boardId 即可
    };
  }

  /**
   * 创建其他网页内容 block
   * 基于 youapp 的 createSnipBlocks 实现
   */
  private async createOtherWebpageContentBlock(otherWebpage: OtherWebpage): Promise<void> {
    // 其他网页可能包含简单的描述内容
    const description = otherWebpage.webpage.getDescription() || '';

    if (!description) {
      // 如果没有描述内容，不创建 block
      return;
    }

    // 使用 createContentHandler 创建内容处理器
    const contentHandler = createContentHandler({
      format: ContentFormatEnum.READER_HTML,
      raw: description,
      plain: description,
      language: DefaultLanguageKey,
    });

    // 创建 block
    const block = Block.create({
      snipId: otherWebpage.id,
      type: SnipFeatureEnum.RAW_SUMMARY, // 其他网页内容使用 RAW_SUMMARY 类型
      display: BlockDisplayEnum.SHOW,
    });

    // 保存 block
    await this.blockRepository.save(block);

    // 创建 content
    const content = BlockContent.create({
      blockId: block.id,
      language: contentHandler.toVO().language || DefaultLanguageKey,
      format: contentHandler.toVO().format,
      raw: contentHandler.toVO().raw,
      plain: contentHandler.toVO().plain,
      status: ProcessStatusEnum.DONE,
    });

    // 目前领域实体未直接 add content 方法，需要手动添加
    block.addContent(content);

    // 保存 content
    await this.contentRepository.save(content);

    // 设置当前内容
    block.setCurrentContent(content.id);
    await this.blockRepository.save(block);

    CreateOtherWebpageHandler.logger.debug(
      `Other webpage content block created for snip: ${otherWebpage.id}`,
    );
  }
}

/**
 * Create Office Handler - 创建Office文档处理器
 * 从 youapp 迁移的创建Office文档处理器
 *
 * Migrated from:
 * - /youapp/src/lib/app/snip/index.ts (createOffice)
 */

import { BadRequestException, Logger } from '@nestjs/common';
import { CommandBus, CommandHandler, EventBus, ICommandHandler } from '@nestjs/cqrs';
import { LanguageEnum } from '@repo/common';
import { runInBackground } from '@/common/errors';
import { QuotaResourceEnum } from '@/common/types/subscription.types';
import { getCallbackOrigin } from '@/common/utils/callback';
import { StorageUsageFromEnum } from '@/dao/usage-record/types';
import { ChatDomainService } from '@/domain/chat';
import { FileDomainService } from '@/domain/file';
import { SpaceDomainService } from '@/domain/space';
import { UsageRecordDomainService } from '@/domain/usage-record';
import { Youget } from '@/infra/youget';
import { Office } from '@/modules/material-mng/domain/snip/models/office.entity';
import { UploadFileMeta } from '@/modules/material-mng/domain/snip/models/type';
import { ReaderHTMLContent } from '@/modules/material-mng/domain/snip/value-objects/content.vo';
import { OfficeDto } from '@/modules/material-mng/dto/snip/office.dto';
import { BlockRepository } from '@/modules/material-mng/repositories/block.repository';
import { BlockContentRepository } from '@/modules/material-mng/repositories/block-content.repository';
import { BoardRepository } from '@/modules/material-mng/repositories/board.repository';
import { BoardPositionDomainService } from '../../../domain/shared/board-position.service';
import { SnipRepository } from '../../../repositories/snip.repository';
import { CreateOfficeCommand } from '../../commands/snip/create-office.command';
import { SnipDtoService } from '../../dto-services/snip-dto.service';

@CommandHandler(CreateOfficeCommand)
export class CreateOfficeHandler implements ICommandHandler<CreateOfficeCommand> {
  private static readonly logger = new Logger(CreateOfficeHandler.name);

  constructor(
    private readonly snipRepository: SnipRepository,
    private readonly boardPositionService: BoardPositionDomainService,
    private readonly snipDtoService: SnipDtoService,
    private readonly eventBus: EventBus,
    private readonly boardRepository: BoardRepository,
    private readonly youget: Youget,
    private readonly blockRepository: BlockRepository,
    private readonly contentRepository: BlockContentRepository,
    private readonly chatDomainService: ChatDomainService,
    private readonly commandBus: CommandBus,
    private readonly fileDomainService: FileDomainService,
    private readonly usageRecordDomainService: UsageRecordDomainService,
    private readonly spaceDomainService: SpaceDomainService,
  ) {}

  async execute(command: CreateOfficeCommand): Promise<OfficeDto> {
    const {
      spaceId,
      creatorId,
      file,
      title,
      syncTranscribe,
      boardId: originalBoardId,
      parentBoardGroupId,
      chatId,
    } = command;

    // 验证必填字段
    if (!file || !file.hash) {
      throw new BadRequestException('File information is required');
    }

    // 检查配额
    const space = await this.spaceDomainService.get(spaceId);
    if (space) {
      await this.usageRecordDomainService.checkQuota(space, QuotaResourceEnum.STORAGE);
    }

    // 确定 boardId：如果指定了则使用，否则获取默认 board
    let boardId: string;
    if (originalBoardId) {
      await this.boardRepository.getById(originalBoardId);
      boardId = originalBoardId;
    } else {
      boardId = (await this.boardRepository.getDefaultBoardBySpaceId(spaceId)).id;
    }

    // 获取文件元信息
    const fileMeta = await this.fileDomainService.getFileMeta(
      file.name,
      file.hash,
      file.isPublic ? 'public' : 'private',
    );

    // 记录存储使用量
    runInBackground(
      this.usageRecordDomainService.createStorageUsageRecord({
        space_id: spaceId,
        user_id: creatorId,
        from: StorageUsageFromEnum.SNIP,
        file: {
          name: fileMeta.name,
          mime_type: fileMeta.mime_type,
          size: fileMeta.size,
          storage_url: fileMeta.storage_url,
        },
      }),
    );

    // 创建 Office 实体
    const office = await Office.create(
      {
        spaceId,
        creatorId,
        title: title || fileMeta.name,
        file: <UploadFileMeta>{
          name: fileMeta.name,
          hash: file.hash,
          mimeType: fileMeta.mime_type,
          size: fileMeta.size,
          storageUrl: fileMeta.storage_url,
          isPublic: file.isPublic || false,
          directory: file.directory,
        },
        boardId,
        parentBoardGroupId,
      },
      boardId ? this.boardPositionService : undefined,
    );

    CreateOfficeHandler.logger.debug(`before save Office: ${JSON.stringify(office)}`);

    // 保存 Office 文档
    await this.snipRepository.save(office);

    // Office文档转录，由于 youget 接收的是 snake-case，这里直接转换一下
    const transcribeParams = {
      file: {
        name: office.file.name,
        size: office.file.size,
        directory: office.file.directory,
        hash: office.file.hash,
        is_public: office.file.isPublic,
        mime_type: office.file.mimeType,
        storage_url: office.file.storageUrl,
        url: office.file.storageUrl,
      },
      youget_callback_url: `${getCallbackOrigin()}/webhook/v1/office-parsed?snip_id=${office.id}`,
      youget_callback_method: 'PUT',
    };
    // 启动 Office 文档解析（Azure Document Intelligence）
    if (syncTranscribe) {
      // 同步模式：等待解析完成
      const result = await this.youget.executeTranscribeOfficeTaskInSync(
        transcribeParams,
        creatorId,
      );
      if (result) {
        office.content = new ReaderHTMLContent(
          result.content.raw,
          result.content.plain,
          result.content.language as LanguageEnum,
        );
        await office.save();
        await this.snipRepository.save(office);
      }
    } else {
      // 异步模式：后台处理
      runInBackground(
        this.youget.executeTranscribeOfficeTaskInBackground(transcribeParams, creatorId),
      );
    }

    // 绑定聊天
    if (chatId) {
      await this.chatDomainService.bindWebpageChatToSnip({
        snip_id: office.id!,
        chat_id: chatId,
      });
    }

    CreateOfficeHandler.logger.debug(`Office snip created successfully: ${office.id}`);

    // 返回 DTO，与 youapp 的 snip2vo 功能保持一致
    const dto = this.snipDtoService.toDto(office);

    return {
      ...dto,
      boardIds: boardId ? [boardId] : [], // 兼容旧数据，实际取 boardId 即可
    };
  }
}

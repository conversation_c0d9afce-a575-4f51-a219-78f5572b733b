import { ICommand } from '@nestjs/cqrs';
import { NoteSourceEntityType } from '../../../domain/note/types/note.types';

export class CreateNoteCommand implements ICommand {
  constructor(
    public readonly userId: string,
    public readonly spaceId: string,
    public readonly boardId: string,
    public readonly source?: {
      entityType: NoteSourceEntityType;
      entityId: string;
      selection?: {
        matchText?: string;
        matchIndex?: number;
        hashID?: string;
        color?: string;
      };
      quote?: {
        raw: string;
        plain?: string;
      };
    },
    public readonly content?: {
      raw: string;
      plain?: string;
    },
  ) {}
}

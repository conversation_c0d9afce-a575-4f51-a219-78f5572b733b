import { ICommand } from '@nestjs/cqrs';
import { AudioGenerationOptions } from '../../../domain/playlist-item/types/playlist-item.types';

type PlaylistItemEntityType = 'snip' | 'thought';

export class CreatePlaylistItemCommand implements ICommand {
  constructor(
    public readonly userId: string,
    public readonly spaceId: string,
    public readonly entityType: PlaylistItemEntityType,
    public readonly entityId: string,
    public readonly boardId?: string,
    public readonly audioOptions?: AudioGenerationOptions,
  ) {}
}

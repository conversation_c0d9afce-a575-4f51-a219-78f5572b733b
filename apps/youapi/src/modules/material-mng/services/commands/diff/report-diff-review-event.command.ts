import { ICommand } from '@nestjs/cqrs';
import { DiffReviewActionType } from '../../../domain/thought-version/value-objects/diff-review-action.vo';

export interface DiffNodeData {
  id: string;
  old?: string;
  new?: string;
}

export interface ResolveVersionData {
  thoughtTitle: string;
  content: {
    raw: string;
    plain?: string;
  };
}

export class ReportDiffReviewEventCommand implements ICommand {
  constructor(
    public readonly thoughtId: string,
    public readonly action: DiffReviewActionType,
    public readonly nodes: DiffNodeData[],
    public readonly resolveVersion?: ResolveVersionData,
  ) {}
}

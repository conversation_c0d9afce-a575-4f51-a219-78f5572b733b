/**
 * Block DTO Service - 区块 DTO 转换服务
 * 负责将 Block 和 BlockContent 领域实体转换为 DTO 对象
 *
 * 参考 snip-dto.service.ts 的实现模式
 */

import { Injectable } from '@nestjs/common';
import { Block } from '../../domain/block/models/block.entity';
import { BlockContent } from '../../domain/block/models/block-content.entity';
import { BlockContentDto, BlockDto } from '../../dto/snip/snip-with-blocks.dto';

@Injectable()
export class BlockDtoService {
  /**
   * 转换 Block 实体为 BlockDto
   */
  toBlockDto(block: Block): BlockDto {
    return {
      currentContentId: block.currentContentId,
      contents: this.toContentDtoList(block.contents),
      type: block.type,
    };
  }

  /**
   * 批量转换 Block 实体为 BlockDto
   */
  toBlockDtoList(blocks: Block[]): BlockDto[] {
    return blocks.map((block) => this.toBlockDto(block));
  }

  /**
   * 转换 BlockContent 实体为 BlockContentDto
   */
  toContentDto(content: BlockContent): BlockContentDto {
    return {
      id: content.id,
      format: content.format,
      raw: content.raw,
      plain: content.plain,
      language: content.language,
      status: content.status,
      createdAt: content.createdAt,
      updatedAt: content.updatedAt,
    };
  }

  /**
   * 批量转换 BlockContent 实体为 BlockContentDto
   */
  toContentDtoList(contents: BlockContent[]): BlockContentDto[] {
    return contents.map((content) => this.toContentDto(content));
  }

  /**
   * 根据 Block 类型查找并转换特定类型的 Block
   * 返回第一个匹配的 Block，如果没有找到返回 null
   */
  findAndConvertBlockByType(blocks: Block[], type: string): BlockDto | null {
    const block = blocks.find((b) => b.type === type);
    return block ? this.toBlockDto(block) : null;
  }

  /**
   * 根据 Block 类型批量查找并转换特定类型的 Blocks
   */
  findAndConvertBlocksByType(blocks: Block[], type: string): BlockDto[] {
    const filteredBlocks = blocks.filter((b) => b.type === type);
    return this.toBlockDtoList(filteredBlocks);
  }

  /**
   * 转换 Block 数组为包含 overview 和 transcript 的对象
   * 用于 GetSnip API 响应
   */
  toSnipBlocks(blocks: Block[]): { overview?: BlockDto | null; transcript?: BlockDto | null } {
    // 使用 SnipFeatureEnum 的值直接比较
    const overview = blocks.find((b) => b.type === 'overview');
    const transcript = blocks.find((b) => b.type === 'transcript');

    return {
      overview: overview ? this.toBlockDto(overview) : null,
      transcript: transcript ? this.toBlockDto(transcript) : null,
    };
  }

  /**
   * 检查 Block 是否有内容
   */
  hasContents(block: Block): boolean {
    return block.contents && block.contents.length > 0;
  }

  /**
   * 获取 Block 的当前内容
   * 如果有 currentContentId，返回对应的内容；否则返回最新的内容
   */
  getCurrentContent(block: Block): BlockContent | undefined {
    if (!this.hasContents(block)) {
      return undefined;
    }

    if (block.currentContentId) {
      return block.contents.find((c) => c.id === block.currentContentId);
    }

    // 返回最新的内容（假设按创建时间排序）
    return block.contents[block.contents.length - 1];
  }

  /**
   * 转换当前内容为 DTO
   */
  getCurrentContentDto(block: Block): BlockContentDto | undefined {
    const currentContent = this.getCurrentContent(block);
    return currentContent ? this.toContentDto(currentContent) : undefined;
  }
}

/**
 * PlaylistItem 领域类型定义
 * 播放列表项相关的类型和枚举
 */

// 播放列表项支持的来源实体类型
export const PlaylistItemSourceEntityType = {
  SNIP: 'snip',
  THOUGHT: 'thought',
} as const;

export type PlaylistItemSourceEntityType =
  (typeof PlaylistItemSourceEntityType)[keyof typeof PlaylistItemSourceEntityType];

// 播放列表项状态枚举
export const PlaylistItemStatus = {
  GENERATING: 'generating',
  SUCCESS: 'success',
  FAILED: 'failed',
} as const;

export type PlaylistItemStatus = (typeof PlaylistItemStatus)[keyof typeof PlaylistItemStatus];

// 音频信息值对象
export interface AudioInfo {
  playUrl: string;
  duration: number;
  albumCoverUrl?: string;
  transcript?: string;
}

// 实体引用值对象
export interface EntityReference {
  entityType: PlaylistItemSourceEntityType;
  entityId: string;
}

// 创建 PlaylistItem 参数
export interface CreatePlaylistItemParams {
  creatorId: string;
  spaceId: string;
  entityType: PlaylistItemSourceEntityType;
  entityId: string;
  boardId?: string;
}

// 音频生成选项
export interface AudioGenerationOptions {
  model?: string;
  voice?: string;
  emotion?: string;
}

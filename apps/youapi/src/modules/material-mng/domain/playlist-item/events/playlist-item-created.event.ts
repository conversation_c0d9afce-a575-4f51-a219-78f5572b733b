import { IEvent } from '@nestjs/cqrs';

type PlaylistItemEntityType = 'snip' | 'thought';

export class PlaylistItemCreatedEvent implements IEvent {
  constructor(
    public readonly playlistItemId: string,
    public readonly creatorId: string,
    public readonly spaceId: string,
    public readonly entityType: PlaylistItemEntityType,
    public readonly entityId: string,
    public readonly boardId?: string,
  ) {}
}

import { AggregateRoot } from '@nestjs/cqrs';
import { uuidv7 } from 'uuidv7';
import { NoteCreatedEvent } from '../events/note-created.event';
import { NoteDeletedEvent } from '../events/note-deleted.event';
import { NoteUpdatedEvent } from '../events/note-updated.event';
import { NoteSourceEntityType } from '../types/note.types';

export interface HTMLSelection {
  matchText?: string;
  matchIndex?: number;
  hashID?: string;
  color?: string;
}

export interface NoteQuote {
  raw: string;
  plain: string;
}

export interface NoteContent {
  raw: string;
  plain: string;
}

export interface NoteSource {
  entityType: NoteSourceEntityType;
  entityId: string;
  selection?: HTMLSelection;
  quote?: NoteQuote;
}

export class Note extends AggregateRoot {
  public isNew: boolean = false;

  constructor(
    public readonly id: string,
    public readonly createdAt: Date,
    public updatedAt: Date,
    public readonly creatorId: string,
    public readonly spaceId: string,
    public readonly boardId: string,
    public source?: NoteSource,
    public content?: NoteContent,
  ) {
    super();
  }

  /**
   * 创建新笔记
   */
  static create(
    creatorId: string,
    spaceId: string,
    boardId: string,
    source?: NoteSource,
    content?: NoteContent,
  ): Note {
    const now = new Date();
    const note = new Note(uuidv7(), now, now, creatorId, spaceId, boardId, source, content);

    note.isNew = true;
    note.apply(new NoteCreatedEvent(note.id, note.creatorId, note.boardId));
    return note;
  }

  // Properties are now public and accessible directly

  /**
   * 更新笔记内容
   */
  updateContent(content: NoteContent): void {
    this.content = content;
    this.updatedAt = new Date();
    this.apply(new NoteUpdatedEvent(this.id, 'content'));
  }

  /**
   * 更新笔记来源
   */
  updateSource(source: Partial<NoteSource>): void {
    if (this.source) {
      this.source = { ...this.source, ...source };
    } else {
      // 如果原来没有来源，需要提供完整的来源信息
      if (source.entityType && source.entityId) {
        this.source = {
          entityType: source.entityType,
          entityId: source.entityId,
          selection: source.selection,
          quote: source.quote,
        };
      }
    }
    this.updatedAt = new Date();
    this.apply(new NoteUpdatedEvent(this.id, 'source'));
  }

  /**
   * 删除笔记（触发删除事件）
   */
  delete(): void {
    this.apply(new NoteDeletedEvent(this.id, this.creatorId));
  }

  /**
   * 检查是否为新实体 (property access directly)
   */

  /**
   * 标记为已存在的实体（仅供 Repository 层使用）
   */
  markAsExisting(): void {
    this.isNew = false;
  }
}

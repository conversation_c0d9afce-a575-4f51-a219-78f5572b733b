/**
 * 差异审查操作值对象
 * 定义对差异内容的操作类型
 */
export class DiffReviewAction {
  public static readonly ACCEPT = 'accept' as const;
  public static readonly REJECT = 'reject' as const;

  private constructor(private readonly value: string) {}

  public static create(value: string): DiffReviewAction {
    if (!DiffReviewAction.isValid(value)) {
      throw new Error(`Invalid diff review action: ${value}`);
    }
    return new DiffReviewAction(value);
  }

  public static accept(): DiffReviewAction {
    return new DiffReviewAction(DiffReviewAction.ACCEPT);
  }

  public static reject(): DiffReviewAction {
    return new DiffReviewAction(DiffReviewAction.REJECT);
  }

  public static isValid(value: string): boolean {
    return [DiffReviewAction.ACCEPT, DiffReviewAction.REJECT].includes(value as any);
  }

  public getValue(): string {
    return this.value;
  }

  public isAccept(): boolean {
    return this.value === DiffReviewAction.ACCEPT;
  }

  public isReject(): boolean {
    return this.value === DiffReviewAction.REJECT;
  }

  public equals(other: DiffReviewAction): boolean {
    return this.value === other.value;
  }

  public toString(): string {
    return this.value;
  }
}

// 为API文档和验证器使用的枚举对象
export const DiffReviewActionType = {
  ACCEPT: DiffReviewAction.ACCEPT,
  REJECT: DiffReviewAction.REJECT,
} as const;

export type DiffReviewActionType = (typeof DiffReviewActionType)[keyof typeof DiffReviewActionType];

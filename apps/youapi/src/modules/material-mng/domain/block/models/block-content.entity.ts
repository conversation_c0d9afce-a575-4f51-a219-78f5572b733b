/**
 * BlockContent Entity - 区块内容实体
 * 区块中的具体内容，支持多语言和版本管理
 *
 * 后续可考虑去掉 content 这一领域实体层，直接在 block 里封装好 content 相关逻辑，本质上 content 是 block 聚合根下的一个值对象（但是目前需要 id，目前无法去掉）
 *
 * Migrated from:
 * - /youapp/src/lib/domain/content/types.ts
 */

import { AggregateRoot } from '@nestjs/cqrs';
import { type ContentFormatEnum, type LanguageEnum, ProcessStatusEnum } from '@repo/common';
import { uuidv7 } from 'uuidv7';
import { BlockContentCreatedEvent } from '../events/block-content-created.event';
import { BlockContentDeletedEvent } from '../events/block-content-deleted.event';
import { BlockContentUpdatedEvent } from '../events/block-content-updated.event';

export interface CreateContentParams {
  id?: string;
  blockId: string;
  snipId?: string;
  language: LanguageEnum;
  format: ContentFormatEnum;
  raw: string;
  plain?: string;
  status?: ProcessStatusEnum;
  traceId?: string;
}

export interface UpdateContentParams {
  raw?: string;
  plain?: string;
  status?: ProcessStatusEnum;
}

export class BlockContent extends AggregateRoot {
  public isNew: boolean = false;

  constructor(
    public readonly id: string,
    public createdAt: Date,
    public updatedAt: Date,
    public readonly blockId: string,
    public readonly snipId: string | undefined,
    public readonly language: LanguageEnum,
    public readonly format: ContentFormatEnum,
    public raw: string,
    public plain?: string,
    public status: ProcessStatusEnum = ProcessStatusEnum.DONE,
    public readonly traceId?: string,
  ) {
    super();
  }

  static create(params: CreateContentParams): BlockContent {
    const id = params.id || uuidv7();
    const now = new Date();

    const content = new BlockContent(
      id,
      now,
      now,
      params.blockId,
      params.snipId,
      params.language,
      params.format,
      params.raw,
      params.plain,
      params.status || ProcessStatusEnum.DONE,
      params.traceId,
    );

    content.isNew = true;

    // 发布创建事件
    content.apply(
      new BlockContentCreatedEvent({
        contentId: id,
        blockId: params.blockId,
        language: params.language,
        format: params.format,
        status: content.status,
      }),
    );

    return content;
  }

  /**
   * 更新内容
   */
  update(params: UpdateContentParams): void {
    const hasChanges =
      (params.raw && params.raw !== this.raw) ||
      (params.plain !== undefined && params.plain !== this.plain) ||
      (params.status && params.status !== this.status);

    if (!hasChanges) return;

    // 更新字段
    if (params.raw) this.raw = params.raw;
    if (params.plain !== undefined) this.plain = params.plain;
    if (params.status) this.status = params.status;

    this.updatedAt = new Date();

    // 发布更新事件
    this.apply(
      new BlockContentUpdatedEvent({
        contentId: this.id,
        blockId: this.blockId,
        changes: params,
      }),
    );
  }

  /**
   * 设置处理状态
   */
  setStatus(status: ProcessStatusEnum): void {
    this.update({ status });
  }

  /**
   * 标记为处理中
   */
  markAsProcessing(): void {
    this.setStatus(ProcessStatusEnum.ING);
  }

  /**
   * 标记为完成
   */
  markAsDone(): void {
    this.setStatus(ProcessStatusEnum.DONE);
  }

  /**
   * 标记为失败
   */
  markAsFailed(): void {
    this.setStatus(ProcessStatusEnum.FAIL);
  }

  /**
   * 检查是否处理完成
   */
  isDone(): boolean {
    return this.status === ProcessStatusEnum.DONE;
  }

  /**
   * 检查是否正在处理
   */
  isProcessing(): boolean {
    return this.status === ProcessStatusEnum.ING;
  }

  /**
   * 检查是否处理失败
   */
  isFailed(): boolean {
    return this.status === ProcessStatusEnum.FAIL;
  }

  /**
   * 软删除内容
   */
  delete(): void {
    // 发布删除事件
    this.apply(
      new BlockContentDeletedEvent({
        contentId: this.id,
        blockId: this.blockId,
      }),
    );
  }

  /**
   * 标记为已存在（非新建）
   */
  markAsExisting(): void {
    this.isNew = false;
  }

  /**
   * 获取内容文本（优先返回 plain，否则返回 raw）
   */
  getText(): string {
    return this.plain || this.raw;
  }

  /**
   * 获取内容长度
   */
  getLength(): number {
    return this.getText().length;
  }

  /**
   * 检查是否有纯文本内容
   */
  hasPlainText(): boolean {
    return Boolean(this.plain);
  }

  /**
   * 更新纯文本内容（通常由处理管道自动生成）
   */
  updatePlainText(plain: string): void {
    this.update({ plain });
  }
}

/**
 * Block Entity - 内容区块领域实体
 * 用于处理内容的不同展现形式和处理状态
 *
 * Migrated from:
 * - /youapp/src/lib/domain/block/index.ts
 * - /youapp/src/lib/domain/block/types.ts
 */

import { AggregateRoot } from '@nestjs/cqrs';
import { ProcessStatusEnum } from '@repo/common';
import { uuidv7 } from 'uuidv7';
import { BlockDisplayEnum, SnipFeatureEnum } from '../../../../../common/types/snip.types';
import { BlockCreatedEvent } from '../events/block-created.event';
import { BlockDeletedEvent } from '../events/block-deleted.event';
import { BlockUpdatedEvent } from '../events/block-updated.event';
import { BlockContent } from './block-content.entity';

export interface CreateBlockParams {
  id?: string;
  snipId: string;
  type: SnipFeatureEnum;
  display?: BlockDisplayEnum;
  currentContentId?: string;
  actionId?: string;
}

export interface UpdateBlockParams {
  type?: SnipFeatureEnum;
  display?: BlockDisplayEnum;
  currentContentId?: string;
  actionId?: string;
}

export class Block extends AggregateRoot {
  public isNew: boolean = false;
  public contents: BlockContent[] = [];

  constructor(
    public readonly id: string,
    public createdAt: Date,
    public updatedAt: Date,
    public readonly snipId: string,
    public type: SnipFeatureEnum,
    public display: BlockDisplayEnum,
    public currentContentId?: string,
    public actionId?: string,
  ) {
    super();
  }

  static create(params: CreateBlockParams): Block {
    const id = params.id || uuidv7();
    const now = new Date();

    const block = new Block(
      id,
      now,
      now,
      params.snipId,
      params.type,
      params.display || BlockDisplayEnum.SHOW,
      params.currentContentId,
      params.actionId,
    );

    block.isNew = true;

    // 发布领域事件
    block.apply(
      new BlockCreatedEvent({
        blockId: id,
        snipId: params.snipId,
        type: params.type,
        display: params.display || BlockDisplayEnum.SHOW,
        actionId: params.actionId,
      }),
    );

    return block;
  }

  /**
   * 更新区块信息
   */
  update(params: UpdateBlockParams): void {
    const hasChanges =
      (params.type && params.type !== this.type) ||
      (params.display && params.display !== this.display) ||
      (params.currentContentId && params.currentContentId !== this.currentContentId) ||
      (params.actionId && params.actionId !== this.actionId);

    if (!hasChanges) return;

    // 更新字段
    if (params.type) this.type = params.type;
    if (params.display) this.display = params.display;
    if (params.currentContentId !== undefined) this.currentContentId = params.currentContentId;
    if (params.actionId !== undefined) this.actionId = params.actionId;

    this.updatedAt = new Date();

    // 发布更新事件
    this.apply(
      new BlockUpdatedEvent({
        blockId: this.id,
        snipId: this.snipId,
        changes: params,
      }),
    );
  }

  /**
   * 设置当前内容
   */
  setCurrentContent(contentId: string): void {
    // 验证内容是否属于该区块
    const content = this.contents.find((c) => c.id === contentId);
    if (!content) {
      throw new Error(`Content ${contentId} not found in block ${this.id}`);
    }

    this.update({ currentContentId: contentId });
  }

  /**
   * 添加内容
   */
  addContent(content: BlockContent): void {
    // 确保内容属于该区块
    if (content.blockId !== this.id) {
      throw new Error(`Content ${content.id} does not belong to block ${this.id}`);
    }

    this.contents.push(content);

    // 如果是第一个内容或没有设置当前内容，设置为当前内容
    if (!this.currentContentId || this.contents.length === 1) {
      this.currentContentId = content.id;
      this.updatedAt = new Date();
    }
  }

  /**
   * 移除内容
   */
  removeContent(contentId: string): void {
    const index = this.contents.findIndex((c) => c.id === contentId);
    if (index === -1) return;

    this.contents.splice(index, 1);

    // 如果移除的是当前内容，需要重新设置
    if (this.currentContentId === contentId) {
      this.currentContentId = this.contents.length > 0 ? this.contents[0].id : undefined;
      this.updatedAt = new Date();
    }
  }

  /**
   * 获取当前内容
   */
  getCurrentContent(): BlockContent | undefined {
    if (!this.currentContentId) {
      // 兼容性处理：如果没有设置当前内容但有内容，返回第一个
      return this.contents.length > 0 ? this.contents[0] : undefined;
    }
    return this.contents.find((c) => c.id === this.currentContentId);
  }

  /**
   * 计算区块状态
   * 基于所有内容的状态计算得出
   */
  getStatus(): ProcessStatusEnum {
    if (this.contents.length === 0) {
      return ProcessStatusEnum.DONE;
    }

    // 如果有任何内容正在处理中，整个区块就是处理中
    if (this.contents.some((c) => c.status === ProcessStatusEnum.ING)) {
      return ProcessStatusEnum.ING;
    }

    // 如果有内容失败，整个区块失败
    if (this.contents.some((c) => c.status === ProcessStatusEnum.FAIL)) {
      return ProcessStatusEnum.FAIL;
    }

    return ProcessStatusEnum.DONE;
  }

  /**
   * 检查是否显示
   */
  isVisible(): boolean {
    return this.display === BlockDisplayEnum.SHOW;
  }

  /**
   * 设置显示状态
   */
  setDisplay(display: BlockDisplayEnum): void {
    this.update({ display });
  }

  /**
   * 隐藏区块
   */
  hide(): void {
    this.setDisplay(BlockDisplayEnum.HIDE);
  }

  /**
   * 显示区块
   */
  show(): void {
    this.setDisplay(BlockDisplayEnum.SHOW);
  }

  /**
   * 软删除区块
   */
  delete(): void {
    // 发布删除事件
    this.apply(
      new BlockDeletedEvent({
        blockId: this.id,
        snipId: this.snipId,
      }),
    );
  }

  /**
   * 标记为已存在（非新建）
   */
  markAsExisting(): void {
    this.isNew = false;
  }

  /**
   * 获取区块的显示标题（用于前端展示）
   */
  getDisplayTitle(): string {
    switch (this.type) {
      case SnipFeatureEnum.OVERVIEW:
        return '概览';
      case SnipFeatureEnum.EXPLAIN:
        return '解释';
      case SnipFeatureEnum.TRANSCRIPT:
        return '转录';
      case SnipFeatureEnum.RAW_SUMMARY:
        return '摘要';
      default:
        return '未知类型';
    }
  }
}

import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsDefined, IsOptional, IsString } from 'class-validator';
import { NoteSourceEntityType } from '../domain/note/types/note.types';

export class HTMLSelectionDto {
  @ApiPropertyOptional({
    description: '选中的文本内容',
    example: '重要的信息',
  })
  @IsOptional()
  @IsString()
  matchText?: string;

  @ApiPropertyOptional({
    description: '匹配的索引位置',
    example: 0,
  })
  @IsOptional()
  matchIndex?: number;

  @ApiPropertyOptional({
    description: '选区的哈希ID',
    example: 'hash-12345',
  })
  @IsOptional()
  @IsString()
  hashID?: string;

  @ApiPropertyOptional({
    description: '高亮颜色',
    example: '#ffff00',
  })
  @IsOptional()
  @IsString()
  color?: string;
}

export class NoteQuoteDto {
  @ApiProperty({
    description: '引用的原始内容',
    example: '这是引用的原始内容',
  })
  @IsString()
  @IsDefined()
  raw: string;

  @ApiProperty({
    description: '引用的纯文本内容',
    example: '这是引用的纯文本内容',
  })
  @IsString()
  @IsDefined()
  plain: string;
}

export class NoteContentDto {
  @ApiProperty({
    description: '笔记的原始内容',
    example: '这是我的笔记内容',
  })
  @IsString()
  @IsDefined()
  raw: string;

  @ApiProperty({
    description: '笔记的纯文本内容',
    example: '这是我的笔记内容',
  })
  @IsString()
  @IsDefined()
  plain: string;
}

export class NoteSourceEntityDto {
  @ApiProperty({
    description: '实体ID',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  id: string;

  @ApiProperty({
    description: '实体标题',
    example: 'Snip标题或Thought标题',
  })
  title: string;
}

export class NoteSourceDto {
  @ApiProperty({
    description: '来源实体类型',
    enum: NoteSourceEntityType,
    enumName: 'NoteSourceEntityType',
    example: 'snip',
  })
  entityType: NoteSourceEntityType;

  @ApiProperty({
    description: '来源实体ID（兼容字段）',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  entityId: string;

  @ApiProperty({
    description: '来源实体信息',
    type: NoteSourceEntityDto,
  })
  entity: NoteSourceEntityDto;

  @ApiPropertyOptional({
    description: 'HTML选区信息',
    type: HTMLSelectionDto,
  })
  selection?: HTMLSelectionDto;

  @ApiPropertyOptional({
    description: '引用内容',
    type: NoteQuoteDto,
  })
  quote?: NoteQuoteDto;
}

export class NoteDto {
  @ApiProperty({
    description: '笔记ID',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  id: string;

  @ApiProperty({
    description: '创建时间',
    example: '2023-10-01T10:00:00.000Z',
  })
  createdAt: Date;

  @ApiProperty({
    description: '更新时间',
    example: '2023-10-01T10:00:00.000Z',
  })
  updatedAt: Date;

  @ApiProperty({
    description: '所属看板ID',
    example: '550e8400-e29b-41d4-a716-446655440003',
  })
  boardId: string;

  @ApiPropertyOptional({
    description: '笔记来源信息',
    type: NoteSourceDto,
  })
  source?: NoteSourceDto;

  @ApiPropertyOptional({
    description: '笔记内容',
    type: NoteContentDto,
  })
  content?: NoteContentDto;
}

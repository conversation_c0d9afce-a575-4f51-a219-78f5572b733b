import { ApiProperty } from '@nestjs/swagger';
import { EntityType } from '@/shared/db/public.schema';

/**
 * 短链接DTO
 * 用于API响应中的短链接信息
 */
export class ShortLinkDto {
  @ApiProperty({
    description: '短链接ID',
    example: '01234567-89ab-cdef-0123-456789abcdef',
  })
  id: string;

  @ApiProperty({
    description: '短链接标识符',
    example: 'abc123xyz78901',
    maxLength: 14,
  })
  shortId: string;

  @ApiProperty({
    description: '短链接是否激活',
    example: true,
  })
  active: boolean;

  @ApiProperty({
    description: '创建时间',
    example: '2023-01-01T00:00:00.000Z',
  })
  createdAt: Date;

  @ApiProperty({
    description: '更新时间',
    example: '2023-01-01T00:00:00.000Z',
  })
  updatedAt: Date;

  @ApiProperty({
    description: '实体类型',
    enum: EntityType,
    enumName: 'EntityType',
    example: 'snip',
  })
  entityType: EntityType;

  @ApiProperty({
    description: '实体ID',
    example: '01234567-89ab-cdef-0123-456789abcdef',
  })
  entityId: string;
}

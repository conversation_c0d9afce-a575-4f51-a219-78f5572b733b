import { ApiProperty } from '@nestjs/swagger';
import { IsIn, IsOptional, IsString, IsUUID } from 'class-validator';
import {
  PlaylistItemSourceEntityType,
  PlaylistItemStatus,
} from '../domain/playlist-item/types/playlist-item.types';

export class PlaylistItemDto {
  @ApiProperty({ description: '播放列表项ID' })
  id: string;

  @ApiProperty({ description: '创建者ID' })
  creatorId: string;

  @ApiProperty({ description: '空间ID' })
  spaceId: string;

  @ApiProperty({ description: '创作板ID', required: false })
  boardId?: string;

  @ApiProperty({
    description: '实体类型',
    enum: PlaylistItemSourceEntityType,
    enumName: 'PlaylistItemSourceEntityType',
  })
  entityType: PlaylistItemSourceEntityType;

  @ApiProperty({ description: '实体ID' })
  entityId: string;

  @ApiProperty({ description: '标题' })
  title: string;

  @ApiProperty({ description: '播放地址' })
  playUrl: string;

  @ApiProperty({ description: '音频时长（秒）' })
  duration: number;

  @ApiProperty({
    description: '状态',
    enum: PlaylistItemStatus,
    enumName: 'PlaylistItemStatus',
  })
  status: PlaylistItemStatus;

  @ApiProperty({ description: '专辑封面URL', required: false })
  albumCoverUrl?: string;

  @ApiProperty({ description: '转录文本', required: false })
  transcript?: string;

  @ApiProperty({ description: '排序', required: false })
  rank?: string;

  @ApiProperty({ description: '播放进度', required: false })
  playbackProgress?: number;

  @ApiProperty({ description: '创建时间' })
  createdAt: Date;

  @ApiProperty({ description: '更新时间' })
  updatedAt: Date;
}

// 请求 DTO
export class CreatePlaylistItemDto {
  @ApiProperty({
    description: '实体类型',
    enum: PlaylistItemSourceEntityType,
    enumName: 'PlaylistItemSourceEntityType',
  })
  @IsIn([PlaylistItemSourceEntityType.SNIP, PlaylistItemSourceEntityType.THOUGHT])
  entityType: PlaylistItemSourceEntityType;

  @ApiProperty({ description: '实体ID' })
  @IsUUID()
  entityId: string;

  @ApiProperty({ description: '创作板ID', required: false })
  @IsOptional()
  @IsUUID()
  boardId?: string;

  @ApiProperty({ description: 'AI模型', required: false })
  @IsOptional()
  @IsString()
  model?: string;

  @ApiProperty({ description: '声音类型', required: false })
  @IsOptional()
  @IsString()
  voice?: string;

  @ApiProperty({ description: '情感类型', required: false })
  @IsOptional()
  @IsString()
  emotion?: string;
}

export class ListPlaylistItemsDto {
  @ApiProperty({ description: '创作板ID' })
  @IsUUID()
  boardId: string;
}

export class UpdatePlaylistItemTitleDto {
  @ApiProperty({ description: '播放列表项ID' })
  @IsUUID()
  id: string;

  @ApiProperty({ description: '新标题' })
  @IsString()
  title: string;
}

export class DeletePlaylistItemDto {
  @ApiProperty({ description: '播放列表项ID' })
  @IsUUID()
  id: string;
}

import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  <PERSON>A<PERSON>y,
  IsIn,
  IsOptional,
  IsString,
  IsUUID,
  MaxLength,
  ValidateNested,
} from 'class-validator';
import { DiffReviewActionType } from '../../domain/thought-version/value-objects/diff-review-action.vo';
import { ThoughtVersionContentDto } from './create-thought-version.dto';

export class DiffNodeDto {
  @ApiProperty({
    description: '节点ID',
    example: 'node-1',
  })
  @IsString()
  id: string;

  @ApiProperty({
    description: '旧内容',
    example: 'old content',
    required: false,
  })
  @IsOptional()
  @IsString()
  old?: string;

  @ApiProperty({
    description: '新内容',
    example: 'new content',
    required: false,
  })
  @IsOptional()
  @IsString()
  new?: string;
}

export class ResolveVersionDto {
  @ApiProperty({
    description: '解决后的想法标题',
    example: 'Resolved Thought Title',
    maxLength: 255,
  })
  @IsString()
  @MaxLength(255)
  thoughtTitle: string;

  @ApiProperty({
    description: '解决后的内容',
    type: ThoughtVersionContentDto,
  })
  @ValidateNested()
  @Type(() => ThoughtVersionContentDto)
  content: ThoughtVersionContentDto;
}

export class ReportDiffReviewEventDto {
  @ApiProperty({
    description: '想法ID',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  thoughtId: string;

  @ApiProperty({
    description: '操作类型',
    enum: DiffReviewActionType,
    enumName: 'DiffReviewActionType',
    example: 'accept',
  })
  @IsIn([DiffReviewActionType.ACCEPT, DiffReviewActionType.REJECT])
  action: DiffReviewActionType;

  @ApiProperty({
    description: '差异节点列表',
    type: [DiffNodeDto],
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => DiffNodeDto)
  nodes: DiffNodeDto[];

  @ApiProperty({
    description: '解决版本数据（可选）',
    type: ResolveVersionDto,
    required: false,
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => ResolveVersionDto)
  resolveVersion?: ResolveVersionDto;
}

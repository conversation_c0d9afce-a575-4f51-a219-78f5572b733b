import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsEnum, IsOptional, IsString, MaxLength, ValidateNested } from 'class-validator';
import { TitleType } from '../domain/thought/models/thought.entity';
import { ThoughtContentDto } from './create-thought.dto';

export class UpdateThoughtDto {
  @ApiProperty({
    description: '标题',
    example: 'Updated thought title',
    required: false,
    maxLength: 255,
  })
  @IsString()
  @IsOptional()
  @MaxLength(255)
  title?: string;

  @ApiProperty({
    description: '标题类型',
    enum: TitleType,
    enumName: 'TitleType',
    example: 'manual',
    required: false,
  })
  @IsEnum(TitleType)
  @IsOptional()
  titleType?: TitleType;

  @ApiProperty({
    description: '内容',
    type: ThoughtContentDto,
    required: false,
  })
  @ValidateNested()
  @Type(() => ThoughtContentDto)
  @IsOptional()
  content?: ThoughtContentDto;
}

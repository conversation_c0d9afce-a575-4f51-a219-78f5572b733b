import { ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsIn, IsOptional, IsUUID, ValidateNested } from 'class-validator';
import { NoteSourceEntityType } from '../domain/note/types/note.types';

export class ListNotesSourceDto {
  @ApiPropertyOptional({
    description: '来源实体类型',
    enum: NoteSourceEntityType,
    enumName: 'NoteSourceEntityType',
    example: 'snip',
  })
  @IsOptional()
  @IsIn([NoteSourceEntityType.SNIP, NoteSourceEntityType.THOUGHT])
  entityType?: NoteSourceEntityType;

  @ApiPropertyOptional({
    description: '来源实体ID',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @IsOptional()
  @IsUUID()
  entityId?: string;
}

export class ListNotesDto {
  @ApiPropertyOptional({
    description: '看板ID，用于筛选特定看板的笔记',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @IsOptional()
  @IsUUID()
  boardId?: string;

  @ApiPropertyOptional({
    description: '笔记来源筛选条件',
    type: ListNotesSourceDto,
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => ListNotesSourceDto)
  source?: ListNotesSourceDto;
}

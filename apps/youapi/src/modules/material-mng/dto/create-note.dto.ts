import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsDefined, IsIn, IsOptional, IsUUID, ValidateNested } from 'class-validator';
import { NoteSourceEntityType } from '../domain/note/types/note.types';
import { HTMLSelectionDto, NoteContentDto, NoteQuoteDto } from './note.dto';

export class CreateNoteSourceDto {
  @ApiProperty({
    description: '来源实体类型',
    enum: NoteSourceEntityType,
    enumName: 'NoteSourceEntityType',
    example: 'snip',
  })
  @IsIn([NoteSourceEntityType.SNIP, NoteSourceEntityType.THOUGHT])
  @IsDefined()
  entityType: NoteSourceEntityType;

  @ApiProperty({
    description: '来源实体 ID',
  })
  @IsUUID()
  @IsDefined()
  entityId: string;

  @ApiPropertyOptional({
    description: 'HTML选区信息',
    type: HTMLSelectionDto,
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => HTMLSelectionDto)
  selection?: HTMLSelectionDto;

  @ApiPropertyOptional({
    description: '引用内容',
    type: NoteQuoteDto,
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => NoteQuoteDto)
  quote?: NoteQuoteDto;
}
export class CreateNoteDto {
  @ApiProperty({
    description: '所属看板ID',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @IsUUID()
  @IsDefined()
  boardId: string;

  @ApiPropertyOptional({
    description: '笔记来源信息',
    type: CreateNoteSourceDto,
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => CreateNoteSourceDto)
  source?: CreateNoteSourceDto;

  @ApiProperty({
    description: '笔记内容',
    type: NoteContentDto,
  })
  @ValidateNested()
  @IsDefined()
  @Type(() => NoteContentDto)
  content: NoteContentDto;
}

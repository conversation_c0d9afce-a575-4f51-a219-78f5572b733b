import { ApiProperty } from '@nestjs/swagger';
import { Visibility } from '@/shared/db/public.schema';
import type { BoardItemInfo, BoardPositionInfo } from '../domain/shared/board-position.service';
import { TitleType } from '../domain/thought/models/thought.entity';

export class ThoughtDto {
  @ApiProperty({
    description: '想法ID',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  id: string;

  @ApiProperty({
    description: '创建时间',
    example: '2023-01-01T00:00:00.000Z',
  })
  createdAt: Date;

  @ApiProperty({
    description: '更新时间',
    example: '2023-01-01T00:00:00.000Z',
  })
  updatedAt: Date;

  @ApiProperty({
    description: '空间ID',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  spaceId: string;

  @ApiProperty({
    description: '创建者ID',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  creatorId: string;

  @ApiProperty({
    description: '创作板ID',
    example: '123e4567-e89b-12d3-a456-426614174000',
    required: false,
  })
  boardId?: string;

  @ApiProperty({
    description: '标题',
    example: 'My thought title',
  })
  title: string;

  @ApiProperty({
    description: '标题类型',
    enum: TitleType,
    enumName: 'TitleType',
    example: 'manual',
  })
  titleType: TitleType;

  @ApiProperty({
    description: '内容',
    type: 'object',
    properties: {
      raw: { type: 'string', description: '原始内容' },
      plain: { type: 'string', description: '纯文本内容' },
    },
  })
  content?: {
    raw: string;
    plain?: string;
  };

  @ApiProperty({
    description: '可见性',
    enum: Visibility,
    enumName: 'Visibility',
    example: 'private',
  })
  visibility: Visibility;

  @ApiProperty({
    description: '位置信息',
    required: true,
  })
  position: BoardPositionInfo;

  /** @deprecated 请使用 position 字段替代 boardItem */
  @ApiProperty({
    description: '创作板关联信息（已废弃，请使用 position 字段）',
    required: false,
    deprecated: true,
  })
  boardItem?: BoardItemInfo;

  /** @deprecated 请使用 position 字段替代 boardItem */
  @ApiProperty({
    description: '创作板关联信息（已废弃，请使用 position 字段）',
    required: false,
    deprecated: true,
  })
  boardIds?: string[];
}

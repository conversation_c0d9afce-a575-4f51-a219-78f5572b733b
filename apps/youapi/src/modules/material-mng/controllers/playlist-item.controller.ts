import { Body, Controller, HttpCode, Post } from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { BaseController } from '@/shared/base.controller';
import {
  CreatePlaylistItemDto,
  DeletePlaylistItemDto,
  ListPlaylistItemsDto,
  PlaylistItemDto,
  UpdatePlaylistItemTitleDto,
} from '../dto/playlist-item.dto';
import { CreatePlaylistItemCommand } from '../services/commands/playlist-item/create-playlist-item.command';
import { DeletePlaylistItemCommand } from '../services/commands/playlist-item/delete-playlist-item.command';
import { UpdatePlaylistItemTitleCommand } from '../services/commands/playlist-item/update-playlist-item-title.command';
import { ListPlaylistItemsQuery } from '../services/queries/playlist-item/list-playlist-items.query';

@ApiTags('Playlist Item')
@Controller('api/v1/playlist-item')
export class PlaylistItemController extends BaseController {
  @Post('createPlaylistItem')
  @HttpCode(200)
  @ApiOperation({
    summary: '创建播放列表项',
    description: '基于指定实体创建音频总结播放列表项',
  })
  @ApiResponse({
    status: 200,
    description: 'Success',
    type: PlaylistItemDto,
  })
  async createPlaylistItem(@Body() dto: CreatePlaylistItemDto): Promise<PlaylistItemDto> {
    const userId = this.getUserId();
    const spaceId = await this.getSpaceId();

    const command = new CreatePlaylistItemCommand(
      userId,
      spaceId,
      dto.entityType as 'snip' | 'thought',
      dto.entityId,
      dto.boardId,
      {
        model: dto.model,
        voice: dto.voice,
        emotion: dto.emotion,
      },
    );

    return this.commandBus.execute(command);
  }

  @Post('listPlaylistItems')
  @HttpCode(200)
  @ApiOperation({
    summary: '列出播放列表项',
    description: '获取指定创作板的播放列表项列表',
  })
  @ApiResponse({
    status: 200,
    description: 'Success',
    type: [PlaylistItemDto],
  })
  async listPlaylistItems(@Body() dto: ListPlaylistItemsDto): Promise<PlaylistItemDto[]> {
    const userId = this.getUserId();
    const spaceId = await this.getSpaceId();

    const query = new ListPlaylistItemsQuery(spaceId, dto.boardId, userId);
    return this.queryBus.execute(query);
  }

  @Post('deletePlaylistItem')
  @HttpCode(200)
  @ApiOperation({
    summary: '删除播放列表项',
    description: '删除指定的播放列表项',
  })
  @ApiResponse({
    status: 200,
    description: 'Success',
  })
  async deletePlaylistItem(@Body() dto: DeletePlaylistItemDto): Promise<void> {
    const userId = this.getUserId();

    const command = new DeletePlaylistItemCommand(dto.id, userId);
    await this.commandBus.execute(command);
  }

  @Post('updatePlaylistItemTitle')
  @HttpCode(200)
  @ApiOperation({
    summary: '更新播放列表项标题',
    description: '更新指定播放列表项的标题',
  })
  @ApiResponse({
    status: 200,
    description: 'Success',
    type: PlaylistItemDto,
  })
  async updatePlaylistItemTitle(@Body() dto: UpdatePlaylistItemTitleDto): Promise<PlaylistItemDto> {
    const userId = this.getUserId();

    const command = new UpdatePlaylistItemTitleCommand(dto.id, dto.title, userId);
    return this.commandBus.execute(command);
  }
}

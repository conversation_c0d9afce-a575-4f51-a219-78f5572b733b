import { Controller, HttpCode, Post, Sse } from '@nestjs/common';
import { ApiOperation, ApiTags } from '@nestjs/swagger';
import { streamText } from 'ai';
import { from, map, Observable, shareReplay } from 'rxjs';
import z from 'zod';
import { PublicRoute } from '@/common/decorators/public.decorator';
import { LLMs } from '@/common/types';
import { BaseController } from '@/shared/base.controller';
import { ModelProviderService } from '../ai/providers/index.service';

@ApiTags('Streaming Test')
@Controller('api/v1/test')
export class StreamingTestController extends BaseController {
  constructor(private readonly modelProviderService: ModelProviderService) {
    super();
  }

  @Post('basic-stream')
  @HttpCode(200)
  @Sse()
  @PublicRoute()
  @ApiOperation({
    summary: 'Basic streaming test',
    description: 'Tests the most basic streaming functionality',
  })
  async basicStream() {
    try {
      console.log('Starting basicStream...');

      // Try a different model to isolate the issue
      const instance = this.modelProviderService.getLanguageModel(LLMs.CLAUDE_4_SONNET);
      console.log('Got model instance:', instance);

      console.log('Calling streamText...');
      const result = await streamText({
        model: instance,
        messages: [
          {
            role: 'user',
            content: [
              {
                type: 'text',
                text: `Prompt caching is a powerful feature that optimizes your API usage by allowing resuming from specific prefixes in your prompts. This approach significantly reduces processing time and costs for repetitive tasks or prompts with consistent elements.

Here’s an example of how to implement prompt caching with the Messages API using a cache_control block:

# Call the model again with the same inputs up to the cache checkpoint
curl https://api.anthropic.com/v1/messages # rest of input
JSON

Copy
{"cache_creation_input_tokens":188086,"cache_read_input_tokens":0,"input_tokens":21,"output_tokens":393}
{"cache_creation_input_tokens":0,"cache_read_input_tokens":188086,"input_tokens":21,"output_tokens":393}
In this example, the entire text of “Pride and Prejudice” is cached using the cache_control parameter. This enables reuse of this large text across multiple API calls without reprocessing it each time. Changing only the user message allows you to ask various questions about the book while utilizing the cached content, leading to faster responses and improved efficiency.

​
How prompt caching works
When you send a request with prompt caching enabled:

The system checks if a prompt prefix, up to a specified cache breakpoint, is already cached from a recent query.
If found, it uses the cached version, reducing processing time and costs.
Otherwise, it processes the full prompt and caches the prefix once the response begins.
This is especially useful for:

Prompts with many examples
Large amounts of context or background information
Repetitive tasks with consistent instructions
Long multi-turn conversations
By default, the cache has a 5-minute lifetime. The cache is refreshed for no additional cost each time the cached content is used.

If you find that 5 minutes is too short, Anthropic also offers a 1-hour cache duration. The 1-hour cache is currently in beta.

For more information, see 1-hour cache duration.

Prompt caching caches the full prefix

Prompt caching references the entire prompt - tools, system, and messages (in that order) up to and including the block designated with cache_control.

​
Pricing
Prompt caching introduces a new pricing structure. The table below shows the price per million tokens for each supported model:

Model	Base Input Tokens	5m Cache Writes	1h Cache Writes	Cache Hits & Refreshes	Output Tokens
Claude Opus 4	$15 / MTok	$18.75 / MTok	$30 / MTok	$1.50 / MTok	$75 / MTok
Claude Sonnet 4	$3 / MTok	$3.75 / MTok	$6 / MTok	$0.30 / MTok	$15 / MTok
Claude Sonnet 3.7	$3 / MTok	$3.75 / MTok	$6 / MTok	$0.30 / MTok	$15 / MTok
Claude Sonnet 3.5	$3 / MTok	$3.75 / MTok	$6 / MTok	$0.30 / MTok	$15 / MTok
Claude Haiku 3.5	$0.80 / MTok	$1 / MTok	$1.6 / MTok	$0.08 / MTok	$4 / MTok
Claude Opus 3	$15 / MTok	$18.75 / MTok	$30 / MTok	$1.50 / MTok	$75 / MTok
Claude Haiku 3	$0.25 / MTok	$0.30 / MTok	$0.50 / MTok	$0.03 / MTok	$1.25 / MTok
Note:

5-minute cache write tokens are 1.25 times the base input tokens price
1-hour cache write tokens are 2 times the base input tokens price
Cache read tokens are 0.1 times the base input tokens price
Regular input and output tokens are priced at standard rates
​
How to implement prompt caching
​
Supported models
Prompt caching is currently supported on:

Claude Opus 4
Claude Sonnet 4
Claude Sonnet 3.7
Claude Sonnet 3.5
Claude Haiku 3.5
Claude Haiku 3
Claude Opus 3
​
Structuring your prompt
Place static content (tool definitions, system instructions, context, examples) at the beginning of your prompt. Mark the end of the reusable content for caching using the cache_control parameter.

Cache prefixes are created in the following order: tools, system, then messages. This order forms a hierarchy where each level builds upon the previous ones.

​
How automatic prefix checking works
You can use just one cache breakpoint at the end of your static content, and the system will automatically find the longest matching prefix. Here’s how it works:

When you add a cache_control breakpoint, the system automatically checks for cache hits at all previous content block boundaries (up to approximately 20 blocks before your explicit breakpoint)
If any of these previous positions match cached content from earlier requests, the system uses the longest matching prefix
This means you don’t need multiple breakpoints just to enable caching - one at the end is sufficient
​
When to use multiple breakpoints
You can define up to 4 cache breakpoints if you want to:

Cache different sections that change at different frequencies (e.g., tools rarely change, but context updates daily)
Have more control over exactly what gets cached
Ensure caching for content more than 20 blocks before your final breakpoint
`,
              },
            ],
            providerOptions: {
              anthropic: {
                cacheControl: { type: 'ephemeral', ttl: '1h' },
              },
            },
          },
        ],
        headers: {
          'anthropic-beta': 'extended-cache-ttl-2025-04-11',
        },
      });

      console.log('Got streamText result, creating Observable...');

      // Try using the iterator approach instead of Observable
      return from(result.fullStream).pipe(
        map((chunk) => {
          console.log('chunk', chunk);
          return `data: ${JSON.stringify(chunk)}\n\n`;
        }),
        shareReplay({ bufferSize: Number.MAX_SAFE_INTEGER, refCount: false }),
      );
    } catch (error) {
      console.error('basicStream setup error:', error);
      throw error;
    }
  }

  @Post('tool-stream')
  @HttpCode(200)
  @Sse()
  @PublicRoute()
  async toolStream() {
    try {
      console.log('Starting toolStream...');

      // Try a different model to isolate the issue
      const instance = this.modelProviderService.getLanguageModel(LLMs.GPT_4O);
      console.log('Got model instance:', instance);

      console.log('Calling streamText...');
      const result = await streamText({
        model: instance,
        toolCallStreaming: true,
        tools: {
          googleSearch: {
            parameters: z.object({
              query: z.string().describe('Tool input parameters'),
            }),
            description: 'Provide a search query',
            execute: async (args) => {
              console.log('execute', args);
              return {
                type: 'text',
                text: 'test',
              };
            },
          },
        },
        toolChoice: 'required',
        messages: [
          {
            role: 'user',
            content: [
              {
                type: 'text',
                text: `Cognition 公司是做什么的`,
              },
            ],
          },
        ],
      });

      console.log('Got streamText result, creating Observable...');

      // Try using the iterator approach instead of Observable
      return from(result.fullStream).pipe(
        map((chunk) => {
          console.log('chunk', chunk);
          return `data: ${JSON.stringify(chunk)}\n\n`;
        }),
        shareReplay({ bufferSize: Number.MAX_SAFE_INTEGER, refCount: false }),
      );
    } catch (error) {
      console.error('basicStream setup error:', error);
      throw error;
    }
  }

  @Post('simple-stream')
  @HttpCode(200)
  @PublicRoute()
  @Sse()
  @ApiOperation({
    summary: 'Simple immediate stream',
    description: 'Tests streaming with immediate values',
  })
  simpleStream(): Observable<any> {
    // Create an observable from static values
    const stream = new Observable((subscriber) => {
      // Emit some immediate values
      subscriber.next({ type: 'start', message: 'Stream starting...' });

      setTimeout(() => {
        subscriber.next({ type: 'data', message: 'First chunk' });
      }, 100);

      setTimeout(() => {
        subscriber.next({ type: 'data', message: 'Second chunk' });
      }, 200);

      setTimeout(() => {
        subscriber.next({ type: 'end', message: 'Stream complete' });
        subscriber.complete();
      }, 300);
    });

    return stream;
  }
}

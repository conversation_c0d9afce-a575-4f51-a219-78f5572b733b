import { CoreMessage } from 'ai';
import { CompletionBlock } from '../domain/models/completion-block.entity';

export function blocksToMessages(blocks: CompletionBlock[]): CoreMessage[] {
  const fullMessages: CoreMessage[] = blocks.flatMap((block) => block.toCoreMessages());

  const messages = [];
  for (const message of fullMessages) {
    const lastMessage = messages[messages.length - 1];
    // 在 content 长度小于 5 时，合并同类 role 的 message
    if (
      lastMessage &&
      lastMessage.role === message.role &&
      lastMessage.content.length + message.content.length < 5
    ) {
      lastMessage.content.push(...message.content);
    } else {
      messages.push(message);
    }
  }

  return messages;
}

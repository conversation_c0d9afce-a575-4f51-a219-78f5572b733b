import { Injectable } from '@nestjs/common';
import { LLMs } from '@/common/types';
import { purifyObject } from '@/common/utils';
import { Generation } from '../domain/models/generation.entity';
import { OverviewRequestDto } from '../dto/overview.dto';
import { PromptService } from '../prompt/index.service';
import { TextRunner } from '../runners';

@Injectable()
export class OverviewService {
  constructor(private readonly promptService: PromptService) {}

  async overview(dto: OverviewRequestDto) {
    const { pageData, aiLanguage, aiSecondLanguage, enableBilingual = true, options = {} } = dto;

    const needBilingual =
      enableBilingual === true && !!aiSecondLanguage && aiLanguage !== aiSecondLanguage;

    // Extract content from pageData (similar to youapp implementation)
    const content = this.extractContentFromPageData(pageData);

    // Determine content length for summarize length
    let summarizeLength = '50';
    if (content.length < 200) {
      summarizeLength = '20';
    }

    // Determine which prompt to use based on content type
    const promptName = this.determineOverviewPromptName(pageData);

    // Check if this is a multi-speaker video
    const speakerLabels = pageData.speakerLabels;
    const isMultiSpeakerVideo = promptName === 'overview-video-prompt' && speakerLabels;

    // Build variables for the prompt (matching youapp's finalVariables)
    const variables = purifyObject({
      aiLanguage,
      aiSecondLanguage: needBilingual ? aiSecondLanguage : '',
      summarizeLength,
      speakerLabels,
      content,
      url: pageData?.webpage?.url || pageData?.url,
      title: pageData.title || '',
      summarySecondLanguageInstruction: needBilingual
        ? this.buildSummarySecondLanguageInstruction(aiLanguage, aiSecondLanguage)
        : undefined,
      outlineSecondLanguageInstruction: needBilingual
        ? this.buildOutlineSecondLanguageInstruction(aiLanguage, aiSecondLanguage)
        : undefined,
      aiSecondLanguageInstruction: this.buildAiSecondLanguageInstruction(
        aiLanguage,
        needBilingual ? aiSecondLanguage : undefined,
      ),
      // For multi-speaker videos, include speakerLables (typo from youapp)
      speakerLables: speakerLabels,
    });

    // Adjust prompt name for multi-speaker videos
    const finalPromptName = isMultiSpeakerVideo
      ? 'overview-video-multi-speaker-prompt'
      : promptName;

    // Fetch the appropriate overview prompt
    const { prompt, promptMessages } = this.promptService.getPromptAndMessages(
      finalPromptName,
      variables,
    );

    // Create generation domain model
    const generation = new Generation({
      model: options.model as LLMs,
      temperature: options.regenerate ? 1 : options.temperature || 0.5,
      prompt,
      promptMessages,
      traceMetadata: {
        promptName: finalPromptName,
        contentType: pageData.type || 'unknown',
        hasMultipleSpeakers: String(isMultiSpeakerVideo),
      },
    });

    // Create and return TextRunner with generation and compiled messages
    const runner = new TextRunner();
    return runner.addGeneration(generation).generateStream({
      useCache: options.useCache,
    });
  }

  private extractContentFromPageData(pageData: any): string {
    // Extract content from various possible sources, matching youapp's logic
    let content =
      pageData.content?.plain ||
      pageData.messages?.plain ||
      pageData.transcript?.plain ||
      pageData.show_notes?.plain ||
      '';

    // If no plain content found, try raw content
    if (!content) {
      content = pageData.content?.raw || pageData.content_raw || '';
    }

    // If still no content, check other fields
    if (!content) {
      if (pageData.content_plain) {
        content = pageData.content_plain;
      } else if (pageData.transcript) {
        content =
          typeof pageData.transcript === 'string'
            ? pageData.transcript
            : pageData.transcript?.plain || '';
      } else if (pageData.messages) {
        content = Array.isArray(pageData.messages)
          ? pageData.messages.map((m: any) => m.content).join('\n')
          : pageData.messages?.plain || '';
      }
    }

    // Apply text sampling logic from youapp (60k chars, min 10k)
    const beforeLength = content.length;
    if (content.length > 60000) {
      const truncateLength = Math.max(10000, Math.floor(content.length * 0.1));
      content = content.substring(0, truncateLength);
      console.log(`plain length has been redacted from ${beforeLength} to ${content.length}`);
    }

    return content || 'empty';
  }

  private determineOverviewPromptName(pageData: any): string {
    // Determine prompt based on content type (matching youapp logic)
    if (pageData.type === 'video' || pageData.type === 'voice') {
      return 'overview-video-prompt';
    }
    return 'overview-article-prompt';
  }

  private buildSummarySecondLanguageInstruction(
    primaryLanguage: string,
    secondaryLanguage: string,
  ): string {
    return `- You should first summarize in ${primaryLanguage}, followed by a translation in ${secondaryLanguage} language`;
  }

  private buildOutlineSecondLanguageInstruction(
    primaryLanguage: string,
    secondaryLanguage: string,
  ): string {
    return `- The bullet points should be ${primaryLanguage} followed by ${secondaryLanguage} in the same sentence or phrase
- The headings should be ${primaryLanguage} followed by ${secondaryLanguage} in the same sentence or phrase`;
  }

  private buildAiSecondLanguageInstruction(
    primaryLanguage?: string,
    secondaryLanguage?: string,
  ): string {
    return `${primaryLanguage ? `${primaryLanguage} ` : ''}${
      secondaryLanguage ? `or ${secondaryLanguage} ` : ''
    }`;
  }
}

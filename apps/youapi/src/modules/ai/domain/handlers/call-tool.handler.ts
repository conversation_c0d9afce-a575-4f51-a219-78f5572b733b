/**
 * 处理 Tool Call
 *
 */

import { Injectable, Logger } from '@nestjs/common';
import { CommandHandler, ICommandHandler, QueryBus } from '@nestjs/cqrs';
import { CoreMessage } from 'ai';
import { ToolCallError } from '@/common/errors';
import { ToolCallContext } from '@/common/types';
import { Chat } from '@/modules/chat/domain/chat/models/chat.entity';
import { GetChatDetailQuery } from '@/modules/chat/services/queries/get-chat-detail.query';
import { CallToolCommand } from '../commands/call-tool.command';

@Injectable()
@CommandHandler(CallToolCommand)
export class CallToolHandler implements ICommandHandler<CallToolCommand> {
  private readonly logger = new Logger(CallToolHandler.name);

  constructor(private readonly queryBus: QueryBus) {}

  async execute(command: CallToolCommand): Promise<CoreMessage> {
    const { generation, subject, input, completionBlock } = command.param;

    try {
      let chat: Chat | null = null;
      if (generation.bizArgs?.chatId) {
        chat = await this.queryBus.execute(new GetChatDetailQuery(generation.bizArgs.chatId));
      }

      const toolContext: ToolCallContext = {
        chat,
        userId: chat?.creatorId || generation.bizArgs.userId,
        params: input,
        completionBlock,
      };

      // Example of emitting tool progress/status to the stream
      // IMPORTANT: Only use subject.next(), never subject.complete() or subject.error()

      // const isGenerator =
      //   Object.getPrototypeOf(fn).constructor.name === "AsyncGeneratorFunction";
      // let result: ToolCallResult;

      // // Execute the tool function and store results directly in the block
      // if (isGenerator) {
      //   // Execute generator function and allow yielding intermediate results
      //   const generator = fn(toolContext) as AsyncGenerator<
      //     CompletionstreamTextGeneration<streamTextGenerationUnion>,
      //     ToolCallResult,
      //     unknown
      //   >;

      //   // Forward any intermediate yields from the tool function
      //   let yielded: IteratorResult<
      //     CompletionstreamTextGeneration<streamTextGenerationUnion>,
      //     ToolCallResult
      //   >;
      //   while (
      //     !(yielded = await generator.next()).done
      //   ) {
      //     subject.next(yielded.value);
      //   }
      //   // Process tool result
      //   result = yielded.value;
      // } else {
      //   // Execute regular async function with timeout
      //   result = (await fn(toolContext)) as ToolCallResult;
      // }

      const result = {
        toolResult: {
          result: {
            a: 1,
            b: 2,
            c: 3,
            d: 4,
            e: 5,
            f: 6,
            g: 7,
          },
        },
        toolResponse: `Found 1 article, title: Is SaaS dead?
url: https://www.reddit.com/r/ycombinator/comments/1fvrcmv/is_saas_dead/

After wrapping up my last SaaS startup in the e-commerce space, I’m brainstorming ideas for what to start next.

Every space or idea I evaluate already has hundreds of companies (seed, Series A-B), and new ones are popping up every two days.

Tbh, it feels like all the software in the world has already been made 😅

Has building become this easy? Is software no longer a moat? If supply outpaces demand, will software be obsolete in a few years?

People say execution is the differentiator, but I’m not sure why they think they can’t be out-executed by a 19-year-old prodigy coder with a lot of money in the bank.
        `,
      };

      return {
        role: 'tool',
        content: [
          {
            type: 'tool-result',
            result:
              typeof result.toolResponse === 'string'
                ? result.toolResponse
                : JSON.stringify(result.toolResponse),
            toolName: completionBlock.toolName,
            toolCallId: completionBlock.toolId,
            // @ts-ignore
            jsonResult: result.toolResult,
          },
        ],
      };
    } catch (error) {
      const error_info = ToolCallError.fromError(
        error,
        completionBlock.toolName,
        completionBlock.toolId,
      ).json('Failed to run ' + completionBlock.toolName);

      // Return error result instead of throwing to prevent stream termination
      return {
        role: 'tool',
        content: [
          {
            type: 'tool-result',
            result: 'ERROR: ' + JSON.stringify(error_info),
            toolName: completionBlock.toolName,
            toolCallId: completionBlock.toolId,
            // @ts-ignore
            jsonResult: error_info,
          },
        ],
      };
    }
  }
}

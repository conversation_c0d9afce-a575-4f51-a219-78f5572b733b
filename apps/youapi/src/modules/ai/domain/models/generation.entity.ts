import { Logger } from '@nestjs/common';
import { AggregateRoot } from '@nestjs/cqrs';
import { CoreMessage, GenerateTextResult, TextStreamPart } from 'ai';
import { ChatPromptClient, LangfuseGenerationClient, LangfuseSpanClient } from 'langfuse';
import { ChatCompletionToolChoiceOption } from 'openai/resources/index.js';
import { interval, Observable, Subject } from 'rxjs';
import { finalize, takeUntil } from 'rxjs/operators';
import { LangfuseTraceService } from '@/common/services/langfuse-trace.service';
import {
  CompletionBlockTypeEnum,
  CompletionStreamChunk,
  CompletionStreamModeEnum,
  DEFAULT_AI_CHAT_MODEL,
  GenerationStatusEnum,
  LLMs,
  ToolNames,
} from '@/common/types';
import { ApplicationContext } from '@/common/utils/application-context';
import {
  CompletionBlock,
  ContentCompletionBlock,
  ReasoningCompletionBlock,
  ToolCompletionBlock,
} from '@/modules/ai/domain/models/completion-block.entity';
import { CompletionBlockRepository } from '@/modules/ai/repositories/completion-block.repository';
import { blocksToMessages } from '../../utils/blocksToMessages';

export interface ImageEditMask {
  x: number;
  y: number;
  width: number;
  height: number;
}

export interface ImageEditOptions {
  prompt: string;
  size?: '1024x1024' | '1536x1024' | '1024x1536' | 'auto';
  quality?: 'standard' | 'hd';
  mask?: ImageEditMask[];
}

export interface ImageGenerationOptions {
  prompt: string;
  size?: '256x256' | '512x512' | '1024x1024' | '1792x1024' | '1024x1792';
  quality?: 'standard' | 'hd';
  style?: 'vivid' | 'natural';
  n?: number;
}

export class Generation extends AggregateRoot {
  protected readonly logger = new Logger(Generation.name);

  public model: LLMs;
  public temperature: number;
  public tools: ToolNames[];
  public toolChoice: ChatCompletionToolChoiceOption;
  public modelOptions: Record<string, any>;
  public traceMetadata: Record<string, string>;
  public bizArgs: Record<string, any>;

  protected status: GenerationStatusEnum = GenerationStatusEnum.PENDING;
  protected trace: LangfuseGenerationClient | null = null;

  protected prompt: ChatPromptClient;
  public promptName: string;
  public promptVersion: number;
  public promptMessages: CoreMessage[];
  public generatedMessages: CoreMessage[];
  public blocks: CompletionBlock[];

  constructor(param: {
    model?: LLMs;
    temperature?: number;
    tools?: ToolNames[];
    toolChoice?: ChatCompletionToolChoiceOption;
    bizArgs?: Record<string, any>;
    modelOptions?: Record<string, any>;
    traceMetadata?: Record<string, string>;
    prompt?: ChatPromptClient;
    promptMessages?: CoreMessage[];
  }) {
    super();

    this.prompt = param.prompt;
    const promptConfig = param.prompt?.config as {
      model?: LLMs;
      temperature?: number;
      topP?: number;
      frequencyPenalty?: number;
      presencePenalty?: number;
    };

    this.model = param.model || promptConfig?.model || DEFAULT_AI_CHAT_MODEL;
    this.temperature = param.temperature || promptConfig?.temperature || 0.5;
    this.tools = param.tools || [];
    this.toolChoice = param.toolChoice || (this.tools.length > 0 ? 'auto' : 'none');

    const topP = param.modelOptions?.topP || promptConfig?.topP || 1;
    const frequencyPenalty =
      param.modelOptions?.frequencyPenalty || promptConfig?.frequencyPenalty || 0;
    const presencePenalty =
      param.modelOptions?.presencePenalty || promptConfig?.presencePenalty || 0;

    this.bizArgs = param.bizArgs || {};
    this.traceMetadata = param.traceMetadata || {};
    this.modelOptions = {
      ...param.modelOptions,
      ...(topP ? { topP } : {}),
      ...(frequencyPenalty ? { frequencyPenalty } : {}),
      ...(presencePenalty ? { presencePenalty } : {}),
    };
    this.promptName = param.prompt?.name || '';
    this.promptVersion = param.prompt?.version || 1;
    this.promptMessages = param.promptMessages || [];
    this.blocks = [];
    this.generatedMessages = [];
  }

  public setStatus(status: GenerationStatusEnum) {
    this.status = status;
  }

  public addGeneratedMessage(message: CoreMessage) {
    this.generatedMessages.push(message);
  }

  private chatMessageId: string | null = null;
  public getChatMessageId(): string | null {
    return this.chatMessageId;
  }
  public setChatMessageId(messageId: string) {
    this.chatMessageId = messageId;
    return this;
  }

  public setBizArgs(bizArgs: Record<string, any>) {
    this.bizArgs = {
      ...this.bizArgs,
      ...bizArgs,
    };
    return this;
  }

  public getToolBlockByToolId(toolId: string): ToolCompletionBlock | null {
    return (
      (this.blocks?.find(
        (block) =>
          block.type === CompletionBlockTypeEnum.TOOL &&
          (block as ToolCompletionBlock).toolId === toolId,
      ) as ToolCompletionBlock) || null
    );
  }

  /**
   * 保存所有完成块到数据库 (repository 会自动筛选新的或已修改的块)
   */
  private async saveBlocks(): Promise<void> {
    try {
      this.logger.debug(`Attempting to save ${this.blocks.length} completion blocks`);
      if (this.blocks.length > 0) {
        const newBlocks = this.blocks.filter((block) => block.isNew);
        const modifiedBlocks = this.blocks.filter((block) => block.isModified);
        this.logger.debug(
          `Found ${newBlocks.length} new blocks and ${modifiedBlocks.length} modified blocks`,
        );

        const completionBlockRepository =
          ApplicationContext.getProvider<CompletionBlockRepository>(CompletionBlockRepository);
        await completionBlockRepository.saveMany(this.blocks);
        this.logger.debug('Successfully saved completion blocks');
      } else {
        this.logger.debug('No completion blocks to save');
      }
    } catch (error) {
      this.logger.error('Failed to save completion blocks:', error);
      // 不抛出错误，避免中断主流程
    }
  }

  /**
   * 录入 Completion Block
   * 包含每2秒的定期持久化和完成时的最终持久化
   * @param observable
   */
  public processTextStream(
    observable: Observable<TextStreamPart<any>>,
    emitSubject?: Subject<CompletionStreamChunk<any>>,
  ) {
    const subject = emitSubject || new Subject<CompletionStreamChunk<any>>();
    const traceService = ApplicationContext.getProvider<LangfuseTraceService>(LangfuseTraceService);
    const spanMap = new Map<string, LangfuseSpanClient>();
    const getLastBlock = () => {
      if (this.blocks.length === 0) {
        return null;
      }
      return this.blocks[this.blocks.length - 1];
    };
    const getToolBlockByToolId = this.getToolBlockByToolId.bind(this);

    // 设置定期保存完成块的定时器 (每2秒)
    const persistenceTimer = interval(2000)
      .pipe(
        takeUntil(subject), // 当subject完成或错误时停止定时器
      )
      .subscribe(() => {
        this.saveBlocks(); // 异步保存，不等待结果
      });

    observable
      .pipe(
        finalize(async () => {
          // 清理定时器
          persistenceTimer.unsubscribe();
          // 最后一次保存完成块
          await this.saveBlocks();
          // 转换为 CoreMessage
          this.generatedMessages = blocksToMessages(this.blocks);
        }),
      )
      .subscribe(async (chunk) => {
        if (chunk.type === 'error') {
          // 错误时也保存一次完成块
          await this.saveBlocks();

          subject.error(chunk.error);
          this.status = GenerationStatusEnum.FAILED;

          return;
        }

        if (chunk.type === 'text-delta') {
          let current: ContentCompletionBlock | null = getLastBlock() as ContentCompletionBlock;
          if (!current || current.type !== CompletionBlockTypeEnum.CONTENT) {
            current = ContentCompletionBlock.createNew({
              messageId: this.chatMessageId,
              data: '',
            });

            this.blocks.push(current);

            subject.next({
              mode: CompletionStreamModeEnum.INSERT,
              data: current.toCompletionStreamChunk(),
              dataType: 'CompletionBlock',
            });
          }

          (current as ContentCompletionBlock).appendContent(chunk.textDelta);

          subject.next({
            mode: CompletionStreamModeEnum.APPEND_STRING,
            data: chunk.textDelta,
            path: 'data',
            targetId: current.id,
            targetType: 'CompletionBlock',
          });

          return;
        }

        if (
          chunk.type === 'reasoning' ||
          chunk.type === 'redacted-reasoning' ||
          chunk.type === 'reasoning-signature'
        ) {
          let current: ReasoningCompletionBlock | null = getLastBlock() as ReasoningCompletionBlock;
          if (!current || current.type !== CompletionBlockTypeEnum.REASONING) {
            current = ReasoningCompletionBlock.createNew({
              messageId: this.chatMessageId,
              data: '',
            });

            this.blocks.push(current);

            subject.next({
              mode: CompletionStreamModeEnum.INSERT,
              data: current.toCompletionStreamChunk(),
              dataType: 'CompletionBlock',
            });
          }

          if (chunk.type === 'reasoning') {
            (current as ReasoningCompletionBlock).appendContent(chunk.textDelta);

            subject.next({
              mode: CompletionStreamModeEnum.APPEND_STRING,
              data: chunk.textDelta,
              path: 'data',
              targetId: current.id,
              targetType: 'CompletionBlock',
            });
          } else if (chunk.type === 'redacted-reasoning') {
            (current as ReasoningCompletionBlock).appendRedactedReasoning(chunk.data);
          } else if (chunk.type === 'reasoning-signature') {
            (current as ReasoningCompletionBlock).setSignature(chunk.signature);
          }

          return;
        }

        if (
          chunk.type === 'tool-call' ||
          chunk.type === 'tool-call-delta' ||
          chunk.type === 'tool-call-streaming-start' ||
          chunk.type === 'tool-result'
        ) {
          let current: ToolCompletionBlock | null = getToolBlockByToolId(chunk.toolCallId);

          if (!current) {
            current = ToolCompletionBlock.createNew({
              messageId: this.chatMessageId,
              toolId: chunk.toolCallId,
              toolName: chunk.toolName,
            });

            this.blocks.push(current);

            subject.next({
              mode: CompletionStreamModeEnum.INSERT,
              data: current.toCompletionStreamChunk(),
              dataType: 'CompletionBlock',
            });
            spanMap.set(
              chunk.toolCallId,
              await traceService.addSpan({
                name: 'tool-call-' + chunk.toolCallId,
                input: {},
              }),
            );
          }

          if (chunk.type === 'tool-call-delta') {
            current.appendPartialToolArguments(chunk.argsTextDelta);

            subject.next({
              mode: CompletionStreamModeEnum.APPEND_JSON,
              data: chunk.argsTextDelta,
              path: 'tool_arguments',
              targetId: current.id,
              targetType: 'CompletionBlock',
            });
          } else if (chunk.type === 'tool-call') {
            current.startExecution(chunk.args as Record<string, unknown>);
          } else if (chunk.type === 'tool-result') {
            const toolResult = (chunk as any).jsonResult;
            if (!chunk.result.startsWith('ERROR')) {
              current.completeExecution(toolResult, chunk.result);

              subject.next({
                mode: CompletionStreamModeEnum.REPLACE,
                data: current.toolResultInCoreMessage,
                path: 'tool_result',
                targetId: current.id,
                targetType: 'CompletionBlock',
              });
            } else {
              current.failExecution(toolResult, chunk.result);

              subject.next({
                mode: CompletionStreamModeEnum.REPLACE,
                data: current.toolResultInCoreMessage,
                path: 'extra.error',
                targetId: current.id,
                targetType: 'CompletionBlock',
              });
            }

            this.generatedMessages = blocksToMessages(this.blocks);
            spanMap.get(chunk.toolCallId)?.update({
              input: current.toolArguments,
              output: {
                result: current.toolResult,
                response: current.toolResponse,
              },
              statusMessage: current.status,
            });
          }

          return;
        }
      });

    return subject.asObservable();
  }

  public processTextResult(result: GenerateTextResult<any, any>) {
    const subject = new Subject<CompletionStreamChunk<any>>();

    // add block
    const block = ContentCompletionBlock.createNew({
      messageId: this.chatMessageId,
      data: result.text,
    });
    this.blocks.push(block);

    // add messages
    this.addGeneratedMessage({
      role: 'assistant',
      content: result.text,
    });

    // emit event
    subject.next({
      mode: CompletionStreamModeEnum.INSERT,
      data: block.toCompletionStreamChunk(),
      dataType: 'CompletionBlock',
    });

    return subject.asObservable();
  }

  static fromCurrentGeneration(current: Generation): Generation {
    return new Generation({
      model: current.model,
      temperature: current.temperature,
      tools: current.tools,
      toolChoice: current.toolChoice as ChatCompletionToolChoiceOption,
      bizArgs: current.bizArgs,
      modelOptions: current.modelOptions,
      traceMetadata: current.traceMetadata,
      prompt: current.prompt,
      promptMessages: [...current.promptMessages, ...current.generatedMessages],
    });
  }
}

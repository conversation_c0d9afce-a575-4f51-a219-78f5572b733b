import { Subject } from 'rxjs';
import { CompletionStreamChunk } from '@/common/types';
import { ToolCompletionBlock } from '@/modules/ai/domain/models/completion-block.entity';
import { Generation } from '../models/generation.entity';

export class CallToolCommand {
  constructor(
    public readonly param: {
      input: Record<string, any>;
      generation: Generation;
      completionBlock: ToolCompletionBlock;
      subject?: Subject<CompletionStreamChunk<any>>;
    },
  ) {}
}

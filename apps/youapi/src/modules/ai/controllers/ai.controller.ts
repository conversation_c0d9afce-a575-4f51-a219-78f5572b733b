import { Body, Controller, HttpCode, Post, Sse, UseInterceptors } from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { SSEErrorHandlerInterceptor } from '@/common/interceptors/sse-error-handler.interceptor';
import { LangfuseService } from '@/infra/langfuse';
import { BaseController } from '@/shared/base.controller';
import { CreateLLMFeedbackDto } from '../dto/create-llm-feedback.dto';
import { CreateTingwuTaskDto } from '../dto/create-tingwu-task.dto';
import { EditImageDto } from '../dto/edit-image.dto';
import { ExplainRequestDto } from '../dto/explain.dto';
import { GetTingwuTaskInfoDto } from '../dto/get-tingwu-task-info.dto';
import { OverviewRequestDto } from '../dto/overview.dto';
import { SelfDescByFeedsRequestDto } from '../dto/self-desc-by-feeds.dto';
import { StopTingwuTaskDto } from '../dto/stop-tingwu-task.dto';
import { UpdateLLMFeedbackDto } from '../dto/update-llm-feedback.dto';
import { ExplainService } from '../services/explain.service';
import { ImageService } from '../services/image.service';
import { OverviewService } from '../services/overview.service';
import { SelfDescService } from '../services/self-desc.service';
import { SpeechService } from '../services/speech.service';
import { TingwuService } from '../services/tingwu.service';
import { observableToSse, toOpenaiCompatibleObservable } from '../utils/toObservable';

@UseInterceptors(SSEErrorHandlerInterceptor)
@ApiTags('AI')
@Controller('api/v1')
export class AIController extends BaseController {
  constructor(
    private readonly explainService: ExplainService,
    private readonly imageService: ImageService,
    private readonly overviewService: OverviewService,
    private readonly langfuseService: LangfuseService,
    private readonly tingwuService: TingwuService,
    private readonly speechService: SpeechService,
    private readonly selfDescService: SelfDescService,
  ) {
    super();
  }

  @Post('createLLMFeedback')
  @HttpCode(200)
  @ApiOperation({
    summary: '创建LLM反馈',
    description: '为LLM生成创建用户反馈评分',
  })
  @ApiResponse({
    status: 200,
    description: '成功创建反馈',
    schema: {
      type: 'object',
      properties: {
        id: { type: 'string' },
      },
    },
  })
  async createLLMFeedback(@Body() dto: CreateLLMFeedbackDto) {
    const feedback = await this.langfuseService.createScore(dto.traceId, {
      name: dto.name,
      value: dto.value,
      comment: dto.comment,
    });
    return { id: feedback.id };
  }

  @Post('updateLLMFeedback')
  @HttpCode(200)
  @ApiOperation({
    summary: '更新LLM反馈',
    description: '更新LLM生成的用户反馈评分',
  })
  @ApiResponse({
    status: 200,
    description: '成功更新反馈',
  })
  async updateLLMFeedback(@Body() dto: UpdateLLMFeedbackDto): Promise<void> {
    await this.langfuseService.updateScore(dto.scoreId, {
      name: dto.name,
      value: dto.value,
      comment: dto.comment,
    });
  }

  @Post('createTingwuTask')
  @HttpCode(200)
  @ApiOperation({
    summary: '创建 tingwu 任务用于实时转写',
    description: '创建 tingwu 任务用于实时转写',
  })
  @ApiResponse({
    status: 200,
    description: 'OK',
  })
  async createTingwuTask(@Body() dto: CreateTingwuTaskDto) {
    return await this.tingwuService.createTask(dto);
  }

  @Post('stopTingwuTask')
  @HttpCode(200)
  @ApiOperation({
    summary: '停止 tingwu 任务',
    description: '停止 tingwu 任务',
  })
  @ApiResponse({
    status: 200,
    description: 'OK',
  })
  async stopTingwuTask(@Body() dto: StopTingwuTaskDto) {
    return await this.tingwuService.stopTask(dto.taskId);
  }

  @Post('getTingwuTaskInfo')
  @HttpCode(200)
  @ApiOperation({
    summary: '获取当前 tingwu 任务状态',
    description: '获取当前 tingwu 任务状态，可以用来轮询',
  })
  @ApiResponse({
    status: 200,
    description: 'OK',
  })
  async getTingwuTaskInfo(@Body() dto: GetTingwuTaskInfoDto) {
    return await this.tingwuService.getTaskInfo(dto.taskId);
  }

  @Post('editImage')
  @HttpCode(200)
  @ApiOperation({
    summary: '编辑图片',
    description: '编辑图片',
  })
  @ApiResponse({
    status: 200,
    description: 'OK',
  })
  async editImage(@Body() dto: EditImageDto) {
    return await this.imageService.editImage(dto);
  }

  @Post('getSelfDescByFeeds')
  @HttpCode(200)
  @ApiOperation({
    summary: '通过 feed 流来获取用户的爱好分析',
    description: '通过 feed 流来获取用户的爱好分析',
  })
  @ApiResponse({
    status: 200,
    description: 'Success',
  })
  @Sse()
  async getSelfDescByFeeds(@Body() dto: SelfDescByFeedsRequestDto) {
    const observable = await this.selfDescService.getSelfDescByFeeds(dto);
    return observableToSse(toOpenaiCompatibleObservable(observable));
  }

  @Post('getExplain/chat/completions')
  @HttpCode(200)
  @ApiOperation({
    summary: '插件 Explain 功能',
  })
  @ApiResponse({
    status: 200,
    description: 'Success',
  })
  @Sse()
  async explainCompletions(@Body() dto: ExplainRequestDto) {
    const observable = await this.explainService.explain(dto);
    return observableToSse(toOpenaiCompatibleObservable(observable));
  }

  @Post('getOverview/chat/completions')
  @HttpCode(200)
  @ApiOperation({
    summary: '生成 Overview，给插件用',
  })
  @ApiResponse({
    status: 200,
    description: 'Success',
  })
  @Sse()
  async overviewCompletions(@Body() dto: OverviewRequestDto) {
    const observable = await this.overviewService.overview(dto);
    return observableToSse(toOpenaiCompatibleObservable(observable));
  }
}

import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsEnum, IsObject, IsOptional, IsString, ValidateNested } from 'class-validator';
import { MessageStatusEnum, StreamDataTypeEnum } from '@/common/types';

/**
 * Base Stream Message DTO
 */
export abstract class BaseStreamMessageDto {
  @ApiProperty({
    enum: StreamDataTypeEnum,
    enumName: 'StreamDataTypeEnum',
    description: 'The type of stream message',
    example: StreamDataTypeEnum.CONTENT,
  })
  @IsEnum(StreamDataTypeEnum)
  type: StreamDataTypeEnum;
}

/**
 * Stream Data DTO - for complete data objects
 */
export class StreamDataDto extends BaseStreamMessageDto {
  @ApiProperty({
    enum: StreamDataTypeEnum,
    enumName: 'StreamDataTypeEnum',
    example: StreamDataTypeEnum.DATA,
  })
  declare type: StreamDataTypeEnum.DATA;

  @ApiProperty({
    description: 'The type of data being streamed',
    example: 'Message',
  })
  @IsString()
  dataType: string;

  @ApiProperty({
    description: 'The data payload',
    example: { id: 'msg-123', content: 'Hello world' },
  })
  @IsObject()
  data: any;

  @ApiProperty({
    description: 'The ID of the data object',
    example: 'msg-123',
  })
  @IsString()
  id: string;
}

/**
 * Stream Chunked Data DTO - for partial data updates
 */
export class StreamChunkedDataDto extends BaseStreamMessageDto {
  @ApiProperty({
    enum: StreamDataTypeEnum,
    enumName: 'StreamDataTypeEnum',
    example: StreamDataTypeEnum.CHUNKED_DATA,
  })
  declare type: StreamDataTypeEnum.CHUNKED_DATA;

  @ApiProperty({
    description: 'The type of parent data being updated',
    example: 'Message',
  })
  @IsString()
  parentDataType: string;

  @ApiProperty({
    description: 'The ID of the parent object being updated',
    example: 'msg-123',
  })
  @IsString()
  parentId: string;

  @ApiProperty({
    description: 'The path to the field being updated',
    example: 'reasoning_begin_at',
  })
  @IsString()
  chunkPath: string;

  @ApiProperty({
    description: 'The chunk data being updated',
    example: '1640995200000',
  })
  chunkData: any;
}

/**
 * Stream Error DTO - for error information
 */
export class StreamErrorDto extends BaseStreamMessageDto {
  @ApiProperty({
    enum: StreamDataTypeEnum,
    enumName: 'StreamDataTypeEnum',
    example: StreamDataTypeEnum.ERROR,
  })
  declare type: StreamDataTypeEnum.ERROR;

  @ApiProperty({
    description: 'Error information',
    example: { name: 'ValidationError', message: 'Invalid input', code: 'VALIDATION_FAILED' },
  })
  @IsObject()
  error: {
    name?: string;
    message?: string;
    code?: string;
    [key: string]: any;
  };
}

/**
 * Stream Text DTO - for streaming text content
 */
export class StreamTextDto extends BaseStreamMessageDto {
  @ApiProperty({
    enum: StreamDataTypeEnum,
    enumName: 'StreamDataTypeEnum',
    example: StreamDataTypeEnum.CONTENT,
  })
  declare type: StreamDataTypeEnum.CONTENT;

  @ApiProperty({
    description: 'The text content being streamed',
    example: 'Hello world',
  })
  @IsString()
  data: string;

  @ApiPropertyOptional({
    description: 'The type of content being streamed',
    enum: ['content', 'reasoning', 'error'],
    example: 'content',
  })
  @IsOptional()
  @IsString()
  contentType?: 'content' | 'reasoning' | 'error';
}

/**
 * Stream Trace DTO - for trace ID information
 */
export class StreamTraceDto extends BaseStreamMessageDto {
  @ApiProperty({
    enum: StreamDataTypeEnum,
    enumName: 'StreamDataTypeEnum',
    example: StreamDataTypeEnum.TRACE_ID,
  })
  declare type: StreamDataTypeEnum.TRACE_ID;

  @ApiProperty({
    description: 'The trace ID for request tracking',
    example: 'trace-abc123',
  })
  @IsString()
  trace_id: string;
}

/**
 * Stream Text Start DTO - marks the beginning of content streaming
 */
export class StreamTextStartDto extends BaseStreamMessageDto {
  @ApiProperty({
    enum: StreamDataTypeEnum,
    enumName: 'StreamDataTypeEnum',
    example: StreamDataTypeEnum.CONTENT_START,
  })
  declare type: StreamDataTypeEnum.CONTENT_START;
}

/**
 * Stream Text Stop DTO - marks the end of content streaming
 */
export class StreamTextStopDto extends BaseStreamMessageDto {
  @ApiProperty({
    enum: StreamDataTypeEnum,
    enumName: 'StreamDataTypeEnum',
    example: StreamDataTypeEnum.CONTENT_STOP,
  })
  declare type: StreamDataTypeEnum.CONTENT_STOP;
}

/**
 * Stream Status Update DTO - for status updates
 */
export class StreamStatusUpdateDto extends BaseStreamMessageDto {
  @ApiProperty({
    enum: StreamDataTypeEnum,
    enumName: 'StreamDataTypeEnum',
    example: StreamDataTypeEnum.STATUS_UPDATE,
  })
  declare type: StreamDataTypeEnum.STATUS_UPDATE;

  @ApiProperty({
    enum: MessageStatusEnum,
    enumName: 'MessageStatusEnum',
    description: 'The status being updated',
    example: MessageStatusEnum.ING,
  })
  @IsEnum(MessageStatusEnum)
  status: MessageStatusEnum;

  @ApiProperty({
    description: 'Status message describing the current operation',
    example: 'Searching for relevant content',
  })
  @IsString()
  message: string;

  @ApiPropertyOptional({
    description: 'Optional event data',
    example: { tool: 'google_search', progress: 50 },
  })
  @IsOptional()
  @IsObject()
  event?: any;
}

/**
 * Union type for all stream message DTOs
 * This represents the discriminated union of all possible stream message types
 */
export type StreamMessageDto =
  | StreamDataDto
  | StreamChunkedDataDto
  | StreamErrorDto
  | StreamTextDto
  | StreamTraceDto
  | StreamTextStartDto
  | StreamTextStopDto
  | StreamStatusUpdateDto;

/**
 * SSE Event wrapper for stream messages
 * This represents the actual SSE event format sent to clients
 */
export class StreamMessageEventDto {
  @ApiProperty({
    description: 'Event ID for SSE',
    example: '1640995200000',
    required: false,
  })
  @IsOptional()
  @IsString()
  id?: string;

  @ApiProperty({
    description: 'Event type for SSE',
    example: 'stream-message',
    default: 'stream-message',
    required: false,
  })
  @IsOptional()
  @IsString()
  event?: string;

  @ApiProperty({
    description: 'The stream message data',
    oneOf: [
      { $ref: '#/components/schemas/StreamDataDto' },
      { $ref: '#/components/schemas/StreamChunkedDataDto' },
      { $ref: '#/components/schemas/StreamErrorDto' },
      { $ref: '#/components/schemas/StreamTextDto' },
      { $ref: '#/components/schemas/StreamTraceDto' },
      { $ref: '#/components/schemas/StreamTextStartDto' },
      { $ref: '#/components/schemas/StreamTextStopDto' },
      { $ref: '#/components/schemas/StreamStatusUpdateDto' },
    ],
  })
  @ValidateNested()
  @Type(() => Object)
  data: StreamMessageDto;
}

/**
 * Chat Detail V2 DTO - V2 API 聊天详情数据传输对象
 *
 * V2 API 使用现代的 blocks 结构：
 * - Assistant消息使用 blocks 数组来表示内容、推理和工具调用
 * - 不再使用 content, reasoning, events 字段
 * - 更灵活和可扩展的数据结构
 *
 * 这个DTO专门用于V2 API的响应格式化
 */

import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsArray,
  IsEnum,
  IsNumber,
  IsOptional,
  IsString,
  IsUUID,
  MaxLength,
  MinLength,
  ValidateNested,
} from 'class-validator';
import {
  ChatModeEnum,
  CompletionBlockStatusEnum,
  CompletionBlockTypeEnum,
  LLMs,
  MessageModeEnum,
  MessageRoleEnum,
  MessageStatusEnum,
} from '@/common/types';
import {
  AtReferenceDto,
  ChatOriginDto,
  EditCommandDto,
  ShortcutDto,
  UseToolsDto,
} from '../chat.dto';

/**
 * V2 API 完成块基础格式
 */
export class CompletionBlockV2Dto {
  @ApiProperty({ description: 'Block ID' })
  id: string;

  @ApiProperty({ description: 'Creation timestamp' })
  createdAt: Date;

  @ApiProperty({ description: 'Update timestamp' })
  updatedAt: Date;

  @ApiProperty({
    description: 'Block type',
    enum: CompletionBlockTypeEnum,
    enumName: 'CompletionBlockTypeEnum',
  })
  type: CompletionBlockTypeEnum;

  @ApiProperty({
    description: 'Block status',
    enum: CompletionBlockStatusEnum,
    enumName: 'CompletionBlockStatusEnum',
  })
  status: CompletionBlockStatusEnum;

  @ApiProperty({ description: 'Message ID this block belongs to' })
  messageId: string;

  @ApiPropertyOptional({ description: 'Extra metadata' })
  extra?: Record<string, any>;
}

/**
 * V2 API 内容块格式
 */
export class ContentBlockV2Dto extends CompletionBlockV2Dto {
  @ApiProperty({
    description: 'Block type',
    enum: [CompletionBlockTypeEnum.CONTENT],
    enumName: 'CompletionBlockTypeEnum',
  })
  declare type: CompletionBlockTypeEnum.CONTENT;

  @ApiProperty({ description: 'Content data' })
  data: string;
}

/**
 * V2 API 推理块格式
 */
export class ReasoningBlockV2Dto extends CompletionBlockV2Dto {
  @ApiProperty({
    description: 'Block type',
    enum: [CompletionBlockTypeEnum.REASONING],
    enumName: 'CompletionBlockTypeEnum',
  })
  declare type: CompletionBlockTypeEnum.REASONING;

  @ApiProperty({ description: 'Reasoning data' })
  data: string;
}

/**
 * V2 API 工具块格式
 */
export class ToolBlockV2Dto extends CompletionBlockV2Dto {
  @ApiProperty({
    description: 'Block type',
    enum: [CompletionBlockTypeEnum.TOOL],
    enumName: 'CompletionBlockTypeEnum',
  })
  declare type: CompletionBlockTypeEnum.TOOL;

  @ApiProperty({ description: 'Tool ID' })
  toolId: string;

  @ApiProperty({ description: 'Tool name' })
  toolName: string;

  @ApiPropertyOptional({ description: 'Tool arguments' })
  toolArguments?: Record<string, any>;

  @ApiPropertyOptional({ description: 'Tool execution result' })
  toolResult?: Record<string, any>;

  @ApiPropertyOptional({ description: 'Tool response text' })
  toolResponse?: string;

  @ApiPropertyOptional({ description: 'Tool generation time in milliseconds' })
  toolGenerateElapsedMs?: number;

  @ApiPropertyOptional({ description: 'Tool execution time in milliseconds' })
  toolExecuteElapsedMs?: number;
}

/**
 * V2 API 用户消息格式
 */
export class UserMessageV2Dto {
  @ApiProperty({ description: 'Message ID' })
  id: string;

  @ApiProperty({ description: 'Chat ID' })
  chatId: string;

  @ApiProperty({ description: 'Creation timestamp' })
  createdAt: Date;

  @ApiProperty({ description: 'Update timestamp' })
  updatedAt: Date;

  @ApiProperty({
    description: 'Message role',
    enum: [MessageRoleEnum.USER],
    enumName: 'MessageRoleEnum',
  })
  role: MessageRoleEnum.USER;

  @ApiProperty({
    description: 'Message status',
    enum: MessageStatusEnum,
    enumName: 'MessageStatusEnum',
  })
  status: MessageStatusEnum;

  @ApiProperty({ description: 'Message content' })
  content: string;

  @ApiProperty({ description: 'Message origin context' })
  origin: ChatOriginDto;

  @ApiPropertyOptional({ description: 'Selected text context' })
  selection?: string;

  @ApiPropertyOptional({ description: 'Referenced entities' })
  atReferences?: AtReferenceDto[];

  @ApiProperty({ description: 'Board ID if applicable' })
  boardId: string;

  @ApiPropertyOptional({
    description: 'Tools configuration',
    type: [UseToolsDto],
  })
  @Type(() => UseToolsDto)
  tools?: UseToolsDto;

  @ApiPropertyOptional({ description: 'Edit command' })
  command?: EditCommandDto;

  @ApiProperty({
    description: 'Message mode',
    enum: MessageModeEnum,
    enumName: 'MessageModeEnum',
  })
  mode: MessageModeEnum;

  @ApiPropertyOptional({ description: 'Shortcut information', type: [UseToolsDto] })
  @Type(() => ShortcutDto)
  shortcut?: ShortcutDto;
}

/**
 * V2 API 助手消息格式
 * 使用现代的 blocks 结构
 */
export class AssistantMessageV2Dto {
  @ApiProperty({ description: 'Message ID' })
  id: string;

  @ApiProperty({ description: 'Chat ID' })
  chatId: string;

  @ApiProperty({ description: 'Creation timestamp' })
  createdAt: Date;

  @ApiProperty({ description: 'Update timestamp' })
  updatedAt: Date;

  @ApiProperty({
    description: 'Message role',
    enum: [MessageRoleEnum.ASSISTANT],
    enumName: 'MessageRoleEnum',
  })
  role: MessageRoleEnum.ASSISTANT;

  @ApiProperty({
    description: 'Message status',
    enum: MessageStatusEnum,
    enumName: 'MessageStatusEnum',
  })
  status: MessageStatusEnum;

  @ApiProperty({
    description: 'AI model used',
    enum: LLMs,
    enumName: 'LLMs',
  })
  @IsEnum(LLMs)
  model: LLMs;

  @ApiProperty({ description: 'Trace ID for debugging' })
  traceId: string;

  @ApiProperty({
    description: 'Completion blocks containing content, reasoning, and tool calls',
    type: [CompletionBlockV2Dto],
  })
  blocks: CompletionBlockV2Dto[];

  @ApiPropertyOptional({ description: 'Error information if any' })
  error?: Record<string, any>;
}

export type MessageV2Dto = UserMessageV2Dto | AssistantMessageV2Dto;

/**
 * V2 API 聊天详情格式
 */
export class ChatV2Dto {
  @ApiProperty({ description: 'Chat ID' })
  id: string;

  @ApiProperty({ description: 'Creator user ID' })
  creatorId: string;

  @ApiProperty({ description: 'Creation timestamp' })
  createdAt: Date;

  @ApiProperty({ description: 'Update timestamp' })
  updatedAt: Date;

  @ApiProperty({ description: 'Chat title' })
  title: string;

  @ApiProperty({ description: 'Chat origin context' })
  origin: ChatOriginDto;

  @ApiPropertyOptional({ description: 'Board ID if applicable' })
  boardId?: string;

  @ApiProperty({
    description: 'Chat mode',
    enum: ChatModeEnum,
    enumName: 'ChatModeEnum',
  })
  mode: ChatModeEnum;

  @ApiProperty({ description: 'Whether to show new board suggestion' })
  showNewBoardSuggestion: boolean;

  @ApiPropertyOptional({ description: 'New board chat ID if applicable' })
  newBoardChatId?: string;

  @ApiPropertyOptional({ description: 'Associated board IDs' })
  boardIds?: string[];

  @ApiPropertyOptional({ description: 'Board item information' })
  boardItem?: any; // TODO: Define proper BoardItem type
}

/**
 * V2 API 聊天详情格式
 */
export class ChatDetailV2Dto extends ChatV2Dto {
  @ApiProperty({
    description: 'Chat messages',
    type: [Object], // Using Object since we have union types
  })
  messages: MessageV2Dto[];
}

/**
 * V2 API 聊天详情列表响应格式
 */
export class ChatDetailListV2Dto {
  @ApiProperty({
    description: 'List of chat details',
    type: [ChatDetailV2Dto],
  })
  data: ChatDetailV2Dto[];
}

/**
 * V2 API 创建空聊天请求 DTO
 * 对应 youapp 的 CreateChatV2Param
 */
export class CreateEmptyChatDto {
  @ApiProperty({ description: 'Chat origin context', type: ChatOriginDto })
  @ValidateNested()
  @Type(() => ChatOriginDto)
  origin: ChatOriginDto;

  @ApiProperty({ description: 'Board ID where the chat will be created' })
  @IsString()
  @IsUUID()
  boardId: string;

  @ApiProperty({
    description: 'Chat title',
    maxLength: 200,
    example: 'New Chat',
  })
  @IsString()
  @MinLength(1)
  @MaxLength(200)
  title: string;
}

/**
 * V2 API 创建空聊天响应 DTO
 */
export class CreateEmptyChatResponseDto {
  @ApiProperty({ description: 'Created chat details', type: ChatDetailV2Dto })
  @ValidateNested()
  @Type(() => ChatDetailV2Dto)
  chat: ChatDetailV2Dto;
}

/**
 * V2 API 重新生成消息请求 DTO
 * 对应 youapp 的 RegenerateMessageV2Param
 */
export class RegenerateMessageV2Dto {
  @ApiProperty({ description: 'Chat ID containing the message to regenerate' })
  @IsString()
  @IsUUID()
  chatId: string;

  @ApiProperty({ description: 'User message ID to regenerate from' })
  @IsString()
  @IsUUID()
  userMessageId: string;
}

/**
 * V2 API 列出聊天历史请求 DTO
 * 对应 youapp 的 ListHistoryForChatAssistantSchema
 */
export class ListChatHistoryV2Dto {
  @ApiPropertyOptional({
    description: 'Current page number (0-based)',
    minimum: 0,
    default: 0,
  })
  @IsOptional()
  @IsNumber()
  current?: number;

  @ApiPropertyOptional({
    description: 'Page size',
    minimum: 1,
    maximum: 100,
    default: 10,
  })
  @IsOptional()
  @IsNumber()
  pageSize?: number;

  @ApiPropertyOptional({
    description: 'Chat origin filter',
    type: ChatOriginDto,
  })
  @ValidateNested()
  @Type(() => ChatOriginDto)
  @IsOptional()
  origin?: ChatOriginDto;

  @ApiPropertyOptional({
    description: 'Search query for chat titles',
    maxLength: 200,
  })
  @IsOptional()
  @IsString()
  @MaxLength(200)
  query?: string;
}

/**
 * V2 API 列出聊天历史响应 DTO
 */
export class ListChatHistoryV2ResponseDto {
  @ApiProperty({
    description: 'Paginated chat history data',
    type: [ChatV2Dto],
  })
  data: ChatV2Dto[];

  @ApiProperty({
    description: 'Pagination information',
    properties: {
      current: { type: 'number', description: 'Current page (0-based)' },
      pageSize: { type: 'number', description: 'Page size' },
      total: { type: 'number', description: 'Total number of chats' },
    },
  })
  paging: {
    current: number;
    pageSize: number;
    total: number;
  };
}

class BaseSendMessageDto {
  @ApiProperty({ description: 'Message content' })
  @IsString()
  @MinLength(1)
  message: string;

  @ApiPropertyOptional({ description: 'Board Id' })
  @IsString()
  @IsUUID()
  @IsOptional()
  boardId?: string;

  @ApiPropertyOptional({ description: 'Selected text context' })
  @IsString()
  @IsOptional()
  selection?: string;

  @ApiPropertyOptional({ description: 'Referenced entities', type: [AtReferenceDto] })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => AtReferenceDto)
  @IsOptional()
  atReferences?: AtReferenceDto[];

  @ApiPropertyOptional({
    description: 'AI model to use',
    enum: LLMs,
    enumName: 'LLMs',
  })
  @IsEnum(LLMs)
  @IsOptional()
  chatModel?: LLMs;

  @ApiPropertyOptional({ description: 'Tools configuration', type: [UseToolsDto] })
  @Type(() => UseToolsDto)
  @IsOptional()
  tools?: UseToolsDto;

  @ApiPropertyOptional({ description: 'Shortcut information', type: [ShortcutDto] })
  @Type(() => ShortcutDto)
  @IsOptional()
  shortcut?: ShortcutDto;

  @ApiPropertyOptional({
    description: 'Message mode',
    enum: MessageModeEnum,
    enumName: 'MessageModeEnum',
  })
  @IsEnum(MessageModeEnum)
  @IsOptional()
  messageMode?: MessageModeEnum;

  @ApiPropertyOptional({ description: 'Edit command', type: [EditCommandDto] })
  @Type(() => EditCommandDto)
  @IsOptional()
  command?: EditCommandDto;

  @ApiPropertyOptional({ description: 'Chat origin context', type: ChatOriginDto })
  @Type(() => ChatOriginDto)
  @IsOptional()
  origin?: ChatOriginDto;
}

export class CreateChatV2Dto extends BaseSendMessageDto {}

export class SendMessageV2Dto extends BaseSendMessageDto {
  @ApiProperty({ description: 'Chat ID to send message to' })
  @IsString()
  @IsUUID()
  chatId: string;
}

/**
 * V2 API 重新生成消息响应 DTO
 * 返回更新后的聊天详情
 */
export class RegenerateMessageV2ResponseDto {
  @ApiProperty({
    description: 'Updated chat details with regenerated message',
    type: ChatDetailV2Dto,
  })
  @ValidateNested()
  @Type(() => ChatDetailV2Dto)
  chat: ChatDetailV2Dto;
}

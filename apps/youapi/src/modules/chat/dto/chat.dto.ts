import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { LanguageEnum } from '@repo/common';
import { Type } from 'class-transformer';
import { IsEnum, IsOptional, IsString, IsUrl, IsUUID, ValidateNested } from 'class-validator';
import {
  AdjustLengthDirectionEnum,
  ChatOriginTypeEnum,
  EditCommandTypeEnum,
  MessageAtReferenceTypeEnum,
  ToolNames,
  TranslateModeEnum,
} from '@/common/types';

/**
 * Chat Origin DTO - unified class for all origin types
 * This avoids the complexity of discriminated unions in validation
 */
export class ChatOriginDto {
  @ApiProperty({
    enum: ChatOriginTypeEnum,
    description: 'Type of chat origin',
    enumName: 'ChatOriginTypeEnum',
  })
  @IsEnum(ChatOriginTypeEnum)
  type: ChatOriginTypeEnum;

  @ApiPropertyOptional({ description: 'Entity ID (for snip, board, thought origins)' })
  @IsString()
  @IsOptional()
  id?: string;

  @ApiPropertyOptional({ description: 'URL (for webpage origins)' })
  @IsUrl()
  @IsOptional()
  url?: string;

  @ApiPropertyOptional({ description: 'Title (for webpage origins)' })
  @IsString()
  @IsOptional()
  title?: string;

  @ApiPropertyOptional({ description: 'Content (for webpage origins)' })
  @IsString()
  @IsOptional()
  content?: string;

  @ApiPropertyOptional({ description: 'Description (for webpage origins)' })
  @IsString()
  @IsOptional()
  description?: string;
}

/**
 * At Reference DTO - for entity references in messages
 */
export class AtReferenceDto {
  @ApiProperty({ description: 'Reference name displayed in chat' })
  @IsString()
  atName: string;

  @ApiProperty({ description: 'Entity ID being referenced' })
  @IsString()
  entityId: string;

  @ApiProperty({
    description: 'Type of entity being referenced',
    enum: MessageAtReferenceTypeEnum,
    enumName: 'MessageAtReferenceTypeEnum',
  })
  @IsEnum(MessageAtReferenceTypeEnum)
  entityType: MessageAtReferenceTypeEnum;

  @ApiPropertyOptional({ description: 'Content with selection context' })
  @IsString()
  @IsOptional()
  contentWithSelection?: string;
}

/**
 * Tool Options DTO - Base tool options
 */
export class ToolOptionsDto {
  @ApiProperty({ description: 'Tool use option', enum: ['required', 'auto', 'none'] })
  @IsEnum(['required', 'auto', 'none'])
  useTool: 'required' | 'auto' | 'none';
}

/**
 * Image Generate Tool DTO - Image generation tool options
 */
export class ImageGenerateToolDto extends ToolOptionsDto {
  @ApiPropertyOptional({ description: 'Image size', enum: ['square', 'portrait', 'landscape'] })
  @IsEnum(['square', 'portrait', 'landscape'])
  @IsOptional()
  size?: 'square' | 'portrait' | 'landscape';

  @ApiPropertyOptional({
    description: 'Image style',
    enum: ['ghibili', 'pixar', 'cartoon', 'pixel'],
  })
  @IsEnum(['ghibili', 'pixar', 'cartoon', 'pixel'])
  @IsOptional()
  style?: 'ghibili' | 'pixar' | 'cartoon' | 'pixel';

  @ApiPropertyOptional({ description: 'Image quality', enum: ['low', 'medium', 'high'] })
  @IsEnum(['low', 'medium', 'high'])
  @IsOptional()
  quality?: 'low' | 'medium' | 'high';
}

/**
 * Use Tools DTO - Tool usage configuration
 */
export class UseToolsDto {
  @ApiPropertyOptional({ description: 'Image generation tool options', type: ImageGenerateToolDto })
  @ValidateNested()
  @Type(() => ImageGenerateToolDto)
  @IsOptional()
  [ToolNames.IMAGE_GENERATE]?: ImageGenerateToolDto;

  @ApiPropertyOptional({ description: 'Diagram generation tool options', type: ToolOptionsDto })
  @ValidateNested()
  @Type(() => ToolOptionsDto)
  @IsOptional()
  [ToolNames.DIAGRAM_GENERATE]?: ToolOptionsDto;

  @ApiPropertyOptional({ description: 'Google search tool options', type: ToolOptionsDto })
  @ValidateNested()
  @Type(() => ToolOptionsDto)
  @IsOptional()
  [ToolNames.GOOGLE_SEARCH]?: ToolOptionsDto;

  @ApiPropertyOptional({ description: 'Library search tool options', type: ToolOptionsDto })
  @ValidateNested()
  @Type(() => ToolOptionsDto)
  @IsOptional()
  [ToolNames.LIBRARY_SEARCH]?: ToolOptionsDto;

  @ApiPropertyOptional({ description: 'Edit thought tool options', type: ToolOptionsDto })
  @ValidateNested()
  @Type(() => ToolOptionsDto)
  @IsOptional()
  [ToolNames.EDIT_THOUGHT]?: ToolOptionsDto;

  @ApiPropertyOptional({ description: 'Audio generation tool options', type: ToolOptionsDto })
  @ValidateNested()
  @Type(() => ToolOptionsDto)
  @IsOptional()
  [ToolNames.AUDIO_GENERATE]?: ToolOptionsDto;

  @ApiPropertyOptional({ description: 'Board search tool options', type: ToolOptionsDto })
  @ValidateNested()
  @Type(() => ToolOptionsDto)
  @IsOptional()
  [ToolNames.BOARD_SEARCH]?: ToolOptionsDto;
}

/**
 * Edit Command DTO - simplified to avoid complex discriminated unions
 */
export class EditCommandDto {
  @ApiProperty({
    description: 'Command type',
    enum: EditCommandTypeEnum,
    enumName: 'EditCommandTypeEnum',
  })
  @IsEnum(EditCommandTypeEnum)
  type: EditCommandTypeEnum;

  @ApiPropertyOptional({
    description: 'Direction for adjust length command',
    enum: AdjustLengthDirectionEnum,
    enumName: 'AdjustLengthDirectionEnum',
  })
  @IsEnum(AdjustLengthDirectionEnum)
  @IsOptional()
  direction?: AdjustLengthDirectionEnum;

  @ApiPropertyOptional({
    description: 'Target language for translation',
    enum: LanguageEnum,
    enumName: 'LanguageEnum',
  })
  @IsEnum(LanguageEnum)
  @IsOptional()
  targetLanguage?: LanguageEnum;

  @ApiPropertyOptional({
    description: 'Translation mode',
    enum: TranslateModeEnum,
    enumName: 'TranslateModeEnum',
  })
  @IsEnum(TranslateModeEnum)
  @IsOptional()
  mode?: TranslateModeEnum;
}

/**
 * Shortcut DTO
 */
export class ShortcutDto {
  @ApiProperty({ description: 'Shortcut ID' })
  @IsString()
  @IsUUID()
  id: string;

  @ApiProperty({ description: 'Shortcut name' })
  @IsString()
  name: string;
}

/**
 * Snip ID DTO - For operations that require a snip ID
 */
export class SnipIdDto {
  @ApiProperty({ description: 'Snip ID' })
  @IsString()
  @IsUUID()
  snipId: string;
}

/**
 * Chat ID DTO
 */
export class ChatIdDto {
  @ApiProperty({ description: 'Chat ID to get details for' })
  @IsString()
  @IsUUID()
  chatId: string;
}

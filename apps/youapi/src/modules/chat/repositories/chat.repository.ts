/**
 * Chat Repository - 聊天仓储接口
 * 提供聊天聚合的数据访问抽象，使用领域实体
 *
 * 遵循 DDD 原则，隔离领域层和数据层
 *
 * 性能优化:
 * - 移除查询中的窗口函数以提升性能
 * - 使用 chats.updatedAt 排序而非 MAX() OVER PARTITION BY
 *
 * 未来优化建议:
 * - 考虑在 chats 表中添加去规范化的 lastActivityAt 字段
 * - 通过触发器在 messages/completion_blocks 修改时更新该字段
 * - 这将使按活动时间排序变得极其快速
 */

import { Injectable } from '@nestjs/common';
import { and, asc, desc, eq, getTableColumns, inArray, isNull, sql } from 'drizzle-orm';
import { NotFound, ResourceEnum } from '@/common/errors';
import { ChatModeEnum, type ChatOrigin, ChatOriginTypeEnum } from '@/common/types';
import { DatabaseService } from '@/shared/db/database.service';
import { chats, completionBlocks, messages } from '@/shared/db/public.schema';
import type { CompletionBlockDO } from '../../ai/domain/models/completion-block.entity';
import { Chat, type ChatDO } from '../domain/chat/models/chat.entity';
import type { MessageDO } from '../domain/message/models/message.entity';
import { MessageRepository } from './message.repository';

export interface PagingRequest {
  limit?: number;
  offset?: number;
  order?: 'asc' | 'desc';
}

export interface PagingResponse<T> {
  data: T[];
  total: number;
  limit: number;
  offset: number;
}

@Injectable()
export class ChatRepository {
  constructor(
    private readonly databaseService: DatabaseService,
    private readonly messageRepository: MessageRepository,
  ) {}

  /**
   * 根据ID查找聊天（不包含消息数据）
   * 资源可能不存在，返回 null
   */
  async findById(id: string): Promise<Chat | null> {
    const chatData = await this.databaseService.db
      .select()
      .from(chats)
      .where(and(eq(chats.id, id), isNull(chats.deletedAt)))
      .limit(1);

    if (!chatData?.length) {
      return null;
    }

    return Chat.fromDO(chatData[0] as ChatDO);
  }

  /**
   * 根据ID查找完整的聊天详情的原始数据（包含消息和完成块）
   * 使用高效的三表JOIN查询，返回原始JOIN结果供上层聚合
   * 遵循DDD原则：Repository只负责数据访问，不进行业务逻辑处理
   */
  async findDetailRawDataById(id: string): Promise<Array<{
    chats: ChatDO;
    messages: MessageDO | null;
    blocks: CompletionBlockDO | null;
  }> | null> {
    // 执行三表LEFT JOIN查询，类似youapp的selectDetailById实现
    const result = await this.databaseService.db
      .select({
        chats: chats,
        messages: messages,
        blocks: completionBlocks,
      })
      .from(chats)
      .leftJoin(messages, and(eq(messages.chatId, chats.id), isNull(messages.deletedAt)))
      .leftJoin(
        completionBlocks,
        and(eq(completionBlocks.messageId, messages.id), isNull(completionBlocks.deletedAt)),
      )
      .where(and(eq(chats.id, id), isNull(chats.deletedAt)))
      .orderBy(messages.createdAt, completionBlocks.createdAt, chats.createdAt);

    if (!result || result.length === 0) {
      return null;
    }

    return result as Array<{
      chats: ChatDO;
      messages: MessageDO | null;
      blocks: CompletionBlockDO | null;
    }>;
  }

  /**
   * 根据ID查找聊天
   * 资源不存在时抛异常
   */
  async getById(id: string): Promise<Chat> {
    const chat = await this.findById(id);
    if (!chat) {
      throw new NotFound({
        resource: ResourceEnum.CHAT,
        id,
      });
    }

    return chat;
  }

  /**
   * 保存聊天
   * 如果聊天是新的，则插入，否则更新
   */
  async save(chat: Chat): Promise<Chat> {
    if (chat.isNew) {
      const chatData = chat.toDO();
      const result = await this.databaseService.db.insert(chats).values(chatData).returning();
      return Chat.fromDO(result[0] as ChatDO);
    }

    await this.databaseService.db.update(chats).set(chat.toDO()).where(eq(chats.id, chat.id));
    return chat;
  }

  /**
   * 删除聊天
   * 设置 deletedAt 字段为当前时间
   */
  async delete(chat: Chat): Promise<void> {
    // 软删除聊天 - 设置删除时间
    const now = new Date();
    await this.databaseService.db
      .update(chats)
      .set({
        deletedAt: now,
        updatedAt: now,
      } as ChatDO)
      .where(eq(chats.id, chat.id));

    // 软删除关联的消息
    await this.messageRepository.deleteByChatId(chat.id);
  }

  /**
   * 根据来源查询聊天详情的原始数据
   * 查询与特定来源关联的所有聊天，包含完整的消息和完成块数据
   *
   * Migrated from:
   * - /Users/<USER>/Projects/github.com/YouMindInc/youapp/src/lib/domain/chat/query/index.ts (queryDetailsByOrigin)
   */
  async findDetailsByOriginRawData(
    origin: ChatOrigin,
    userId: string,
  ): Promise<
    Array<{
      chats: ChatDO;
      messages: MessageDO | null;
      blocks: CompletionBlockDO | null;
    }>
  > {
    // 对应youapp的mode过滤：[ChatModeEnum.CHAT, ChatModeEnum.NEW_BOARD]
    const allowedModes = [ChatModeEnum.CHAT, ChatModeEnum.NEW_BOARD];

    const whereConditions = [
      isNull(chats.deletedAt), // 未删除的聊天
      eq(chats.creatorId, userId), // 用户创建的聊天
      eq(chats.originType, origin.type), // 匹配来源类型
      inArray(chats.mode, allowedModes), // 只查询chat和new_board模式的聊天
    ];

    // 根据来源类型添加特定的过滤条件
    if (origin.type === ChatOriginTypeEnum.WEBPAGE && 'url' in origin) {
      whereConditions.push(eq(chats.originUrl, origin.url));
    } else if (
      (origin.type === ChatOriginTypeEnum.SNIP ||
        origin.type === ChatOriginTypeEnum.BOARD ||
        origin.type === ChatOriginTypeEnum.THOUGHT) &&
      'id' in origin
    ) {
      whereConditions.push(eq(chats.originId, origin.id));
    }

    // 方法1: 使用子查询获取每个聊天的最新活动时间，然后JOIN获取完整数据
    // 这样避免了窗口函数，同时保持正确的业务逻辑

    // 先获取每个聊天的最新 completion block 更新时间
    const chatActivitySubquery = this.databaseService.db
      .select({
        chatId: chats.id,
        latestActivity: sql<Date>`COALESCE(
          MAX(${completionBlocks.updatedAt}), 
          MAX(${messages.createdAt}), 
          ${chats.updatedAt}
        )`.as('latest_activity'),
      })
      .from(chats)
      .leftJoin(messages, and(eq(messages.chatId, chats.id), isNull(messages.deletedAt)))
      .leftJoin(
        completionBlocks,
        and(eq(completionBlocks.messageId, messages.id), isNull(completionBlocks.deletedAt)),
      )
      .where(and(...whereConditions))
      .groupBy(chats.id)
      .as('chat_activity');

    // 然后获取完整数据，按最新活动时间排序
    const query = this.databaseService.db
      .select({
        chats: chats,
        messages: messages,
        blocks: completionBlocks,
      })
      .from(chats)
      .innerJoin(chatActivitySubquery, eq(chats.id, chatActivitySubquery.chatId))
      .leftJoin(messages, and(eq(messages.chatId, chats.id), isNull(messages.deletedAt)))
      .leftJoin(
        completionBlocks,
        and(eq(completionBlocks.messageId, messages.id), isNull(completionBlocks.deletedAt)),
      )
      .orderBy(
        // 按最新completion block活动时间倒序（保持原有业务逻辑）
        desc(chatActivitySubquery.latestActivity),
        // 使用聊天 ID 作为分界点
        desc(chats.id),
        // 聊天内消息按时间升序
        asc(messages.createdAt),
        // 完成块按时间升序
        asc(completionBlocks.createdAt),
      );

    const result = await query;

    return result as Array<{
      chats: ChatDO;
      messages: MessageDO | null;
      blocks: CompletionBlockDO | null;
    }>;
  }

  /**
   * 列出用户的聊天历史记录（简化版本，不包含完整消息内容）
   * 支持分页、按来源筛选和标题搜索
   *
   * Migrated from:
   * - /Users/<USER>/Projects/github.com/YouMindInc/youapp/src/lib/dao/chat/index.ts (listForChatAssistant)
   */
  async listChatHistory({
    userId,
    boardId,
    origin,
    searchQuery,
    pageSize = 10,
    offset = 0,
  }: {
    userId: string;
    boardId?: string;
    origin?: ChatOrigin;
    searchQuery?: string;
    pageSize: number;
    offset: number;
  }): Promise<{ chats: Chat[]; total: number }> {
    // 基础过滤条件 - 对应youapp的基础过滤
    const whereConditions = [
      isNull(chats.deletedAt), // 未删除
      eq(chats.creatorId, userId), // 用户自己的聊天
      inArray(chats.mode, [ChatModeEnum.CHAT, ChatModeEnum.NEW_BOARD]), // 只查询chat和new_board模式
    ];

    // Board ID 过滤 - 对应youapp的board_id过滤
    if (boardId) {
      whereConditions.push(eq(chats.boardId, boardId));
    }

    // Origin 过滤 - 对应youapp的origin过滤逻辑
    if (origin && !boardId) {
      whereConditions.push(eq(chats.originType, origin.type));

      if (origin.type === ChatOriginTypeEnum.WEBPAGE && 'url' in origin) {
        whereConditions.push(eq(chats.originUrl, origin.url));
      } else if (
        (origin.type === ChatOriginTypeEnum.SNIP ||
          origin.type === ChatOriginTypeEnum.BOARD ||
          origin.type === ChatOriginTypeEnum.THOUGHT) &&
        'id' in origin
      ) {
        if (origin.id) {
          whereConditions.push(eq(chats.originId, origin.id));
        } else {
          whereConditions.push(isNull(chats.originId));
        }
      }
    }

    // 搜索查询 - 对应youapp的title搜索
    if (searchQuery) {
      whereConditions.push(sql`${chats.title} ILIKE ${'%' + searchQuery + '%'}`);
    }

    // 构建查询 - 优化版本，移除了窗口函数
    const baseQuery = this.databaseService.db
      .select({
        chat: getTableColumns(chats),
        latestMessageTime: sql<Date>`MAX(${messages.createdAt})`.as('latest_message_time'),
      })
      .from(chats)
      .leftJoin(messages, and(eq(messages.chatId, chats.id), isNull(messages.deletedAt)))
      .where(and(...whereConditions))
      .groupBy(chats.id)
      .orderBy(
        // 按最新消息时间倒序，如果没有消息则按聊天更新时间
        desc(sql`COALESCE(MAX(${messages.createdAt}), ${chats.updatedAt})`),
        // 按聊天 ID 作为分界点
        desc(chats.id),
      );

    // 获取总数
    const countQuery = this.databaseService.db
      .select({ count: sql<number>`count(DISTINCT ${chats.id})`.as('count') })
      .from(chats)
      .leftJoin(messages, and(eq(messages.chatId, chats.id), isNull(messages.deletedAt)))
      .where(and(...whereConditions));

    // 执行查询
    const [chatsResult, countResult] = await Promise.all([
      baseQuery.limit(pageSize).offset(offset),
      countQuery,
    ]);

    const total = countResult[0]?.count || 0;

    return {
      chats: chatsResult.map((row) => Chat.fromDO(row.chat as ChatDO)),
      total,
    };
  }

  async selectNewBoardChatIdByBoardId(boardId: string): Promise<string | null> {
    const result = await this.databaseService.db
      .select()
      .from(chats)
      .where(
        and(
          isNull(chats.deletedAt),
          eq(chats.boardId, boardId),
          eq(chats.mode, ChatModeEnum.NEW_BOARD),
        ),
      );

    return result[0]?.id ?? null;
  }
}

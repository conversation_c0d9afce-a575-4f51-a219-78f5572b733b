/**
 * 消息仓储 - 聊天消息仓储接口
 * 提供聊天消息的领域实体数据访问抽象
 *
 * 遵循 DDD 原则，隔离领域层和数据层
 */

import { Injectable } from '@nestjs/common';
import { and, eq, gt, isNull, lt, SQL } from 'drizzle-orm';
import { NotFound, ResourceEnum } from '@/common/errors';
import { DatabaseService } from '@/shared/db/database.service';
import { messages } from '@/shared/db/public.schema';
import { Message, MessageDO } from '../domain/message/models/message.entity';
import { LegacyCompatibilityService } from '../services/legacy-compatibility.service';

@Injectable()
export class MessageRepository {
  constructor(
    private readonly databaseService: DatabaseService,
    private readonly legacyCompatibilityService: LegacyCompatibilityService,
  ) {}

  /**
   * 根据ID查找消息，不存在返回 null
   */
  async findById(id: string): Promise<Message | null> {
    const messageData = await this.databaseService.db
      .select()
      .from(messages)
      .where(and(eq(messages.id, id), isNull(messages.deletedAt)))
      .limit(1);

    if (!messageData?.length) {
      return null;
    }

    return this.legacyCompatibilityService.createMessageFromDO(messageData[0] as MessageDO);
  }

  /**
   * 根据ID获取消息，不存在则抛出异常
   */
  async getById(id: string): Promise<Message> {
    const message = await this.findById(id);
    if (!message) {
      throw new NotFound({
        resource: ResourceEnum.MESSAGE,
        id,
      });
    }
    return message;
  }

  /**
   * 保存消息，若为新消息则插入，否则更新
   */
  async save(message: Message): Promise<Message> {
    if (message.isNew) {
      const messageData = message.toDO();
      // 插入新消息
      const result = await this.databaseService.db.insert(messages).values(messageData).returning();

      return this.legacyCompatibilityService.createMessageFromDO(result[0] as MessageDO);
    }

    const messageData = this.legacyCompatibilityService.convertDOFromMessage(message);

    // 更新消息
    await this.databaseService.db
      .update(messages)
      .set(messageData)
      .where(eq(messages.id, message.id));
    return message;
  }

  /**
   * 根据聊天ID查找消息列表
   */
  async findByChatId(chatId: string): Promise<Message[]> {
    const messageData = await this.databaseService.db
      .select()
      .from(messages)
      .where(and(eq(messages.chatId, chatId), isNull(messages.deletedAt)))
      .orderBy(messages.createdAt);

    return messageData.map((data) =>
      this.legacyCompatibilityService.createMessageFromDO(data as MessageDO),
    );
  }

  /**
   * 重新生成时删除中间消息
   */
  async deleteByRegenerate(param: {
    chatId: string;
    userMessageId: string;
    regeneratedMessageId: string;
  }) {
    const { chatId, userMessageId, regeneratedMessageId } = param;
    // delete messages between user_message and regenerated_message
    // exclusive of these messages
    await this.databaseService.db
      .update(messages)
      .set({
        deletedAt: new Date(),
      } as MessageDO)
      .where(
        and(
          eq(messages.chatId, chatId),
          gt(messages.id, userMessageId),
          lt(messages.id, regeneratedMessageId),
        ) as SQL,
      );
  }

  /**
   * 根据聊天ID删除消息
   */
  async deleteByChatId(chatId: string): Promise<void> {
    await this.databaseService.db
      .update(messages)
      .set({ deletedAt: new Date() } as MessageDO)
      .where(and(eq(messages.chatId, chatId), isNull(messages.deletedAt)))
      .returning();
  }
}

/**
 * Legacy Compatibility Service - 传统格式兼容性服务
 *
 * 专门处理传统消息格式到现代blocks格式的转换
 * 位于chat模块内部，避免跨模块依赖
 *
 * MIGRATION CONTEXT:
 * - Legacy format: messages stored content/reasoning as separate fields, events in context
 * - Modern format: everything stored as CompletionBlocks with types (CONTENT, REASONING, TOOL)
 *
 * CONVERSION PROCESS:
 * 1. Convert reasoning field → ReasoningCompletionBlock
 * 2. Convert content field → ContentCompletionBlock
 * 3. Extract events from context[].event → ToolCompletionBlocks
 * 4. Handle tool event conversion based on event type
 *
 * DOMAIN DESIGN:
 * - Placed in chat module to avoid external dependencies
 * - Keeps DDD domain model clean by handling legacy concerns in service layer
 *
 * ERROR HANDLING:
 * - Individual event conversion failures are logged but don't stop processing
 * - Ensures partial data can still be recovered from legacy format
 */

import { Injectable, Logger } from '@nestjs/common';
import { uuidv7 } from 'uuidv7';
import z from 'zod';
import {
  ChatModeEnum,
  ChatOriginTypeEnum,
  CompletionBlockStatusEnum,
  CompletionBlockTypeEnum,
  DEFAULT_CHAT_TITLE,
  type InternetSearchResult,
  type MessageContext,
  MessageContextEnum,
  MessageCreateSnipByUrlEventSchema,
  type MessageEvent,
  MessageEventTypeEnum,
  MessageGenerateImageSchema,
  MessageGenerateSVGSchema,
  MessageModeEnum,
  MessageRoleEnum,
  MessageStatusEnum,
  SearchResultTypeEnum,
  ToolNames,
} from '@/common/types';
import { camelToSnake, snakeToCamel } from '@/common/utils';
import {
  CompletionBlock,
  ContentCompletionBlock,
  ReasoningCompletionBlock,
  ToolCompletionBlock,
} from '../../ai/domain/models/completion-block.entity';
import { Chat, ChatDO } from '../domain/chat/models/chat.entity';
import {
  AssistantMessage,
  Message,
  MessageDO,
  UserMessage,
} from '../domain/message/models/message.entity';
import { ChatOriginDto } from '../dto/chat.dto';
import {
  AssistantMessageV1Dto,
  ChatDetailV1Dto,
  MessageV1Dto,
  UserMessageV1Dto,
} from '../dto/v1/chat-v1.dto';
import {
  AssistantMessageV2Dto,
  ChatDetailV2Dto,
  CompletionBlockV2Dto,
  ContentBlockV2Dto,
  MessageV2Dto,
  ReasoningBlockV2Dto,
  ToolBlockV2Dto,
  UserMessageV2Dto,
} from '../dto/v2/chat-v2.dto';

@Injectable()
export class LegacyCompatibilityService {
  private readonly logger = new Logger(LegacyCompatibilityService.name);

  /*** Chat V1 DTO <-> Chat Conversion ***/
  convertChatV1DtoFromChat(chat: Chat): ChatDetailV1Dto {
    this.logger.debug(`Assembling chat ${chat.id} to V1 ChatDetail format`);

    const chatDetailV1: ChatDetailV1Dto = {
      id: chat.id,
      creatorId: chat.creatorId,
      createdAt: chat.createdAt,
      updatedAt: chat.updatedAt,
      title: chat.title || DEFAULT_CHAT_TITLE,
      origin: chat.origin as ChatOriginDto,
      boardId: chat.boardId,
      messages: [],
      // TODO: 从board服务获取这些信息
      // 跟某木聊了下先下掉，回头按需加回来
      boardIds: [],
      boardItem: undefined,
    };
    if (chat.messages) {
      for (const message of chat.messages) {
        const messageDto = this.convertMessageToV1Dto(message);
        chatDetailV1.messages.push(messageDto);
      }
    }

    return chatDetailV1;
  }

  createChatFromV1Dto(dto: ChatDetailV1Dto): Chat {
    const chat = new Chat(
      {
        id: dto.id,
        createdAt: dto.createdAt,
        updatedAt: dto.updatedAt,
        deletedAt: null,
        creatorId: dto.creatorId,
        mode: ChatModeEnum.CHAT,
        title: dto.title || DEFAULT_CHAT_TITLE,
        origin: dto.origin as ChatOriginDto,
        boardId: dto.origin?.type === ChatOriginTypeEnum.BOARD ? dto.origin?.id : null,
      },
      !!dto.id,
    );

    return chat;
  }

  /*** Chat V2 DTO <-> Chat Conversion ***/
  convertChatV2DtoFromChat(chat: Chat): ChatDetailV2Dto {
    this.logger.debug(`Assembling chat ${chat.id} to V2 ChatDetail format`);

    const chatDetailV2: ChatDetailV2Dto = {
      id: chat.id,
      creatorId: chat.creatorId,
      createdAt: chat.createdAt,
      updatedAt: chat.updatedAt,
      title: chat.title || DEFAULT_CHAT_TITLE,
      origin: chat.origin as ChatOriginDto,
      boardId: chat.boardId || undefined,
      mode: chat.mode,
      showNewBoardSuggestion: chat.showNewBoardSuggestion,
      // newBoardChatId 不存在于 Chat 实体中，可能需要从其他地方获取
      newBoardChatId: undefined,
      messages: [],
      // TODO: 从board服务获取这些信息
      boardIds: chat.boardId ? [chat.boardId] : [],
      boardItem: undefined,
    };
    if (chat.messages) {
      for (const message of chat.messages) {
        const messageDto = this.convertMessageToV2Dto(message);
        chatDetailV2.messages.push(messageDto);
      }
    }

    return chatDetailV2;
  }

  createChatFromV2Dto(dto: ChatDetailV2Dto): Chat {
    const chat = new Chat(
      {
        id: dto.id,
        createdAt: dto.createdAt,
        updatedAt: dto.updatedAt,
        deletedAt: null,
        creatorId: dto.creatorId,
        mode: ChatModeEnum.CHAT,
        title: dto.title || DEFAULT_CHAT_TITLE,
        origin: dto.origin as ChatOriginDto,
        boardId:
          dto.boardId || dto.origin?.type === ChatOriginTypeEnum.BOARD ? dto.origin?.id : null,
      },
      !!dto.id,
    );

    return chat;
  }

  /*** Chat DO <-> Chat Conversion ***/
  createChatFromDO(data: ChatDO): Chat {
    const chat = Chat.fromDO(data);
    if (data.messages) {
      for (const message of data.messages) {
        const messageEntity = this.createMessageFromDO(message);
        chat.addMessage(messageEntity);
      }
    }
    return chat;
  }

  /*** Legacy Message Check ***/
  isLegacyMessage(message: Message): boolean {
    if (message instanceof AssistantMessage) {
      return message.blocks?.some((b) => b.id?.startsWith('legacy-'));
    }
    return false;
  }

  isLegacyMessageDO(data: MessageDO): boolean {
    if (data.reasoning) {
      return true;
    }
    if (data.context?.some((c) => c.type === MessageContextEnum.EVENT)) {
      return true;
    }
    return false;
  }

  /*** User Message V1 DTO <-> User Message Conversion ***/
  convertUserMessageV1DtoFromUserMessage(message: UserMessage): UserMessageV1Dto {
    return {
      id: message.id,
      chatId: message.chatId,
      createdAt: message.createdAt,
      updatedAt: message.updatedAt,
      role: message.role as MessageRoleEnum.USER,
      status: message.status,
      content: message.content || '',
      origin: message.origin as ChatOriginDto,
      selection: message.selection,
    };
  }

  createUserMessageFromV1Dto(dto: UserMessageV1Dto): UserMessage {
    return new UserMessage(
      {
        id: dto.id,
        chatId: dto.chatId,
        createdAt: dto.createdAt,
        updatedAt: dto.updatedAt,
        deletedAt: null,
        role: MessageRoleEnum.USER,
        status: dto.status,
        origin: dto.origin as ChatOriginDto,
        content: dto.content || '',
        selection: dto.selection,
        atReferences: [],
        boardId: null,
        mode: MessageModeEnum.ASK,
        tools: null,
        command: null,
        shortcut: null,
      },
      !!dto.id,
    );
  }

  /*** User Message V2 DTO <-> User Message Conversion ***/
  convertUserMessageV2DtoFromUserMessage(message: UserMessage): UserMessageV2Dto {
    return {
      id: message.id,
      chatId: message.chatId,
      createdAt: message.createdAt,
      updatedAt: message.updatedAt,
      role: message.role as MessageRoleEnum.USER,
      status: message.status,
      content: message.content || '',
      origin: message.origin,
      selection: message.selection,
      boardId: message.boardId,
      mode: message.mode,
      atReferences: message.atReferences,
      tools: message.tools,
      command: message.command,
      shortcut: message.shortcut,
    };
  }

  createUserMessageFromV2Dto(dto: UserMessageV2Dto): UserMessage {
    return new UserMessage(
      {
        id: dto.id,
        chatId: dto.chatId,
        createdAt: dto.createdAt,
        updatedAt: dto.updatedAt,
        deletedAt: null,
        role: MessageRoleEnum.USER,
        status: dto.status,
        origin: dto.origin,
        content: dto.content || '',
        selection: dto.selection,
        atReferences: dto.atReferences,
        boardId: dto.boardId,
        mode: dto.mode,
        tools: dto.tools,
        command: dto.command,
        shortcut: dto.shortcut,
      },
      !!dto.id,
    );
  }

  /*** User Message DO <-> User Message Conversion ***/
  convertDOFromUserMessage(message: UserMessage): MessageDO {
    const context: MessageContext[] = [
      {
        type: MessageContextEnum.ORIGIN,
        origin: camelToSnake(message.origin),
      },
    ];
    if (message.selection) {
      context.push({
        type: MessageContextEnum.SELECTION,
        text: message.selection,
      });
    }
    if (message.atReferences.length) {
      context.push({
        type: MessageContextEnum.AT,
        at_references: camelToSnake(message.atReferences),
      });
    }

    return {
      id: message.id,
      chatId: message.chatId,
      createdAt: message.createdAt,
      updatedAt: message.updatedAt,
      deletedAt: message.deletedAt,
      role: message.role,
      status: message.status,
      model: message.model,
      context,
      content: message.content,
      tools: message.tools,
      mode: message.mode,
      command: camelToSnake(message.command),
      shortcut: camelToSnake(message.shortcut),
      error: null,
      traceId: null,
      reasoning: null,
    };
  }

  createUserMessageFromDO(data: MessageDO): UserMessage {
    const origin = data.context?.find((c) => c.type === MessageContextEnum.ORIGIN)?.origin || {};
    const selection =
      data.context?.find((c) => c.type === MessageContextEnum.SELECTION)?.text || '';
    const atReferences =
      data.context?.find((c) => c.type === MessageContextEnum.AT)?.at_references || [];
    const boardId =
      data.context?.find((c) => c.type === MessageContextEnum.BOARD_ID)?.board_id || '';

    return new UserMessage(
      {
        ...data,
        origin: snakeToCamel(origin),
        content: data.content || '',
        boardId,
        selection,
        atReferences: snakeToCamel(atReferences),
        mode: data.mode ?? MessageModeEnum.ASK,
        tools: data.tools || {},
        command: data.command ?? undefined,
        shortcut: data.shortcut ?? undefined,
      },
      !!data.id,
    );
  }

  /*** Assistant Message V1 DTO <-> Assistant Message Conversion ***/
  private convertToolBlockToMessageEvent(block: ToolCompletionBlock): MessageEvent {
    switch (block.toolName) {
      case ToolNames.GOOGLE_SEARCH:
        return {
          type: MessageEventTypeEnum.SEARCH,
          query: (block.toolArguments?.query as string) || '',
          results:
            (block.toolResult?.results as InternetSearchResult[])?.map(
              (r: InternetSearchResult) => ({
                entity_type: SearchResultTypeEnum.INTERNET_SEARCH_RESULT,
                entity_vo: r,
              }),
            ) || [],
          time_of_action: block.updatedAt.toISOString(),
        };

      case ToolNames.BOARD_SEARCH:
      case ToolNames.LIBRARY_SEARCH:
      case 'resolve_library':
        return {
          type: MessageEventTypeEnum.RESOLVE_LIBRARY,
          query: (block.toolArguments?.query as string) || '',
          results: (block.toolResult?.results as InternetSearchResult[]) || [],
          time_of_action: block.updatedAt.toISOString(),
        };

      case ToolNames.IMAGE_GENERATE:
        return {
          type: MessageEventTypeEnum.GENERATE_IMAGE,
          results: [],
          image_urls: (block.toolResult as z.infer<typeof MessageGenerateImageSchema>)?.image_urls,
          time_of_action: block.updatedAt.toISOString(),
        };
      case ToolNames.DIAGRAM_GENERATE: {
        const toolResult = (block.toolResult as z.infer<typeof MessageGenerateSVGSchema>) || {};
        return {
          type: MessageEventTypeEnum.GENERATE_SVG,
          results: [],
          svg: toolResult?.svg,
          image_url: toolResult?.image_url,
          time_of_action: block.updatedAt.toISOString(),
        };
      }
      case ToolNames.CREATE_SNIP_BY_URL: {
        const toolResult =
          (block.toolResult as z.infer<typeof MessageCreateSnipByUrlEventSchema>) || {};
        return {
          type: MessageEventTypeEnum.CREATE_SNIP_BY_URL,
          time_of_action: block.updatedAt.toISOString(),
          snipsResults: toolResult?.snipsResults || [],
          summary_message: toolResult?.summary_message || '',
        };
      }
      default:
        return { results: [] } as unknown as MessageEvent;
    }
  }

  private extractV1FieldsFromBlocks(blocks: CompletionBlock[]): {
    content: string;
    reasoning?: string;
    events?: MessageEvent[];
    reasoningTimestamps: { begin?: string; end?: string };
  } {
    let content = '';
    let reasoning: string | undefined;
    const events: MessageEvent[] = [];
    const reasoningTimestamps: { begin?: string; end?: string } = {};

    for (const block of blocks) {
      switch (block.type) {
        case CompletionBlockTypeEnum.CONTENT:
          // 合并所有content blocks
          if (block.data) {
            content += block.data;
          }
          break;

        case CompletionBlockTypeEnum.REASONING:
          // 合并所有reasoning blocks
          if (block.data) {
            reasoning = (reasoning || '') + block.data;
            // 记录推理时间戳
            if (!reasoningTimestamps.begin) {
              reasoningTimestamps.begin = block.createdAt.toISOString();
            }
            reasoningTimestamps.end = block.updatedAt.toISOString();
          }
          break;

        case CompletionBlockTypeEnum.TOOL:
          // 将tool blocks转换为events
          events.push(this.convertToolBlockToMessageEvent(block as ToolCompletionBlock));
          break;
      }
    }

    return {
      content: content || '',
      reasoning: reasoning || '',
      events: events,
      reasoningTimestamps,
    };
  }

  convertAssistantMessageV1DtoFromAssistantMessage(
    message: AssistantMessage,
  ): AssistantMessageV1Dto {
    const { content, reasoning, events, reasoningTimestamps } = this.extractV1FieldsFromBlocks(
      message.blocks || [],
    );
    return {
      id: message.id,
      chatId: message.chatId,
      createdAt: message.createdAt,
      updatedAt: message.updatedAt,
      role: message.role as MessageRoleEnum.ASSISTANT,
      status: message.status,
      model: message.model,
      traceId: message.traceId,
      content: content,
      reasoning: reasoning,
      events: events,
      reasoningBeginAt: reasoningTimestamps.begin,
      reasoningEndAt: reasoningTimestamps.end,
    };
  }

  /**
   * 将单个事件转换为工具块
   */
  private convertEventToToolBlock(
    event: MessageEvent,
    messageId: string,
    index: number,
    messageCreatedAt: Date,
    messageUpdatedAt: Date,
  ): ToolCompletionBlock {
    const baseBlock = {
      id: `legacy-event-${messageId}-${index}`,
      data: null,
      createdAt: messageCreatedAt,
      updatedAt: messageUpdatedAt,
      deletedAt: null,
      messageId,
      status: CompletionBlockStatusEnum.DONE,
      toolGenerateElapsedMs: 0,
      toolExecuteElapsedMs: 0,
      extra: {
        migratedFromLegacy: true,
        originalEvent: event,
      },
    };

    // Convert based on event type
    if (event.type === MessageEventTypeEnum.SEARCH) {
      const isWebSearch = event.results?.some(
        (r) => r.entity_type === SearchResultTypeEnum.INTERNET_SEARCH_RESULT,
      );

      if (isWebSearch) {
        const actualResults = event.results?.map((r) => r.entity_vo);
        return new ToolCompletionBlock({
          ...baseBlock,
          toolId: uuidv7(),
          toolName: ToolNames.GOOGLE_SEARCH,
          toolArguments: {
            query: event.query,
            type: 'webpage',
          },
          toolResult: {
            results: actualResults || [],
          },
          toolResponse: JSON.stringify({ results: actualResults }),
        });
      } else {
        const actualResults = event.results?.map((r) => ({
          entity_id: r.entity_id,
          entity_type: r.entity_type,
          related_chunk: r.related_chunk,
        }));
        return new ToolCompletionBlock({
          ...baseBlock,
          toolId: uuidv7(),
          toolName: ToolNames.LIBRARY_SEARCH,
          toolArguments: {
            query: event.query,
          },
          toolResult: { results: actualResults || [] },
          toolResponse: JSON.stringify({ results: actualResults }),
        });
      }
    }

    if (event.type === MessageEventTypeEnum.RESOLVE_LIBRARY) {
      const actualResults = event.results?.map((r) => ({
        entity_id: r.entity_id,
        entity_type: r.entity_type,
        related_chunk: r.related_chunk,
      }));
      return new ToolCompletionBlock({
        ...baseBlock,
        toolId: uuidv7(),
        toolName: ToolNames.LIBRARY_SEARCH,
        toolArguments: {
          query: event.query,
        },
        toolResult: { results: actualResults || [] },
        toolResponse: JSON.stringify({ results: actualResults }),
      });
    }
  }

  createAssistantMessageFromV1Dto(dto: AssistantMessageV1Dto): AssistantMessage {
    const blocks: CompletionBlock[] = [];
    if (dto.reasoning) {
      const reasoningBlock = new ReasoningCompletionBlock({
        id: `legacy-reasoning-${dto.id}`,
        createdAt: dto.createdAt,
        updatedAt: dto.updatedAt,
        deletedAt: null,
        messageId: dto.id,
        status:
          dto.status === MessageStatusEnum.ERROR
            ? CompletionBlockStatusEnum.ERROR
            : CompletionBlockStatusEnum.DONE,
        data: dto.reasoning,
      });
      blocks.push(reasoningBlock);
    }
    if (dto.events) {
      dto.events.forEach((event, index) => {
        const toolBlock = this.convertEventToToolBlock(
          event,
          dto.id,
          index,
          dto.createdAt,
          dto.updatedAt,
        );
        blocks.push(toolBlock);
      });
    }
    const contentBlock = new ContentCompletionBlock({
      id: `legacy-content-${dto.id}`,
      createdAt: dto.createdAt,
      updatedAt: dto.updatedAt,
      deletedAt: null,
      messageId: dto.id,
      status:
        dto.status === MessageStatusEnum.ERROR
          ? CompletionBlockStatusEnum.ERROR
          : CompletionBlockStatusEnum.DONE,
      data: dto.content,
    });
    blocks.push(contentBlock);

    return new AssistantMessage({
      id: dto.id,
      chatId: dto.chatId,
      createdAt: dto.createdAt,
      updatedAt: dto.updatedAt,
      deletedAt: null,
      role: MessageRoleEnum.ASSISTANT,
      status: dto.status,
      model: dto.model,
      traceId: dto.traceId,
      blocks,
    });
  }

  /*** Assistant Message V2 DTO <-> Assistant Message Conversion ***/
  private convertBlockToDto(block: CompletionBlock): CompletionBlockV2Dto {
    const baseBlock = {
      id: block.id,
      createdAt: block.createdAt,
      updatedAt: block.updatedAt,
      type: block.type,
      status: block.status,
      messageId: block.messageId,
      extra: block.extra,
    };

    switch (block.type) {
      case CompletionBlockTypeEnum.CONTENT:
        return {
          ...baseBlock,
          data: block.data,
        } as ContentBlockV2Dto;
      case CompletionBlockTypeEnum.REASONING:
        return {
          ...baseBlock,
          data: block.data,
        } as ReasoningBlockV2Dto;
      case CompletionBlockTypeEnum.TOOL:
        return {
          ...baseBlock,
          toolId: (block as ToolCompletionBlock).toolId,
          toolName: (block as ToolCompletionBlock).toolName,
          toolArguments: (block as ToolCompletionBlock).toolArguments,
          toolResult: (block as ToolCompletionBlock).toolResult,
          toolResponse: (block as ToolCompletionBlock).toolResponse,
          toolGenerateElapsedMs: (block as ToolCompletionBlock).toolGenerateElapsedMs,
          toolExecuteElapsedMs: (block as ToolCompletionBlock).toolExecuteElapsedMs,
        } as ToolBlockV2Dto;
    }
  }

  convertAssistantMessageV2DtoFromAssistantMessage(
    message: AssistantMessage,
  ): AssistantMessageV2Dto {
    return {
      id: message.id,
      chatId: message.chatId,
      createdAt: message.createdAt,
      updatedAt: message.updatedAt,
      role: message.role as MessageRoleEnum.ASSISTANT,
      status: message.status,
      model: message.model,
      traceId: message.traceId,
      error: message.error,
      blocks: message.blocks.map((b) => this.convertBlockToDto(b)),
    };
  }

  private convertDtoToBlock(dto: CompletionBlockV2Dto): CompletionBlock {
    const baseProps = {
      id: dto.id,
      createdAt: dto.createdAt,
      updatedAt: dto.updatedAt,
      deletedAt: null,
      status: dto.status,
      messageId: dto.messageId,
      extra: dto.extra,
    };
    switch (dto.type) {
      case CompletionBlockTypeEnum.CONTENT:
        return new ContentCompletionBlock({
          ...baseProps,
          data: (dto as ContentBlockV2Dto).data,
        });
      case CompletionBlockTypeEnum.REASONING:
        return new ReasoningCompletionBlock({
          ...baseProps,
          data: (dto as ReasoningBlockV2Dto).data,
        });
      case CompletionBlockTypeEnum.TOOL:
        return new ToolCompletionBlock({
          ...baseProps,
          toolId: (dto as ToolBlockV2Dto).toolId,
          toolName: (dto as ToolBlockV2Dto).toolName,
          toolArguments: (dto as ToolBlockV2Dto).toolArguments,
          toolResult: (dto as ToolBlockV2Dto).toolResult,
          toolResponse: (dto as ToolBlockV2Dto).toolResponse,
          toolGenerateElapsedMs: (dto as ToolBlockV2Dto).toolGenerateElapsedMs,
          toolExecuteElapsedMs: (dto as ToolBlockV2Dto).toolExecuteElapsedMs,
        });
      default:
        throw new Error(`Unsupported block type: ${dto.type}`);
    }
  }

  createAssistantMessageFromV2Dto(dto: AssistantMessageV2Dto): AssistantMessage {
    return new AssistantMessage({
      id: dto.id,
      chatId: dto.chatId,
      createdAt: dto.createdAt,
      updatedAt: dto.updatedAt,
      deletedAt: null,
      role: MessageRoleEnum.ASSISTANT,
      model: dto.model,
      error: dto.error,
      status: dto.status,
      blocks: dto.blocks.map((b) => this.convertDtoToBlock(b)),
      traceId: dto.traceId,
    });
  }

  /*** User Message DO <-> User Message Conversion ***/
  convertDOFromAssistantMessage(message: AssistantMessage): MessageDO {
    const isLegacy = this.isLegacyMessage(message);
    const baseProps = {
      id: message.id,
      chatId: message.chatId,
      createdAt: message.createdAt,
      updatedAt: message.updatedAt,
      deletedAt: message.deletedAt,
      role: message.role,
      mode: message.mode,
      status: message.status,
      model: message.model,
      error: message.error,
      traceId: message.traceId,
    };

    if (isLegacy) {
      const { content, reasoning, events, reasoningTimestamps } = this.extractV1FieldsFromBlocks(
        message.blocks || [],
      );
      const context: MessageContext[] = [];
      if (reasoning) {
        context.push({
          type: MessageContextEnum.REASONING_METADATA,
          begin_at: reasoningTimestamps.begin || '',
          end_at: reasoningTimestamps.end || '',
        });
      }
      if (events.length) {
        events.forEach((event) => {
          context.push({
            type: MessageContextEnum.EVENT,
            event,
          });
        });
      }

      return {
        ...baseProps,
        reasoning: reasoning,
        content: content,
        context,
        tools: {},
        command: null,
        shortcut: null,
        blocks: [],
      };
    }

    return {
      ...baseProps,
      reasoning: '',
      content: '',
      context: [],
      tools: {},
      command: null,
      shortcut: null,
      blocks: message.blocks.map((b) => b.toDO()),
    };
  }

  createAssistantMessageFromDO(data: MessageDO): AssistantMessage {
    let blocks: CompletionBlock[] = [];

    if (data.blocks) {
      // V2 format
      blocks = data.blocks.map((b) => CompletionBlock.fromDO(b));
    } else {
      // V1 format
      if (data.reasoning) {
        const reasoningBlock = new ReasoningCompletionBlock({
          id: `legacy-reasoning-${data.id}`,
          createdAt: data.createdAt,
          updatedAt: data.updatedAt,
          deletedAt: null,
          messageId: data.id,
          status:
            data.status === MessageStatusEnum.ERROR
              ? CompletionBlockStatusEnum.ERROR
              : CompletionBlockStatusEnum.DONE,
          data: data.reasoning,
        });
        blocks.push(reasoningBlock);
      }
      const events = data.context?.filter((c) => c.type === MessageContextEnum.EVENT);
      if (events) {
        events.forEach((event, index) => {
          const toolBlock = this.convertEventToToolBlock(
            event.event,
            data.id,
            index,
            data.createdAt,
            data.updatedAt,
          );
          blocks.push(toolBlock);
        });
      }
      const contentBlock = new ContentCompletionBlock({
        id: `legacy-content-${data.id}`,
        createdAt: data.createdAt,
        updatedAt: data.updatedAt,
        deletedAt: null,
        messageId: data.id,
        status:
          data.status === MessageStatusEnum.ERROR
            ? CompletionBlockStatusEnum.ERROR
            : CompletionBlockStatusEnum.DONE,
        data: data.content,
      });
      blocks.push(contentBlock);
    }

    const message = new AssistantMessage({
      id: data.id,
      chatId: data.chatId,
      createdAt: data.createdAt,
      updatedAt: data.updatedAt,
      deletedAt: data.deletedAt,
      role: data.role,
      status: data.status,
      model: data.model,
      error: data.error,
      traceId: data.traceId,
      blocks,
    });

    return message;
  }

  /*** Message <-> MessageV1Dto Conversion ***/
  convertMessageToV1Dto(message: Message): MessageV1Dto {
    if (message instanceof UserMessage) {
      return this.convertUserMessageV1DtoFromUserMessage(message);
    } else if (message instanceof AssistantMessage) {
      return this.convertAssistantMessageV1DtoFromAssistantMessage(message);
    }
    throw new Error(`Unknown message type for message ${message.id}`);
  }

  createMessageFromV1Dto(dto: MessageV1Dto): Message {
    if (dto instanceof UserMessageV1Dto) {
      return this.createUserMessageFromV1Dto(dto);
    } else if (dto instanceof AssistantMessageV1Dto) {
      return this.createAssistantMessageFromV1Dto(dto);
    }
  }

  /*** Message <-> MessageV2Dto Conversion ***/
  convertMessageToV2Dto(message: Message): MessageV2Dto {
    if (message instanceof UserMessage) {
      return this.convertUserMessageV2DtoFromUserMessage(message);
    } else if (message instanceof AssistantMessage) {
      return this.convertAssistantMessageV2DtoFromAssistantMessage(message);
    }
    throw new Error(`Unknown message type for message ${message.id}`);
  }

  createMessageFromV2Dto(dto: MessageV2Dto): Message {
    if (dto instanceof UserMessageV2Dto) {
      return this.createUserMessageFromV2Dto(dto);
    } else if (dto instanceof AssistantMessageV2Dto) {
      return this.createAssistantMessageFromV2Dto(dto);
    }
  }

  /*** Message <-> MessageDO Conversion ***/
  convertDOFromMessage(message: Message): MessageDO {
    if (message instanceof UserMessage) {
      const userMessageDO = this.convertDOFromUserMessage(message);
      return userMessageDO;
    } else if (message instanceof AssistantMessage) {
      const assistantMessageDO = this.convertDOFromAssistantMessage(message);
      if (this.isLegacyMessage(message)) {
        return assistantMessageDO;
      }

      const blocksDO = message.blocks?.map((b) => b.toDO());
      return {
        ...assistantMessageDO,
        blocks: blocksDO,
      };
    }
  }

  createMessageFromDO(data: MessageDO): Message {
    if (data.role === MessageRoleEnum.USER) {
      return this.createUserMessageFromDO(data);
    } else if (data.role === MessageRoleEnum.ASSISTANT) {
      return this.createAssistantMessageFromDO(data);
    }
  }
}

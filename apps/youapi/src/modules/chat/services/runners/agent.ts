import { CoreMessage } from 'ai';
import { ChatCompletionToolChoiceOption } from 'openai/resources/index.js';
import { ChatOriginTypeEnum, EditCommandTypeEnum, ToolNames } from '@/common/types';
import { Generation } from '@/modules/ai/domain/models/generation.entity';
import { SendMessageCommand } from '../commands/send-message.command';
import { ChatRunner } from './chat';

export class AgentRunner extends ChatRunner {
  protected readonly traceName = 'chat-assistant-agent';
  protected readonly prompt = 'writer-system-prompt';

  protected async getPromptVariables(): Promise<Record<string, any>> {
    const aiLanguage = await this.userDomainService.getPrimaryResponseLanguage({
      user_id: this.userId,
    });
    // const boardId = this.chat.getBoardId();
    // const board = await this.boardDomainService.getBoardById(boardId);
    // const directoryStructure = await this.boardDomainService.getDirectoryStructure(boardId);

    const assistantMessage = this.chat.getLastAssistantMessage();
    const user = await this.userDomainService.selectOneById(this.userId);
    return {
      model: assistantMessage.model,
      userName: user.name,
      language: aiLanguage,
      currentTime: new Date().toLocaleString(),
      boardName: 'Unsorted',
      boardDirectoryStructure: 'No directory structure',
    };
  }

  protected async preparePromptMessages(): Promise<CoreMessage[]> {
    // 把上一轮的消息返回
    if (this.currentGeneration) {
      return [
        ...this.currentGeneration.promptMessages,
        ...this.currentGeneration.generatedMessages,
      ];
    }

    // 首次生成消息
    const { promptMessages } = await this.promptService.getPromptAndMessages(
      this.prompt,
      await this.getPromptVariables(),
    );
    return [...promptMessages, ...this.chat.toCoreMessages()];
  }

  protected getToolSetup(command: SendMessageCommand): {
    tools: ToolNames[];
    toolChoice: ChatCompletionToolChoiceOption;
  } {
    let tools: ToolNames[] = [
      ToolNames.AUDIO_GENERATE,
      ToolNames.IMAGE_GENERATE,
      ToolNames.DIAGRAM_GENERATE,
      ToolNames.GOOGLE_SEARCH,
      ToolNames.LIBRARY_SEARCH,
      ToolNames.BOARD_SEARCH,
      ToolNames.EDIT_THOUGHT,
      ToolNames.CREATE_SNIP_BY_URL,
    ];
    let toolChoice: ChatCompletionToolChoiceOption = 'auto';
    // TODO: 迁移 board structure 逻辑
    const disableLibrarySearch = false;

    if (this.chat.origin?.type === ChatOriginTypeEnum.WEBPAGE) {
      tools = tools.filter((tool) =>
        [ToolNames.LIBRARY_SEARCH, ToolNames.BOARD_SEARCH, ToolNames.CREATE_SNIP_BY_URL].includes(
          tool,
        ),
      );
    }
    if (disableLibrarySearch) {
      tools = tools.filter((tool) => tool !== ToolNames.LIBRARY_SEARCH);
    }

    const hasToolHistory = this.getGeneratedCoreMessages().some(
      (message) => message.role === 'tool',
    );
    if (hasToolHistory) {
      return {
        tools,
        toolChoice,
      };
    }

    // 通过 tool 数组强制使用工具
    const requiredToolUse = Object.keys(command.param.tools).find(
      (tool) => command.param.tools?.[tool]?.useTool === 'required',
    );
    if (requiredToolUse && tools.includes(requiredToolUse as ToolNames)) {
      toolChoice = {
        type: 'function',
        function: { name: requiredToolUse },
      };
      return {
        tools,
        toolChoice,
      };
    }

    // 通过 command 对象强制使用工具
    let requiredToolUseByCommand;
    switch (command.param.command?.type) {
      case EditCommandTypeEnum.SUGGEST_SEARCH:
        requiredToolUseByCommand = ToolNames.GOOGLE_SEARCH;
        break;
      case EditCommandTypeEnum.SUGGEST_GENERATE_TEXT:
        requiredToolUseByCommand = ToolNames.EDIT_THOUGHT;
        break;
      case EditCommandTypeEnum.SUGGEST_GENERATE_AUDIO:
        requiredToolUseByCommand = ToolNames.AUDIO_GENERATE;
        break;
      case EditCommandTypeEnum.SUGGEST_GENERATE_IMAGE:
        requiredToolUseByCommand = ToolNames.IMAGE_GENERATE;
        break;
      default:
        break;
    }
    if (requiredToolUseByCommand && tools.includes(requiredToolUseByCommand as ToolNames)) {
      toolChoice = {
        type: 'function',
        function: { name: requiredToolUseByCommand },
      };
      return {
        tools,
        toolChoice,
      };
    }
  }

  protected async setupGeneration(command: SendMessageCommand) {
    const assistantMessage = this.chat.getLastAssistantMessage();
    const promptMessages = await this.preparePromptMessages();
    const generation = new Generation({
      ...this.getToolSetup(command),
      model: assistantMessage.model,
      prompt: this.promptService.fetchPrompt({ name: this.prompt }),
      promptMessages,
      traceMetadata: this.chat.getTraceMetadata(),
    });
    this.generations.push(generation);
    this.currentGeneration = generation;
  }
}

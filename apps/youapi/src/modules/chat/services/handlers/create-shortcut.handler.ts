/**
 * Create Shortcut Handler - 创建快捷指令处理器
 *
 * 处理快捷指令创建的业务逻辑，包括：
 * 1. 参数验证
 * 2. 创建快捷指令实体
 * 3. 调用仓储进行持久化
 * 4. 返回创建结果
 */

import { Injectable, Logger } from '@nestjs/common';
import { CommandHandler, ICommandHandler } from '@nestjs/cqrs';
import { rankBetween } from '@/common/utils';
import { Shortcut } from '../../domain/shortcut/models/shortcut.entity';
import { ShortcutV2Dto } from '../../dto/shortcut.dto';
import { ShortcutRepository } from '../../repositories/shortcut.repository';
import { CreateShortcutCommand } from '../commands/create-shortcut.command';
import { ShortcutDtoService } from '../shortcut-dto.service';

@CommandHandler(CreateShortcutCommand)
@Injectable()
export class CreateShortcutHandler implements ICommandHandler<CreateShortcutCommand> {
  private readonly logger = new Logger(CreateShortcutHandler.name);

  constructor(
    private readonly shortcutRepository: ShortcutRepository,
    private readonly shortcutDtoService: ShortcutDtoService,
  ) {}

  async execute(command: CreateShortcutCommand): Promise<ShortcutV2Dto> {
    const { userId, spaceId, name, prompt } = command;

    try {
      // 计算排序位置（插入到列表开头）
      const firstRank = await this.shortcutRepository.getFirstRankInSpace(spaceId);
      const rank = rankBetween(undefined, firstRank);

      // 创建快捷指令实体
      const shortcut = Shortcut.create({
        creatorId: userId,
        name,
        prompt,
        rank,
      });

      // 保存到仓储
      await this.shortcutRepository.save(shortcut, spaceId);

      // 提交领域事件
      shortcut.commit();

      // 转换为DTO并返回
      const dto = this.shortcutDtoService.toShortcutV2Dto(shortcut);
      return dto;
    } catch (error) {
      this.logger.error('[CreateShortcutHandler] Error creating shortcut:');
      this.logger.error(error);
      throw error;
    }
  }
}

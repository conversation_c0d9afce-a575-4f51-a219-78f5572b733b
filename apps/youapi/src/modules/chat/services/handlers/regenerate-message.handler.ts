/**
 * 重新生成消息命令处理器
 * 处理重新生成聊天消息的业务逻辑，支持V1和V2 API版本
 *
 * Migrated from:
 * - /Users/<USER>/Projects/github.com/YouMindInc/youapp/src/app/api/v1/chat/regenerateMessage/route.ts
 * - /Users/<USER>/Projects/github.com/YouMindInc/youapp/src/app/api/v2/chatAssistant/regenerateMessage/route.ts
 * - /Users/<USER>/Projects/github.com/YouMindInc/youapp/src/lib/app/chat/index.ts (regenerateMessage function)
 * - /Users/<USER>/Projects/github.com/YouMindInc/youapp/src/lib/app/chat/v2.ts (regenerateMessage function)
 */

import { Injectable, Logger } from '@nestjs/common';
import { CommandHandler, ICommandHandler } from '@nestjs/cqrs';
import { InvalidArguments } from '@/common/errors';
import { YouapiClsService } from '@/common/services/cls.service';
import {
  DEFAULT_AI_AGENT_MODEL,
  DEFAULT_AI_CHAT_MODEL,
  LLMs,
  MessageModeEnum,
} from '@/common/types';
import { AssistantMessage, UserMessage } from '../../domain/message/models/message.entity';
import { ChatRepository } from '../../repositories/chat.repository';
import { MessageRepository } from '../../repositories/message.repository';
import { ChatAggregateService } from '../chat-aggregate.service';
import { RegenerateMessageCommand } from '../commands/regenerate-message.command';
import { SendMessageCommand } from '../commands/send-message.command';
import { getChatRunner } from '../runners';

@Injectable()
@CommandHandler(RegenerateMessageCommand)
export class RegenerateMessageHandler implements ICommandHandler<RegenerateMessageCommand> {
  private readonly logger = new Logger(RegenerateMessageHandler.name);

  constructor(
    private readonly chatRepository: ChatRepository,
    private readonly messageRepository: MessageRepository,
    private readonly chatAggregateService: ChatAggregateService,
    private readonly youapiClsService: YouapiClsService,
  ) {}

  async execute(command: RegenerateMessageCommand) {
    const { userId, chatId, userMessageId, tools, version } = command;
    this.logger.log(
      `[${version.toUpperCase()}] Regenerating message in chat ${chatId}, user message ${userMessageId} for user ${userId}`,
    );

    // 1. 获取用户消息并验证
    const userMessage = (await this.messageRepository.findById(userMessageId)) as UserMessage;
    if (!userMessage) {
      throw new InvalidArguments('Invalid user message ID');
    }

    // 2. 确定聊天模型
    let chatModel = command.chatModel || DEFAULT_AI_CHAT_MODEL;

    // V2特性：Agent模式自动升级到CLAUDE_4_SONNET
    if (userMessage.mode === MessageModeEnum.AGENT) {
      chatModel = DEFAULT_AI_AGENT_MODEL;
      this.logger.debug(`Upgrading to ${DEFAULT_AI_AGENT_MODEL} for AGENT mode message`);
    }

    // 3. 创建新的助手消息
    const assistantMessage = AssistantMessage.createNewFromUserMessage(userMessage);
    assistantMessage.model = chatModel as LLMs;
    assistantMessage.setTraceId(this.youapiClsService.getTraceId());
    if (tools) {
      userMessage.tools = tools;
    }

    // 保存新的助手消息
    const savedAssistantMessage = await this.messageRepository.save(assistantMessage);

    // 4. 删除中间消息
    await this.messageRepository.deleteByRegenerate({
      chatId: chatId,
      userMessageId: userMessageId,
      regeneratedMessageId: savedAssistantMessage.id,
    });

    this.logger.log(
      `Successfully created regenerated assistant message ${savedAssistantMessage.id} for chat ${chatId}`,
    );

    const updatedChatData = await this.chatRepository.findDetailRawDataById(chatId);
    if (!updatedChatData) {
      throw new Error('Failed to retrieve updated chat data after regeneration');
    }

    const chat = this.chatAggregateService.aggregateChatFromJoinResult(updatedChatData);
    const runner = getChatRunner(chat, userId);
    const sendMessageCommand = new SendMessageCommand({
      userId,
      chatId,
      chatModel,
      boardId: chat.boardId,
      origin: userMessage.origin,
      message: userMessage.content,
      messageMode: userMessage.mode,
      selection: userMessage.selection,
      atReferences: userMessage.atReferences,
      tools: userMessage.tools,
      command: userMessage.command,
      shortcut: userMessage.shortcut,
    });
    return await runner.generate(sendMessageCommand);
  }
}

/**
 * Send Message Handler - 发送消息命令处理器
 * 处理发送消息的业务逻辑，验证权限，保存消息
 *
 * Migrated from:
 * - /Users/<USER>/Projects/github.com/YouMindInc/youapp/src/lib/app/chat/index.ts (chatWithMessage function)
 */

import { Injectable, Logger } from '@nestjs/common';
import { CommandHandler, ICommandHandler } from '@nestjs/cqrs';
import type { Observable } from 'rxjs';
import { NotFound, ResourceEnum } from '@/common/errors';
import { YouapiClsService } from '@/common/services/cls.service';
import {
  CompletionStreamChunk,
  DEFAULT_AI_CHAT_MODEL,
  MessageModeEnum,
} from '@/common/types/chat.types';
import { AssistantMessage, UserMessage } from '../../domain/message/models/message.entity';
import { ChatRepository } from '../../repositories/chat.repository';
import { SendMessageCommand } from '../commands/send-message.command';
import { getChatRunner } from '../runners';

@CommandHandler(SendMessageCommand)
@Injectable()
export class SendMessageHandler implements ICommandHandler<SendMessageCommand> {
  private readonly logger = new Logger(SendMessageHandler.name);

  constructor(
    private readonly chatRepository: ChatRepository,
    private readonly youapiClsService: YouapiClsService,
  ) {}

  async execute(command: SendMessageCommand): Promise<Observable<CompletionStreamChunk<any>>> {
    const {
      userId,
      chatId,
      boardId,
      message,
      selection,
      atReferences,
      chatModel = DEFAULT_AI_CHAT_MODEL,
      origin,
      messageMode = MessageModeEnum.ASK,
      tools,
      shortcut,
    } = command.param;

    this.logger.log(`Processing send message for chat ${chatId} by user ${userId}`);

    // 1. 验证聊天是否存在且用户有权限访问
    const chat = await this.chatRepository.findById(chatId);
    if (!chat) {
      throw new NotFound({
        resource: ResourceEnum.CHAT,
        id: chatId,
      });
    }

    // TODO: 添加权限验证
    // await authzDomain.authzChat(userId, chat);

    // 2. 创建用户消息
    const userMessage = UserMessage.createNew({
      chatId: chat.id,
      message: message,
      origin: origin,
      selection: selection || '',
      atReferences: atReferences || [],
      tools: tools || {},
      mode: messageMode || MessageModeEnum.ASK,
      model: chatModel,
      command: command.param.command,
      shortcut,
      boardId,
    });
    const assistantMessage = AssistantMessage.createNewFromUserMessage(userMessage);
    assistantMessage.setTraceId(this.youapiClsService.getTraceId());

    chat.addMessage(userMessage);
    chat.addMessage(assistantMessage);

    const runner = getChatRunner(chat, userId);
    const observable = await runner.generate(command);
    return observable as unknown as Observable<CompletionStreamChunk<any>>;
  }
}

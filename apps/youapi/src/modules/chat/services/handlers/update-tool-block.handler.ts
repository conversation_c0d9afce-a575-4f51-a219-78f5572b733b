/**
 * 重新生成消息命令处理器
 * 处理重新生成聊天消息的业务逻辑
 *
 * Migrated from:
 * - /Users/<USER>/Projects/github.com/YouMindInc/youapp/src/app/api/v1/chat/regenerateMessage/route.ts
 * - /Users/<USER>/Projects/github.com/YouMindInc/youapp/src/lib/app/chat/index.ts (regenerateMessage function)
 */

import { Injectable, Logger } from '@nestjs/common';
import { CommandHandler, ICommandHandler } from '@nestjs/cqrs';
import { ToolCompletionBlock } from '@/modules/ai/domain/models/completion-block.entity';
import { CompletionBlockRepository } from '@/modules/ai/repositories/completion-block.repository';
import { UpdateToolCompletionBlockCommand } from '../commands/update-block.command';

@Injectable()
@CommandHandler(UpdateToolCompletionBlockCommand)
export class UpdateToolBlockHandler implements ICommandHandler<UpdateToolCompletionBlockCommand> {
  private readonly logger = new Logger(UpdateToolBlockHandler.name);

  constructor(private readonly completionBlockRepository: CompletionBlockRepository) {}

  async execute(command: UpdateToolCompletionBlockCommand): Promise<void> {
    const { blockId, toolResult, toolResponse, extra } = command;
    const block = (await this.completionBlockRepository.getById(blockId)) as ToolCompletionBlock;
    if (!block) return;

    block.updateExecution(toolResult, toolResponse, extra);
    await this.completionBlockRepository.save(block);
    block.commit();
  }
}

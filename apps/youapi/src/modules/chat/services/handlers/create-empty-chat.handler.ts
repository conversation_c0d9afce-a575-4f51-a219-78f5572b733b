/**
 * 创建空聊天命令处理器
 * 处理创建空聊天的业务逻辑，不包含任何消息的纯聊天会话
 *
 * Migrated from:
 * - /Users/<USER>/Projects/github.com/YouMindInc/youapp/src/app/api/v2/chatAssistant/createEmptyChat/route.ts
 * - /Users/<USER>/Projects/github.com/YouMindInc/youapp/src/lib/app/chat/v2.ts (createChat function)
 * - /Users/<USER>/Projects/github.com/YouMindInc/youapp/src/lib/domain/chat/index.ts (createChat method)
 */

import { Injectable, Logger } from '@nestjs/common';
import { CommandHandler, ICommandHandler } from '@nestjs/cqrs';
import { ChatModeEnum } from '@/common/types';
import { Chat } from '../../domain/chat/models/chat.entity';
import { ChatDetailV2Dto } from '../../dto/v2/chat-v2.dto';
import { ChatRepository } from '../../repositories/chat.repository';
import { ChatAggregateService } from '../chat-aggregate.service';
import { CreateEmptyChatCommand } from '../commands/create-empty-chat.command';

@Injectable()
@CommandHandler(CreateEmptyChatCommand)
export class CreateEmptyChatHandler implements ICommandHandler<CreateEmptyChatCommand> {
  private readonly logger = new Logger(CreateEmptyChatHandler.name);

  constructor(
    private readonly chatRepository: ChatRepository,
    private readonly chatAggregateService: ChatAggregateService,
  ) {}

  async execute(command: CreateEmptyChatCommand): Promise<ChatDetailV2Dto> {
    const { userId, dto } = command;
    this.logger.log(`Creating empty chat for user ${userId} with title: ${dto.title}`);

    try {
      // 1. 权限验证 - 对应youapp的authByOrigin()
      // TODO: 实现origin权限验证逻辑
      // await this.validateOriginAccess(dto.origin, userId);

      // 2. 处理标题中的@引用 - 对应youapp的replaceAtReference()
      const processedTitle = this.processAtReferences(dto.title);

      // 3. 创建聊天聚合 - 对应youapp的chatDomain.createChat()
      const chat = Chat.createNew({
        creatorId: userId,
        origin: dto.origin,
        title: processedTitle,
        boardId: dto.boardId,
        mode: ChatModeEnum.CHAT,
        webpageContent: dto.origin.type === 'webpage' ? dto.origin.content : undefined,
        showNewBoardSuggestion: false,
      });

      // 4. 保存聊天到数据库
      const savedChat = await this.chatRepository.save(chat);

      // 提交领域事件
      savedChat.commit();

      this.logger.log(`Successfully created empty chat ${savedChat.id} for user ${userId}`);

      // 5. 使用聚合服务的V2 assembler返回结果
      // 创建的是空聊天，没有消息
      const chatDetail = await this.chatAggregateService.toChatDetailDto(
        savedChat,
        'v2', // 强制使用V2格式
      );

      return chatDetail as ChatDetailV2Dto;
    } catch (error) {
      this.logger.error(`Failed to create empty chat for user ${userId}`);
      this.logger.error(error);
      throw error;
    }
  }

  /**
   * 处理标题中的@引用
   * 对应youapp的replaceAtReference函数
   * 将 @mention 转换为 @"mention" 格式
   */
  private processAtReferences(title: string): string {
    // 简化的@引用处理，将@后的文本用双引号包围
    // 更复杂的实现可以解析实际的引用实体
    return title.replace(/@([^\s@]+)/g, '@"$1"');
  }
}

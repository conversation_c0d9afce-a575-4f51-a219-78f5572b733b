/**
 * 创建 Chat 命令处理器
 * 处理创建聊天的业务逻辑，包括创建聊天会话和初始消息
 *
 * Migrated from:
 * - /Users/<USER>/Projects/github.com/YouMindInc/youapp/src/app/api/v1/chat/createChat/route.ts
 */

import { Injectable, Logger } from '@nestjs/common';
import { CommandHandler, ICommandHandler } from '@nestjs/cqrs';
import { YouapiClsService } from '@/common/services/cls.service';
import { ChatOriginTypeEnum, MessageModeEnum } from '@/common/types';
import { Chat } from '../../domain/chat/models/chat.entity';
import { AssistantMessage, UserMessage } from '../../domain/message/models/message.entity';
import { ChatRepository } from '../../repositories/chat.repository';
import { CreateChatCommand } from '../commands/create-chat.command';
import { SendMessageCommand } from '../commands/send-message.command';
import { getChatRunner } from '../runners';

@Injectable()
@CommandHandler(CreateChatCommand)
export class CreateChatHandler implements ICommandHandler<CreateChatCommand> {
  private readonly logger = new Logger(CreateChatHandler.name);

  constructor(
    private readonly chatRepository: ChatRepository,
    private readonly youapiClsService: YouapiClsService,
  ) {}

  async execute(command: CreateChatCommand) {
    try {
      const {
        userId,
        boardId,
        origin,
        message,
        mode,
        messageMode,
        chatModel,
        selection,
        tools,
        atReferences,
        shortcut,
        version,
      } = command.param;

      // 创建聊天聚合
      const chat = Chat.createNew({
        creatorId: userId,
        origin: origin,
        title: message.slice(0, 100), // 使用消息前100个字符作为标题
        boardId: boardId,
        mode,
        webpageContent: origin?.type === ChatOriginTypeEnum.WEBPAGE ? origin.content : undefined,
        showNewBoardSuggestion: false,
      });
      await this.chatRepository.save(chat);
      chat.commit();

      // 使用具体的工厂方法创建消息
      const userMessage = UserMessage.createNew({
        chatId: chat.id,
        message: message,
        origin: origin,
        selection: selection || '',
        atReferences: atReferences || [],
        tools: tools || {},
        mode: messageMode || MessageModeEnum.ASK,
        model: chatModel,
        command: command.param.command,
        shortcut: shortcut,
      });
      const assistantMessage = AssistantMessage.createNewFromUserMessage(userMessage);
      assistantMessage.setTraceId(this.youapiClsService.getTraceId());

      chat.addMessage(userMessage);
      chat.addMessage(assistantMessage);

      const runner = getChatRunner(chat, userId);
      const sendMessageCommand = new SendMessageCommand({
        ...command.param,
        chatId: chat.id,
      });
      return runner.generate(sendMessageCommand);
    } catch (error) {
      this.logger.error(`Error creating chat`);
      this.logger.error(error);
      throw error;
    }
  }
}

import { Call<PERSON><PERSON><PERSON>, ExecutionContext, Injectable, Logger, NestInterceptor } from '@nestjs/common';
import { catchError, Observable, of } from 'rxjs';
import { RestError } from '@/common/errors';
import { CompletionStreamModeEnum } from '@/common/types';

@Injectable()
export class SSEErrorHandlerInterceptor implements NestInterceptor {
  private readonly logger = new Logger(SSEErrorHandlerInterceptor.name);

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const request = context.switchToHttp().getRequest();

    return next.handle().pipe(
      catchError((error: any) => {
        this.logger.error('SSE Error intercepted');
        this.logger.error(error);

        let errorData: any;
        if (error instanceof RestError) {
          errorData = error.json('');
        } else {
          errorData = {
            code: (error as Error).name || 'UNKNOWN_ERROR',
            status: error.status || 500,
            message: (error as Error).message || 'An unexpected error occurred',
          };
        }

        const errorChunk = {
          mode: CompletionStreamModeEnum.REPLACE,
          targetType: 'Message',
          targetId: request.body?.messageId || 'unknown',
          path: 'error',
          data: errorData,
        };

        this.logger.log(`Emitting error chunk: ${JSON.stringify(errorChunk)}`);
        return of(errorChunk);
      }),
    );
  }
}

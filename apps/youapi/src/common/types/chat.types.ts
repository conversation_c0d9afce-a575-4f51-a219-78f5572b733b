/**
 * Cha<PERSON> and LL<PERSON> types
 * Migrated from youapp/src/lib/common/chat
 */

import { LanguageEnum } from '@repo/common';
import z from 'zod';
import type { RestErrorInfo } from './api.types';
import { ImageVOSchema, SnipTypeEnum, SnipVOSchema, VoiceVOSchema } from './snip.types';
import { LanguageNameMap } from './system.types';

export enum LLMProviders {
  // OpenAI 本体
  OPENAI = 'openai',
  // Anthropic 本体
  ANTHROPIC = 'anthropic',
  // Azure
  AZURE_OPENAI = 'azure',
  // 第三方服务提供商
  AI_HUB_MIX = 'aihubmix',
  // Amazon Bedrock
  AMAZON_BEDROCK = 'bedrock',
  // Deepseek
  DEEPSEEK = 'deepseek',
  // Google Vertex AI
  VERTEXAI = 'vertexai',
  // 阿里云
  ALIYUN = 'aliyun',
  // Minimax
  MINIMAX = 'minimax',
  // ElevenLabs
  ELEVENLABS = 'elevenlabs',
}
export enum LLMs {
  // OpenAI Chat
  GPT_4O = 'gpt-4o',
  GPT_4O_MINI = 'gpt-4o-mini',
  GPT_41 = 'gpt-4.1',
  GPT_41_MINI = 'gpt-4.1-mini',
  GPT_41_NANO = 'gpt-4.1-nano',
  O1_MINI = 'o1-mini',
  O1_PREVIEW = 'o1-preview',
  O3_MINI = 'o3-mini',
  O4_MINI = 'o4-mini',
  // OpenAI Embedding
  TEXT_EMBEDDING_3_LARGE = 'text-embedding-3-large',
  // OpenAI TTS
  TTS_1 = 'tts-1',
  TTS_1_HD = 'tts-1-hd',
  GPT_4O_MINI_TTS = 'gpt-4o-mini-tts',
  // OpenAI Image
  GPT_IMAGE_1 = 'gpt-image-1',
  // Anthropic
  CLAUDE_4_SONNET = 'claude-4-sonnet',
  CLAUDE_37_SONNET = 'claude-3-7-sonnet',
  CLAUDE_37_SONNET_THINKING = 'claude-3-7-sonnet-thinking',
  CLAUDE_35_SONNET = 'claude-3-5-sonnet',
  CLAUDE_35_HAIKU = 'claude-3-5-haiku',
  // Deepseek
  DEEPSEEK_CHAT = 'deepseek/deepseek_v3',
  DEEPSEEK_REASONER = 'deepseek/deepseek-r1',
  // Google
  GEMINI_25_PRO = 'gemini-2.5-pro',
  GEMINI_25_FLASH = 'gemini-2.5-flash',
  GEMINI_25_FLASH_LITE = 'gemini-2.5-flash-lite',
  GEMINI_2_FLASH = 'gemini-2.0-flash',
  GEMINI_2_FLASH_LITE = 'gemini-2.0-flash-lite',
  // Aliyun
  QWEN_PLUS = 'qwen-plus',
  QWEN_MAX = 'qwen-max',
  QWEN_TURBO = 'qwen-turbo',
  // Minimax
  SPEECH_02_HD = 'speech-02-hd',
  SPEECH_02_TURBO = 'speech-02-turbo',
  SPEECH_01_HD = 'speech-01-hd',
  SPEECH_01_TURBO = 'speech-01-turbo',
}
export enum ModelTypeEnum {
  TTS = 'tts',
  CHAT = 'chat',
  REASONING = 'reasoning',
  EMBEDDING = 'embedding',
  IMAGE = 'image',
}
export interface ModelDefinition {
  title: string;
  short_title: string;
  type: ModelTypeEnum;
  from: string;
  value: LLMs;
  icon_url: string;
  providers: LLMProviders[];
  input_token_limit: number;
  output_token_limit: number;
  input_per_mil: number;
  output_per_mil: number;
  cached_input_per_mil: number;
  extra?: Record<string, unknown>;
}
export const ModelIconMapping = {
  openai: 'https://cdn.gooo.ai/assets/select_model_chatgpt.png',
  anthropic: 'https://cdn.gooo.ai/assets/select_model_claude.png',
  deepseek: 'https://cdn.gooo.ai/assets/select_model_deepseek.png',
  gemini: 'https://cdn.gooo.ai/assets/select_model_gemini.png',
  qwen: 'https://cdn.gooo.ai/assets/select_model_qwen.png',
  best: 'https://cdn.gooo.ai/assets/select_model_best_4.png',
};
export const MODEL_DEFINITION = new Map<string, ModelDefinition>([
  // https://platform.openai.com/docs/models/gpt-4o
  [
    LLMs.GPT_4O,
    {
      title: 'GPT-4o',
      short_title: '4o',
      type: ModelTypeEnum.CHAT,
      from: 'openai',
      value: LLMs.GPT_4O,
      providers: [LLMProviders.AI_HUB_MIX, LLMProviders.OPENAI, LLMProviders.AZURE_OPENAI],
      input_token_limit: 128000,
      output_token_limit: 16384,
      icon_url: ModelIconMapping.openai,
      input_per_mil: 2.5,
      output_per_mil: 10,
      cached_input_per_mil: 1.25,
    },
  ],
  // https://platform.openai.com/docs/models/gpt-4o-mini
  [
    LLMs.GPT_4O_MINI,
    {
      title: 'GPT-4o mini',
      short_title: '4o mini',
      type: ModelTypeEnum.CHAT,
      from: 'openai',
      value: LLMs.GPT_4O_MINI,
      providers: [LLMProviders.OPENAI, LLMProviders.AZURE_OPENAI],
      input_token_limit: 128000,
      output_token_limit: 16384,
      icon_url: ModelIconMapping.openai,
      input_per_mil: 0.15,
      output_per_mil: 0.6,
      cached_input_per_mil: 0.075,
    },
  ],
  // https://platform.openai.com/docs/models/o3-mini
  [
    LLMs.O3_MINI,
    {
      title: 'o3-mini',
      short_title: 'o3 mini',
      type: ModelTypeEnum.REASONING,
      from: 'openai',
      value: LLMs.O3_MINI,
      providers: [LLMProviders.OPENAI, LLMProviders.AI_HUB_MIX],
      input_token_limit: 200000,
      output_token_limit: 100000,
      icon_url: ModelIconMapping.openai,
      input_per_mil: 1.1,
      output_per_mil: 4.4,
      cached_input_per_mil: 0.55,
      extra: {
        fake_reasoning: true,
      },
    },
  ],
  // https://platform.openai.com/docs/models/o4-mini
  [
    LLMs.O4_MINI,
    {
      title: 'o4-mini',
      short_title: 'o4 mini',
      type: ModelTypeEnum.REASONING,
      from: 'openai',
      value: LLMs.O4_MINI,
      providers: [LLMProviders.OPENAI, LLMProviders.AZURE_OPENAI, LLMProviders.AI_HUB_MIX],
      input_token_limit: 200000,
      output_token_limit: 100000,
      icon_url: ModelIconMapping.openai,
      input_per_mil: 1.1,
      output_per_mil: 4.4,
      cached_input_per_mil: 0.275,
      extra: {
        fake_reasoning: true,
      },
    },
  ],
  // https://platform.openai.com/docs/models/gpt-4.1
  [
    LLMs.GPT_41,
    {
      title: 'GPT-4.1',
      short_title: '4.1',
      type: ModelTypeEnum.CHAT,
      from: 'openai',
      value: LLMs.GPT_41,
      providers: [LLMProviders.OPENAI, LLMProviders.AZURE_OPENAI],
      input_token_limit: 1000000,
      output_token_limit: 32768,
      icon_url: ModelIconMapping.openai,
      input_per_mil: 2,
      output_per_mil: 8,
      cached_input_per_mil: 0.5,
    },
  ],
  // https://platform.openai.com/docs/models/gpt-4.1-mini
  [
    LLMs.GPT_41_MINI,
    {
      title: 'GPT-4.1 mini',
      short_title: '4.1 mini',
      type: ModelTypeEnum.CHAT,
      from: 'openai',
      value: LLMs.GPT_41_MINI,
      providers: [LLMProviders.OPENAI, LLMProviders.AI_HUB_MIX],
      input_token_limit: 1000000,
      output_token_limit: 32768,
      icon_url: ModelIconMapping.openai,
      input_per_mil: 0.4,
      output_per_mil: 1.6,
      cached_input_per_mil: 0.1,
    },
  ],
  // https://platform.openai.com/docs/models/gpt-4.1-nano
  [
    LLMs.GPT_41_NANO,
    {
      title: 'GPT-4.1 nano',
      short_title: '4.1 nano',
      type: ModelTypeEnum.CHAT,
      from: 'openai',
      value: LLMs.GPT_41_NANO,
      providers: [LLMProviders.OPENAI, LLMProviders.AI_HUB_MIX],
      input_token_limit: 1000000,
      output_token_limit: 32768,
      icon_url: ModelIconMapping.openai,
      input_per_mil: 0.1,
      output_per_mil: 0.4,
      cached_input_per_mil: 0.025,
    },
  ],
  // https://platform.openai.com/docs/models/text-embedding-3-large
  [
    LLMs.TEXT_EMBEDDING_3_LARGE,
    {
      title: 'embedding',
      short_title: 'embedding',
      type: ModelTypeEnum.EMBEDDING,
      from: 'openai',
      value: LLMs.TEXT_EMBEDDING_3_LARGE,
      providers: [LLMProviders.OPENAI, LLMProviders.AZURE_OPENAI],
      input_token_limit: 0,
      output_token_limit: 0,
      icon_url: ModelIconMapping.openai,
      input_per_mil: 0.02,
      output_per_mil: 0,
      cached_input_per_mil: 0,
    },
  ],
  // https://platform.openai.com/docs/models/tts-1
  [
    LLMs.TTS_1,
    {
      title: 'TTS 1',
      short_title: 'TTS 1',
      type: ModelTypeEnum.TTS,
      from: 'openai',
      value: LLMs.TTS_1,
      providers: [LLMProviders.OPENAI],
      input_token_limit: 128000,
      output_token_limit: 0,
      icon_url: ModelIconMapping.openai,
      input_per_mil: 15,
      output_per_mil: 0,
      cached_input_per_mil: 0,
    },
  ],
  [
    LLMs.TTS_1_HD,
    {
      title: 'TTS 1 HD',
      short_title: 'TTS 1 HD',
      type: ModelTypeEnum.TTS,
      from: 'openai',
      value: LLMs.TTS_1_HD,
      providers: [LLMProviders.OPENAI],
      input_token_limit: 128000,
      output_token_limit: 0,
      icon_url: ModelIconMapping.openai,
      input_per_mil: 15,
      output_per_mil: 0,
      cached_input_per_mil: 0,
    },
  ],
  [
    LLMs.GPT_4O_MINI_TTS,
    {
      title: 'GPT-4o mini TTS',
      short_title: '4o mini TTS',
      type: ModelTypeEnum.TTS,
      from: 'openai',
      value: LLMs.GPT_4O_MINI_TTS,
      providers: [LLMProviders.OPENAI],
      input_token_limit: 128000,
      output_token_limit: 0,
      icon_url: ModelIconMapping.openai,
      input_per_mil: 15,
      output_per_mil: 0,
      cached_input_per_mil: 0,
    },
  ],
  [
    LLMs.SPEECH_02_HD,
    {
      title: 'TTS',
      short_title: 'TTS',
      type: ModelTypeEnum.TTS,
      from: 'minimax',
      value: LLMs.SPEECH_02_HD,
      providers: [LLMProviders.MINIMAX],
      input_token_limit: 128000,
      output_token_limit: 0,
      icon_url: ModelIconMapping.openai,
      input_per_mil: 0,
      output_per_mil: 100,
      cached_input_per_mil: 0,
    },
  ],
  [
    LLMs.SPEECH_02_TURBO,
    {
      title: 'TTS',
      short_title: 'TTS',
      type: ModelTypeEnum.TTS,
      from: 'minimax',
      value: LLMs.SPEECH_02_TURBO,
      providers: [LLMProviders.MINIMAX],
      input_token_limit: 128000,
      output_token_limit: 0,
      icon_url: ModelIconMapping.openai,
      input_per_mil: 0,
      output_per_mil: 100,
      cached_input_per_mil: 0,
    },
  ],
  [
    LLMs.SPEECH_01_HD,
    {
      title: 'TTS',
      short_title: 'TTS',
      type: ModelTypeEnum.TTS,
      from: 'minimax',
      value: LLMs.SPEECH_01_HD,
      providers: [LLMProviders.MINIMAX],
      input_token_limit: 128000,
      output_token_limit: 0,
      icon_url: ModelIconMapping.openai,
      input_per_mil: 0,
      output_per_mil: 100,
      cached_input_per_mil: 0,
    },
  ],
  [
    LLMs.SPEECH_01_TURBO,
    {
      title: 'TTS',
      short_title: 'TTS',
      type: ModelTypeEnum.TTS,
      from: 'minimax',
      value: LLMs.SPEECH_01_TURBO,
      providers: [LLMProviders.MINIMAX],
      input_token_limit: 128000,
      output_token_limit: 0,
      icon_url: ModelIconMapping.openai,
      input_per_mil: 0,
      output_per_mil: 100,
      cached_input_per_mil: 0,
    },
  ],
  [
    LLMs.GPT_IMAGE_1,
    {
      title: 'GPT Image 1',
      short_title: 'GPT Image 1',
      type: ModelTypeEnum.IMAGE,
      from: 'openai',
      value: LLMs.GPT_IMAGE_1,
      providers: [LLMProviders.OPENAI],
      input_token_limit: 128000,
      output_token_limit: 0,
      icon_url: ModelIconMapping.openai,
      input_per_mil: 15,
      output_per_mil: 0,
      cached_input_per_mil: 0,
    },
  ],
  // https://docs.anthropic.com/en/docs/about-claude/models/all-models
  [
    LLMs.CLAUDE_35_SONNET,
    {
      title: 'Claude 3.5 Sonnet',
      short_title: 'Sonnet',
      type: ModelTypeEnum.CHAT,
      from: 'anthropic',
      value: LLMs.CLAUDE_35_SONNET,
      providers: [LLMProviders.ANTHROPIC, LLMProviders.AMAZON_BEDROCK, LLMProviders.AI_HUB_MIX],
      input_token_limit: 200000,
      output_token_limit: 8192,
      icon_url: ModelIconMapping.anthropic,
      input_per_mil: 3,
      output_per_mil: 15,
      cached_input_per_mil: 0,
    },
  ],
  // https://docs.anthropic.com/en/docs/about-claude/models/all-models
  [
    LLMs.CLAUDE_35_HAIKU,
    {
      title: 'Claude 3.5 Haiku',
      short_title: 'Haiku',
      type: ModelTypeEnum.CHAT,
      from: 'anthropic',
      value: LLMs.CLAUDE_35_HAIKU,
      providers: [LLMProviders.ANTHROPIC, LLMProviders.AMAZON_BEDROCK, LLMProviders.AI_HUB_MIX],
      input_token_limit: 200000,
      output_token_limit: 8192,
      icon_url: ModelIconMapping.anthropic,
      input_per_mil: 0.8,
      output_per_mil: 4,
      cached_input_per_mil: 0,
    },
  ],
  // https://docs.anthropic.com/en/docs/about-claude/models/all-models
  [
    LLMs.CLAUDE_37_SONNET,
    {
      title: 'Claude 3.7 Sonnet',
      short_title: 'Sonnet',
      type: ModelTypeEnum.CHAT,
      from: 'anthropic',
      value: LLMs.CLAUDE_37_SONNET,
      providers: [LLMProviders.ANTHROPIC, LLMProviders.AMAZON_BEDROCK, LLMProviders.AI_HUB_MIX],
      input_token_limit: 200000,
      output_token_limit: 64000,
      icon_url: ModelIconMapping.anthropic,
      input_per_mil: 3,
      output_per_mil: 15,
      cached_input_per_mil: 0,
    },
  ],
  // https://docs.anthropic.com/en/docs/about-claude/models/all-models
  [
    LLMs.CLAUDE_37_SONNET_THINKING,
    {
      title: 'Claude 3.7 Sonnet Thinking',
      short_title: 'Sonnet Thinking',
      type: ModelTypeEnum.REASONING,
      from: 'anthropic',
      value: LLMs.CLAUDE_37_SONNET_THINKING,
      providers: [LLMProviders.ANTHROPIC, LLMProviders.AMAZON_BEDROCK, LLMProviders.AI_HUB_MIX],
      input_token_limit: 200000,
      output_token_limit: 64000,
      icon_url: ModelIconMapping.anthropic,
      input_per_mil: 3,
      output_per_mil: 15,
      cached_input_per_mil: 0,
    },
  ],
  [
    LLMs.CLAUDE_4_SONNET,
    {
      title: 'Claude Sonnet 4',
      short_title: 'Sonnet',
      type: ModelTypeEnum.CHAT,
      from: 'anthropic',
      value: LLMs.CLAUDE_4_SONNET,
      providers: [LLMProviders.ANTHROPIC, LLMProviders.AMAZON_BEDROCK, LLMProviders.AI_HUB_MIX],
      input_token_limit: 200000,
      output_token_limit: 64000,
      icon_url: ModelIconMapping.anthropic,
      input_per_mil: 3,
      output_per_mil: 15,
      cached_input_per_mil: 0,
    },
  ],
  // https://api-docs.deepseek.com/quick_start/pricing
  [
    LLMs.DEEPSEEK_CHAT,
    {
      title: 'DeepSeek V3',
      short_title: 'DeepSeek V3',
      type: ModelTypeEnum.CHAT,
      from: 'deepseek',
      value: LLMs.DEEPSEEK_CHAT,
      providers: [LLMProviders.ANTHROPIC],
      input_token_limit: 64000,
      output_token_limit: 8000,
      icon_url: ModelIconMapping.deepseek,
      input_per_mil: 0,
      output_per_mil: 0,
      cached_input_per_mil: 0,
    },
  ],
  // https://api-docs.deepseek.com/quick_start/pricing
  [
    LLMs.DEEPSEEK_REASONER,
    {
      title: 'DeepSeek R1',
      short_title: 'DeepSeek R1',
      type: ModelTypeEnum.REASONING,
      from: 'deepseek',
      value: LLMs.DEEPSEEK_REASONER,
      providers: [LLMProviders.AMAZON_BEDROCK, LLMProviders.AI_HUB_MIX],
      input_token_limit: 64000,
      output_token_limit: 8000,
      icon_url: ModelIconMapping.deepseek,
      input_per_mil: 1.35,
      output_per_mil: 5,
      cached_input_per_mil: 0,
    },
  ],
  // https://cloud.google.com/vertex-ai/generative-ai/docs/models/gemini/2-5-pro?hl=en
  [
    LLMs.GEMINI_25_PRO,
    {
      title: 'Gemini 2.5 Pro',
      short_title: 'Gemini Pro',
      type: ModelTypeEnum.REASONING,
      from: 'google',
      value: LLMs.GEMINI_25_PRO,
      providers: [LLMProviders.VERTEXAI, LLMProviders.AI_HUB_MIX],
      input_token_limit: 1000000,
      output_token_limit: 65535,
      icon_url: ModelIconMapping.gemini,
      input_per_mil: 1.25,
      output_per_mil: 2.5,
      cached_input_per_mil: 0,
      extra: {
        fake_reasoning: true,
      },
    },
  ],

  // https://cloud.google.com/vertex-ai/generative-ai/docs/models/gemini/2-5-flash?hl=en
  [
    LLMs.GEMINI_25_FLASH,
    {
      title: 'Gemini 2.5 Flash',
      short_title: 'Gemini Flash',
      type: ModelTypeEnum.CHAT,
      from: 'google',
      value: LLMs.GEMINI_25_FLASH,
      providers: [LLMProviders.VERTEXAI, LLMProviders.AI_HUB_MIX],
      input_token_limit: 1000000,
      output_token_limit: 65535,
      icon_url: ModelIconMapping.gemini,
      input_per_mil: 0.15,
      output_per_mil: 0.6,
      cached_input_per_mil: 0,
    },
  ],
  // https://cloud.google.com/vertex-ai/generative-ai/docs/models/gemini/2-5-flash-lite?hl=en
  [
    LLMs.GEMINI_25_FLASH_LITE,
    {
      title: 'Gemini 2.5 Flash Lite',
      short_title: 'Gemini Flash Lite',
      type: ModelTypeEnum.CHAT,
      from: 'google',
      value: LLMs.GEMINI_25_FLASH_LITE,
      providers: [LLMProviders.VERTEXAI],
      input_token_limit: 1000000,
      output_token_limit: 65535,
      icon_url: ModelIconMapping.gemini,
      input_per_mil: 0.1,
      output_per_mil: 0.4,
      cached_input_per_mil: 0,
    },
  ],
  // https://cloud.google.com/vertex-ai/generative-ai/docs/models/gemini/2-0-flash?hl=en
  [
    LLMs.GEMINI_2_FLASH,
    {
      title: 'Gemini 2 Flash',
      short_title: 'Gemini Flash',
      type: ModelTypeEnum.CHAT,
      from: 'google',
      value: LLMs.GEMINI_2_FLASH,
      providers: [LLMProviders.VERTEXAI, LLMProviders.AI_HUB_MIX],
      input_token_limit: 1000000,
      output_token_limit: 8192,
      icon_url: ModelIconMapping.gemini,
      input_per_mil: 0.15,
      output_per_mil: 0.6,
      cached_input_per_mil: 0,
    },
  ],
  // https://cloud.google.com/vertex-ai/generative-ai/docs/models/gemini/2-0-flash-lite?hl=en
  [
    LLMs.GEMINI_2_FLASH_LITE,
    {
      title: 'Gemini 2 Flash Lite',
      short_title: 'Gemini Flash Lite',
      type: ModelTypeEnum.CHAT,
      from: 'google',
      value: LLMs.GEMINI_2_FLASH_LITE,
      providers: [LLMProviders.VERTEXAI, LLMProviders.AI_HUB_MIX],
      input_token_limit: 1000000,
      output_token_limit: 8192,
      icon_url: ModelIconMapping.gemini,
      input_per_mil: 0.075,
      output_per_mil: 0.3,
      cached_input_per_mil: 0,
    },
  ],
  // https://help.aliyun.com/zh/model-studio/models
  [
    LLMs.QWEN_TURBO,
    {
      title: 'Qwen Turbo',
      short_title: 'Qwen Turbo',
      type: ModelTypeEnum.CHAT,
      from: 'qwen',
      value: LLMs.QWEN_TURBO,
      providers: [LLMProviders.ALIYUN, LLMProviders.AI_HUB_MIX],
      input_token_limit: 1000000,
      output_token_limit: 8192,
      icon_url: ModelIconMapping.qwen,
      input_per_mil: 0.05,
      output_per_mil: 0.2,
      cached_input_per_mil: 0,
    },
  ],
  // https://help.aliyun.com/zh/model-studio/models
  [
    LLMs.QWEN_PLUS,
    {
      title: 'Qwen Plus',
      short_title: 'Qwen Plus',
      type: ModelTypeEnum.CHAT,
      from: 'qwen',
      value: LLMs.QWEN_PLUS,
      providers: [LLMProviders.ALIYUN, LLMProviders.AI_HUB_MIX],
      input_token_limit: 30000,
      output_token_limit: 8192,
      icon_url: ModelIconMapping.qwen,
      input_per_mil: 0.4,
      output_per_mil: 1.2,
      cached_input_per_mil: 0,
    },
  ],
  // https://help.aliyun.com/zh/model-studio/models
  [
    LLMs.QWEN_MAX,
    {
      title: 'Qwen Max',
      short_title: 'Qwen Max',
      type: ModelTypeEnum.CHAT,
      from: 'qwen',
      value: LLMs.QWEN_MAX,
      providers: [LLMProviders.ALIYUN, LLMProviders.AI_HUB_MIX],
      input_token_limit: 30000,
      output_token_limit: 8192,
      icon_url: ModelIconMapping.qwen,
      input_per_mil: 1.6,
      output_per_mil: 6.4,
      cached_input_per_mil: 0,
    },
  ],
]);
export const ASK_AI_MODELS = [
  LLMs.GPT_4O,
  LLMs.GPT_4O_MINI,
  LLMs.GPT_41,
  LLMs.CLAUDE_37_SONNET,
  LLMs.GEMINI_25_FLASH,
  LLMs.DEEPSEEK_REASONER,
  LLMs.O4_MINI,
  LLMs.GEMINI_25_PRO,
];

// @FIXME 发布前需要 double check!!!
export const DEFAULT_AI_CHAT_MODEL = LLMs.GPT_4O;
export const DEFAULT_AI_AGENT_MODEL = LLMs.CLAUDE_4_SONNET;
export const DEFAULT_TEMPERATURE = 0.5;

/**
 * 定义聊天的行为模式：标准聊天或各种特定任务的代理模式
 */
export enum ChatModeEnum {
  /** 普通聊天 */
  CHAT = 'chat',
  /** 特定功能：board 初始化 */
  NEW_BOARD = 'new_board',
  /** 特定功能：自定义 assistant 执行记录 */
  CUSTOM_ASSISTANT = 'custom_assistant',
  /** 特定功能：预览 assistant prompt */
  ASSISTANT_PREVIEW = 'assistant_preview',
}
export enum ChatOriginTypeEnum {
  SNIP = 'snip',
  BOARD = 'board',
  THOUGHT = 'thought',
  UNKNOWN = 'unknown',
  WEBPAGE = 'webpage',
}
export enum MessageModeEnum {
  /** ASK */
  ASK = 'ask',
  /** AGENT */
  AGENT = 'agent',
}
export enum MessageRoleEnum {
  USER = 'user',
  ASSISTANT = 'assistant',
}
export enum MessageStatusEnum {
  QUEUED = 'queued',
  ING = 'generating',
  DONE = 'success',
  ERROR = 'errored',
  ABORT = 'aborted',
}
export enum MessageContextEnum {
  AT = 'at',
  VIEW = 'view',
  EVENT = 'event',
  ORIGIN = 'origin',
  SELECTION = 'selection',
  MODEL = 'model',
  REASONING_METADATA = 'reasoning_metadata',
  BOARD_ID = 'board_id',
}
export enum MessageViewTypeEnum {
  BOARD = 'board',
}
export enum BoardPanelTypeEnum {
  SNIP = 'snip',
  CHAT = 'chat',
  NONE = 'none',
  THOUGHT = 'thought',
}
export enum MessageAtReferenceTypeEnum {
  SNIP = 'snip',
  /** @deprecated */
  CHAT = 'chat',
  THOUGHT = 'thought',
  BOARD_GROUP = 'board_group',
  BOARD = 'board',
  YOUMIND = 'youmind',
}
export enum MessageEventTypeEnum {
  SEARCH = 'search',
  GENERATE_SVG = 'generate_svg',
  GENERATE_IMAGE = 'generate_image',
  RESOLVE_LIBRARY = 'resolve_library',
  CREATE_SNIP_BY_URL = 'create_snip_by_url',
  NEW = 'new',
}
export enum SearchResultTypeEnum {
  SNIP = 'snip',
  CHAT = 'chat',
  BOARD = 'board',
  THOUGHT = 'thought',
  INTERNET_SEARCH_RESULT = 'internet_search_result',
}

export const ChatWebpageOriginSchema = z.object({
  type: z.literal(ChatOriginTypeEnum.WEBPAGE),
  url: z.string().url(),
  title: z.string().optional(),
  content: z.string().optional(),
  description: z.string().optional(),
});
export type ChatWebpageOrigin = z.infer<typeof ChatWebpageOriginSchema>;

export const ChatUnknownOriginSchema = z.object({
  type: z.literal(ChatOriginTypeEnum.UNKNOWN),
});
export const ChatSnipOriginSchema = z.object({
  type: z.literal(ChatOriginTypeEnum.SNIP),
  id: z.string().uuid(),
});
export type ChatSnipOrigin = z.infer<typeof ChatSnipOriginSchema>;
export const ChatBoardOriginSchema = z.object({
  type: z.literal(ChatOriginTypeEnum.BOARD),
  id: z.string().uuid(),
});
export type ChatBoardOrigin = z.infer<typeof ChatBoardOriginSchema>;
export const ChatThoughtOriginSchema = z.object({
  type: z.literal(ChatOriginTypeEnum.THOUGHT),
  id: z.string().uuid(),
});
export type ChatThoughtOrigin = z.infer<typeof ChatThoughtOriginSchema>;
export const ChatOriginSchema = z.discriminatedUnion('type', [
  ChatWebpageOriginSchema,
  ChatUnknownOriginSchema,
  ChatThoughtOriginSchema,
  ChatSnipOriginSchema,
  ChatBoardOriginSchema,
]);
export type ChatOrigin = z.infer<typeof ChatOriginSchema>;

export const MessageSelectionContextSchema = z.object({
  type: z.literal(MessageContextEnum.SELECTION),
  text: z.string(),
});
export type MessageSelectionContext = z.infer<typeof MessageSelectionContextSchema>;

export const MessageOriginContextSchema = z.object({
  type: z.literal(MessageContextEnum.ORIGIN),
  origin: ChatOriginSchema,
});
export type MessageOriginContext = z.infer<typeof MessageOriginContextSchema>;

// @deprecated
export const BoardPanelViewSchema = z.object({
  entity_type: z.nativeEnum(BoardPanelTypeEnum),
  entity_id: z.string().uuid().optional(),
});
export type BoardPanel = z.infer<typeof BoardPanelViewSchema>;
export const BoardViewSchema = z.object({
  type: z.literal(MessageViewTypeEnum.BOARD),
  panels: z.array(BoardPanelViewSchema),
});
export type BoardView = z.infer<typeof BoardViewSchema>;
export const MessageViewContextSchema = z.object({
  type: z.literal(MessageContextEnum.VIEW),
  view: BoardViewSchema,
});
export type MessageViewContext = z.infer<typeof MessageViewContextSchema>;

export const AtReferenceSchema = z.object({
  at_name: z.string(),
  entity_id: z.string(),
  entity_type: z.nativeEnum(MessageAtReferenceTypeEnum),
  content_with_selection: z.string().optional(),
});
export type AtReference = z.infer<typeof AtReferenceSchema>;
export const MessageAtContextSchema = z.object({
  type: z.literal(MessageContextEnum.AT),
  at_references: z.array(AtReferenceSchema),
});
export type MessageAtContext = z.infer<typeof MessageAtContextSchema>;
export const MessageBoardIdContextSchema = z.object({
  type: z.literal(MessageContextEnum.BOARD_ID),
  board_id: z.string(),
});
export type MessageBoardIdContext = z.infer<typeof MessageBoardIdContextSchema>;

export const MessageModelContextSchema = z.object({
  type: z.literal(MessageContextEnum.MODEL),
  model: z.string(),
});
export type MessageModelContext = z.infer<typeof MessageModelContextSchema>;

// @deprecated
export const MessageReasoningMetadataContextSchema = z.object({
  type: z.literal(MessageContextEnum.REASONING_METADATA),
  begin_at: z.string(),
  end_at: z.string(),
});
export type MessageReasoningMetadataContext = z.infer<typeof MessageReasoningMetadataContextSchema>;

export enum InternetSearchResultTypeEnum {
  WEBPAGE = 'webpage',
  VIDEO = 'video',
  EVENTS = 'events',
  SCHOLAR = 'scholar',
  APPLE_APP_STORE = 'apple_app_store',
}
export const InternetSearchResultSchema = z.object({
  url: z.string().url(),
  title: z.string(),
  favicon: z.string().url().optional(),
  site_name: z.string().optional(),
  related_chunk: z.string().optional(),
  time: z.string().optional(),
  author: z.string().optional(),
  address: z.string().optional(),
  type: z.nativeEnum(InternetSearchResultTypeEnum).optional(),
  images: z.array(z.object({ caption: z.string(), url: z.string().url() })).optional(),
  extra: z.any().optional(),
});
export type InternetSearchResult = z.infer<typeof InternetSearchResultSchema>;
export const SearchResultSchema = z.object({
  entity_type: z.nativeEnum(SearchResultTypeEnum),
  entity_id: z.string().optional(),
  entity_vo: z.any(), // SnipVO | ChatVO | BoardVO | ThoughtVO | InternetSearchResult
  related_chunk: z.string().optional(),
  // 在 library search 中，会把 snip/thought 归属的 board name 补充进来
  board_name: z.string().optional(),
});
export type SearchResult = z.infer<typeof SearchResultSchema>;
export const MessageSearchEventSchema = z.object({
  type: z.literal(MessageEventTypeEnum.SEARCH),
  query: z.string(),
  results: z.array(SearchResultSchema),
  time_of_action: z.string(),
});
export type MessageSearchEvent = z.infer<typeof MessageSearchEventSchema>;
export const MessageResolveLibraryEventSchema = MessageSearchEventSchema.extend({
  type: z.literal(MessageEventTypeEnum.RESOLVE_LIBRARY),
});
export type MessageResolveLibraryEvent = z.infer<typeof MessageResolveLibraryEventSchema>;
export const MessageGenerateSVGSchema = z.object({
  type: z.literal(MessageEventTypeEnum.GENERATE_SVG),
  svg: z.string(),
  image_url: z.string().url(),
  time_of_action: z.string(),
  results: z.array(z.string()).optional(),
});

export const MessageGenerateImageSchema = z.object({
  type: z.literal(MessageEventTypeEnum.GENERATE_IMAGE),
  image_urls: z.array(z.string().url()),
  time_of_action: z.string(),
  results: z.array(z.string()).optional(),
});

export const MessageCreateSingleUrlResultSchema = z.object({
  url: z.string(),
  success: z.boolean(),
  board_id: z.string().optional().nullable(),
  error: z.string().optional().nullable(),
  // 这里本来应该有 ThoughtVOSchema，但引入后有循环依赖，先凑活下
  snip: SnipVOSchema.optional().nullable(),
  type: z.enum(['snip', 'thought']).optional().nullable(),
  snip_predicted_type: z
    .enum([SnipTypeEnum.ARTICLE, SnipTypeEnum.VIDEO, SnipTypeEnum.VOICE])
    .optional()
    .nullable(),
  // 用户或AI提供的标题，用于在前端显示时优先使用
  provided_title: z.string().optional(),
  provided_album_url: z.string().optional(),
});

export type MessageCreateSingleUrlResult = z.infer<typeof MessageCreateSingleUrlResultSchema>;

export const MessageCreateSnipByUrlEventSchema = z.object({
  type: z.literal(MessageEventTypeEnum.CREATE_SNIP_BY_URL),
  time_of_action: z.string(),
  snipsResults: z.array(MessageCreateSingleUrlResultSchema),
  summary_message: z.string(),
});
export type MessageCreateSnipByUrlEvent = z.infer<typeof MessageCreateSnipByUrlEventSchema>;

export const MessageImageGenerateResultSchema = z.object({
  image_urls: z.array(z.string()),
  blurhash: z.string().optional(),
  width: z.number().optional(),
  height: z.number().optional(),
  quality: z.enum(['low', 'medium', 'high', 'auto']).optional(),
  snip: z
    .object({
      status: z.string(),
      board_id: z.string().optional().nullable(),
      vo: ImageVOSchema.optional().nullable(),
    })
    .optional(),
  history: z
    .array(
      z.object({
        image_url: z.string().url(),
        prompt: z.string(),
      }),
    )
    .optional(),
});
export type MessageImageGenerateResult = z.infer<typeof MessageImageGenerateResultSchema>;

export const MessageImageEditResultSchema = z.object({
  image_urls: z.array(z.string()),
  // blurhash: z.string().optional(),
  // width: z.number().optional(),
  // height: z.number().optional(),
  snip: z
    .object({
      status: z.string(),
      board_id: z.string().optional().nullable(),
      vo: ImageVOSchema.optional().nullable(),
    })
    .optional(),
});
export type MessageImageEditResult = z.infer<typeof MessageImageEditResultSchema>;

export const MessageDiagramGenerateResultSchema = z.object({
  svg: z.string(),
  image_url: z.string().url(),
  snip: z
    .object({
      status: z.string(),
      board_id: z.string().optional().nullable(),
      vo: ImageVOSchema.optional().nullable(),
    })
    .optional(),
});
export type MessageDiagramGenerateResult = z.infer<typeof MessageDiagramGenerateResultSchema>;

export const MessageAudioGenerateResultSchema = z.object({
  title: z.string(),
  audio_url: z.string(),
  subtitle_file: z.string().optional(),
  extra_info: z.any().optional(),
  album_url: z.string().optional(),
  snip: z
    .object({
      status: z.string(),
      board_id: z.string().optional().nullable(),
      vo: VoiceVOSchema.optional().nullable(),
    })
    .optional(),
});
export type MessageAudioGenerateResult = z.infer<typeof MessageAudioGenerateResultSchema>;

const MessageNewSchema = z.object({
  type: z.literal(MessageEventTypeEnum.NEW),
});

export const MessageEventSchema = z.discriminatedUnion('type', [
  MessageSearchEventSchema,
  MessageGenerateSVGSchema,
  MessageGenerateImageSchema,
  MessageResolveLibraryEventSchema,
  MessageCreateSnipByUrlEventSchema,
  MessageNewSchema,
]);
export type MessageEvent = z.infer<typeof MessageEventSchema>;
export const MessageEventContextSchema = z.object({
  type: z.literal(MessageContextEnum.EVENT),
  event: MessageEventSchema,
});
export type MessageEventContext = z.infer<typeof MessageEventContextSchema>;

export const MessageContextSchema = z.discriminatedUnion('type', [
  // UserMessageContext
  MessageSelectionContextSchema,
  MessageOriginContextSchema,
  MessageAtContextSchema,
  MessageBoardIdContextSchema,
  MessageViewContextSchema, // @deprecated
  // AssistantMessageContext
  MessageModelContextSchema, // @deprecated, move to model field
  MessageEventContextSchema, // @deprecated, move to completion block
  MessageReasoningMetadataContextSchema, // @deprecated, move to completion block
]);
export type MessageContext = z.infer<typeof MessageContextSchema>;

export enum EditCommandTypeEnum {
  SUGGEST_EDITS = 'suggest_edits',
  SUGGEST_SEARCH = 'suggest_search',
  SUGGEST_SUMMARY = 'suggest_summary',
  SUGGEST_NEXT_STEP = 'suggest_next_step',
  SUGGEST_GENERATE_TEXT = 'suggest_generate_text',
  SUGGEST_GENERATE_IMAGE = 'suggest_generate_image',
  SUGGEST_GENERATE_AUDIO = 'suggest_generate_audio',
  AUTO_ILLUSTRATION = 'auto_illustration',
  ADJUST_LENGTH = 'adjust_length',
  TRANSLATE = 'translate',
}

export enum AdjustLengthDirectionEnum {
  INCREASE = 'increase',
  DECREASE = 'decrease',
}

export enum TranslateModeEnum {
  REPLACE = 'replace',
  INSERT = 'insert',
  APPEND = 'append',
}

const SuggestEditsCommandSchema = z.object({
  type: z.literal(EditCommandTypeEnum.SUGGEST_EDITS),
});
const AutoIllustrationCommandSchema = z.object({
  type: z.literal(EditCommandTypeEnum.AUTO_ILLUSTRATION),
});
const AdjustLengthCommandSchema = z.object({
  type: z.literal(EditCommandTypeEnum.ADJUST_LENGTH),
  direction: z.nativeEnum(AdjustLengthDirectionEnum),
});
export type AdjustLengthCommand = z.infer<typeof AdjustLengthCommandSchema>;
const TranslateCommandSchema = z.object({
  type: z.literal(EditCommandTypeEnum.TRANSLATE),
  target_language: z.nativeEnum(LanguageEnum),
  mode: z.nativeEnum(TranslateModeEnum),
});
const SuggestSearchCommandSchema = z.object({
  type: z.literal(EditCommandTypeEnum.SUGGEST_SEARCH),
});
const SuggestSummaryCommandSchema = z.object({
  type: z.literal(EditCommandTypeEnum.SUGGEST_SUMMARY),
});
const SuggestNextStepCommandSchema = z.object({
  type: z.literal(EditCommandTypeEnum.SUGGEST_NEXT_STEP),
});
const SuggestGenerateTextCommandSchema = z.object({
  type: z.literal(EditCommandTypeEnum.SUGGEST_GENERATE_TEXT),
});
const SuggestGenerateImageCommandSchema = z.object({
  type: z.literal(EditCommandTypeEnum.SUGGEST_GENERATE_IMAGE),
});
const SuggestGenerateAudioCommandSchema = z.object({
  type: z.literal(EditCommandTypeEnum.SUGGEST_GENERATE_AUDIO),
});
export const SUGGEST_NEW_BOARD_COMMANDS = [
  SuggestSearchCommandSchema,
  SuggestSummaryCommandSchema,
  SuggestNextStepCommandSchema,
  SuggestGenerateTextCommandSchema,
  SuggestGenerateImageCommandSchema,
  SuggestGenerateAudioCommandSchema,
];

export const EditCommandSchema = z.union([
  SuggestEditsCommandSchema,
  SuggestSearchCommandSchema,
  SuggestSummaryCommandSchema,
  SuggestNextStepCommandSchema,
  SuggestGenerateTextCommandSchema,
  SuggestGenerateImageCommandSchema,
  SuggestGenerateAudioCommandSchema,
  AutoIllustrationCommandSchema,
  AdjustLengthCommandSchema,
  TranslateCommandSchema,
]);

export type EditCommand = z.infer<typeof EditCommandSchema>;

export function getCommandTitle(command: EditCommand) {
  switch (command.type) {
    case EditCommandTypeEnum.ADJUST_LENGTH:
      return command.direction === AdjustLengthDirectionEnum.INCREASE
        ? 'Make longer'
        : 'Make shorter';
    case EditCommandTypeEnum.TRANSLATE:
      return `Translate to ${LanguageNameMap[command.target_language]}`;
    default:
      return '';
  }
}

export enum CompletionBlockStatusEnum {
  QUEUED = 'queued',
  ING = 'generating',
  EXECUTING = 'executing',
  DONE = 'completed',
  ERROR = 'errored',
  ABORT = 'aborted',
}

export enum CompletionBlockTypeEnum {
  CONTENT = 'content',
  REASONING = 'reasoning',
  TOOL = 'tool',
}

// Tool schema for completion blocks
export const UseToolSchema = z.object({
  tool_id: z.string(),
  tool_name: z.string(),
  tool_arguments: z.record(z.string(), z.any()),
  tool_result: z.record(z.string(), z.any()).optional(),
  tool_response: z.string().optional(),
  tool_generate_elapsed_ms: z.number().optional(),
  tool_execute_elapsed_ms: z.number().optional(),
});
export type UseToolParam = z.infer<typeof UseToolSchema>;

export enum TOOL_TYPES {
  DIAGRAM_GENERATE = 'diagram_generate',
  IMAGE_GENERATE = 'image_generate',
  GOOGLE_SEARCH = 'google_search',
  LIBRARY_SEARCH = 'library_search',
  EDIT_THOUGHT = 'edit_thought',
  ORGANIZE_DIRECTORY_STRUCTURE = 'organize_directory_structure',
  CREATE_SNIP_BY_URL = 'create_snip_by_url',
  CREATE_BOARD = 'create_board',
  AUDIO_GENERATE = 'audio_generate',
  CREATE_PLAN = 'create_plan',
  UPDATE_PLAN = 'update_plan',
  BOARD_SEARCH = 'board_search',
}
// 接口都转为小驼峰了，这里保持一致
export enum ToolNames {
  // 内容生成
  DIAGRAM_GENERATE = 'diagramGenerate',
  IMAGE_GENERATE = 'imageGenerate',
  AUDIO_GENERATE = 'audioGenerate',
  // 搜索
  GOOGLE_SEARCH = 'googleSearch',
  BOARD_SEARCH = 'boardSearch',
  LIBRARY_SEARCH = 'librarySearch',
  // 编辑
  EDIT_THOUGHT = 'editThought',
  // 组织
  ORGANIZE_DIRECTORY_STRUCTURE = 'organizeDirectoryStructure',
  CREATE_SNIP_BY_URL = 'createSnipByUrl',
  CREATE_BOARD = 'createBoard',
}
export enum ImageGenerateToolSize {
  SQUARE = 'square',
  PORTRAIT = 'portrait',
  LANDSCAPE = 'landscape',
}
export enum ImageGenerateToolStyle {
  GHIBILI = 'ghibili',
  PIXAR = 'pixar',
  CARTOON = 'cartoon',
  PIXEL = 'pixel',
}

export enum ImageGenerateToolQuality {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
}

export const BaseCompletionBlockSchema = z.object({
  id: z.string(),
  created_at: z.date(),
  updated_at: z.date(),
  type: z.nativeEnum(CompletionBlockTypeEnum),
  status: z.nativeEnum(CompletionBlockStatusEnum),
  message_id: z.string(),
  extra: z.record(z.string(), z.any()).default({}),
});
export const CompletionContentBlockSchema = BaseCompletionBlockSchema.extend({
  type: z.literal(CompletionBlockTypeEnum.CONTENT),
  data: z.string(),
});
export const CompletionReasoningBlockSchema = BaseCompletionBlockSchema.extend({
  type: z.literal(CompletionBlockTypeEnum.REASONING),
  data: z.string(),
});
export const CompletionToolBlockSchema = BaseCompletionBlockSchema.extend({
  type: z.literal(CompletionBlockTypeEnum.TOOL),
  tool_id: z.string(),
  tool_name: z.string(),
  tool_arguments: z.record(z.string(), z.any()).default({}),
  tool_result: z.record(z.string(), z.any()).default({}),
  tool_response: z.string(),
  tool_generate_elapsed_ms: z.number().optional(),
  tool_execute_elapsed_ms: z.number().optional(),
});
export type ToolCompletionBlock = z.infer<typeof CompletionToolBlockSchema>;
export const CompletionBlockSchema = z.discriminatedUnion('type', [
  CompletionContentBlockSchema,
  CompletionReasoningBlockSchema,
  CompletionToolBlockSchema,
]);
export const PlainCompletionBlockSchema = z.discriminatedUnion('type', [
  CompletionContentBlockSchema.extend({
    created_at: z.string(),
    updated_at: z.string(),
  }),
  CompletionReasoningBlockSchema.extend({
    created_at: z.string(),
    updated_at: z.string(),
  }),
  CompletionToolBlockSchema.extend({
    created_at: z.string(),
    updated_at: z.string(),
  }),
]);

export const ChatSchema = z.object({
  id: z.string(),
  creator_id: z.string(),
  created_at: z.date(),
  updated_at: z.date(),
  title: z.string(),
  origin: ChatOriginSchema,
  board_id: z.string().optional(),
  mode: z.nativeEnum(ChatModeEnum),
  show_new_board_suggestion: z.boolean(),
  new_board_chat_id: z.string().optional(),
});
export const PlainChatSchema = ChatSchema.extend({
  created_at: z.string(),
  updated_at: z.string(),
});

export const UserMessageSchema = z.object({
  id: z.string(),
  chat_id: z.string(),
  created_at: z.date(),
  updated_at: z.date(),
  role: z.literal(MessageRoleEnum.USER),
  status: z.nativeEnum(MessageStatusEnum),
  content: z.string(),
  origin: ChatOriginSchema,
  selection: z.string().optional(),
  at_references: z.array(AtReferenceSchema).optional(),
  board_id: z.string().optional(),
  tools: UseToolSchema.optional(),
  command: EditCommandSchema.optional(),
  mode: z.nativeEnum(MessageModeEnum).optional().default(MessageModeEnum.ASK),
  shortcut: z
    .object({
      id: z.string().uuid(),
      name: z.string(),
    })
    .optional(),
});
export const AssistantMessageSchema = z.object({
  id: z.string(),
  chat_id: z.string(),
  created_at: z.date(),
  updated_at: z.date(),
  role: z.literal(MessageRoleEnum.ASSISTANT),
  status: z.nativeEnum(MessageStatusEnum),
  model: z.string(),
  trace_id: z.string(),
  blocks: z.array(CompletionBlockSchema),
  error: z.record(z.string(), z.any()).optional(),
  // @deprecated, use blocks instead
  content: z.string(),
  // @deprecated, use blocks instead
  reasoning: z.string(),
  // @deprecated, use blocks instead
  events: z.array(MessageEventSchema).optional(),
  // @deprecated, use blocks instead
  reasoning_begin_at: z.string().optional(),
  // @deprecated, use blocks instead
  reasoning_end_at: z.string().optional(),
});

export const MessageSchema = z.discriminatedUnion('role', [
  UserMessageSchema,
  AssistantMessageSchema,
]);

export const ChatDetailSchema = ChatSchema.extend({
  messages: z.array(MessageSchema),
});

export type Chat = z.infer<typeof ChatSchema>;
export type PlainChat = z.infer<typeof PlainChatSchema>;
export type CompletionContentBlock = z.infer<typeof CompletionContentBlockSchema>;
export type CompletionReasoningBlock = z.infer<typeof CompletionReasoningBlockSchema>;
export type CompletionToolBlock<
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  T extends Record<string, any> = Record<string, any>,
> = z.infer<typeof CompletionToolBlockSchema> & {
  tool_result: T;
  generate_elapsed_ms?: number;
  execute_elapsed_ms?: number;
};
export type CompletionBlock = z.infer<typeof CompletionBlockSchema>;
export type PlainCompletionBlock = z.infer<typeof PlainCompletionBlockSchema>;
export type UserMessage = z.infer<typeof UserMessageSchema>;
export type AssistantMessage = z.infer<typeof AssistantMessageSchema>;
export type Message = z.infer<typeof MessageSchema>;
export type ChatDetail = z.infer<typeof ChatDetailSchema>;

export enum StreamDataTypeEnum {
  DATA = 'data',
  CHUNKED_DATA = 'chunked_data',
  ERROR = 'error',
  CONTENT = 'content',
  CONTENT_STOP = 'content_stop',
  CONTENT_START = 'content_start',
  STATUS_UPDATE = 'status_update',
  TRACE_ID = 'trace_id',
}

export type StreamData<T> = {
  type: StreamDataTypeEnum.DATA;
  dataType: string;
  data: T;
  id: string;
};
export type StreamChunkedData<T> = {
  type: StreamDataTypeEnum.CHUNKED_DATA;
  parentDataType: string;
  parentId: string;
  chunkPath: string;
  chunkData: T;
};
export type StreamError = {
  type: StreamDataTypeEnum.ERROR;
  error: Error | RestErrorInfo;
};
export type StreamText = {
  type: StreamDataTypeEnum.CONTENT;
  data: string;
  contentType?: 'content' | 'reasoning' | 'error';
};
export type StreamTrace = {
  type: StreamDataTypeEnum.TRACE_ID;
  trace_id: string;
};
export type StreamTextStop = {
  type: StreamDataTypeEnum.CONTENT_STOP;
};
export type StreamTextStart = {
  type: StreamDataTypeEnum.CONTENT_START;
};
export type StreamStatusUpdate = {
  type: StreamDataTypeEnum.STATUS_UPDATE;
  status: MessageStatusEnum;
  message: string;
  event?: MessageEvent;
};

export type StreamMessage<T> =
  | StreamData<T>
  | StreamChunkedData<T>
  | StreamError
  | StreamTrace
  | StreamText
  | StreamTextStop
  | StreamTextStart
  | StreamStatusUpdate;

export enum CompletionStreamModeEnum {
  INSERT = 'insert',
  REPLACE = 'replace',
  APPEND_JSON = 'append_json',
  APPEND_STRING = 'append_string',
}
export type CompletionStreamSupportedDataType = 'Chat' | 'Message' | 'CompletionBlock';
export type CompletionStreamInsertChunk<T> = {
  mode: CompletionStreamModeEnum.INSERT;
  dataType: CompletionStreamSupportedDataType;
  data: T;
};
export type CompletionStreamReplaceChunk<T> = {
  mode: CompletionStreamModeEnum.REPLACE;
  targetType: CompletionStreamSupportedDataType;
  targetId: string;
  data: T;
  path: string;
};
export type CompletionStreamAppendStringChunk = {
  mode: CompletionStreamModeEnum.APPEND_STRING;
  targetType: CompletionStreamSupportedDataType;
  targetId: string;
  data: string;
  path: string;
};
export type CompletionStreamAppendJsonChunk = {
  mode: CompletionStreamModeEnum.APPEND_JSON;
  targetType: CompletionStreamSupportedDataType;
  targetId: string;
  data: string;
  path: string;
};

export type CompletionStreamChunk<T> =
  | CompletionStreamInsertChunk<T>
  | CompletionStreamReplaceChunk<T>
  | CompletionStreamAppendStringChunk
  | CompletionStreamAppendJsonChunk;

export type ListHistoryForChatAssistantParam = {
  query: {
    limit?: number;
    offset?: number;
    order?: 'asc' | 'desc';
  };
  user_id: string;
  board_id?: string;
  origin?: ChatOrigin;
};

export const DEFAULT_CHAT_TITLE = 'New chat';

export interface ToolCallContext {
  chat?: Chat;
  userId: string;
  params: Record<string, any>;
  completionBlock: any;
}

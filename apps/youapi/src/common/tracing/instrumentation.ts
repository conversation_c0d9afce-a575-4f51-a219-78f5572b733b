/**
 * Tracing 初始化
 * 从 youapp/src/lib/server/instrumentation/index.ts 和 youapp/src/lib/edge/instrumentation.ts 迁移而来
 * 适配到 NestJS 框架，包含两个自定义传播器：
 * 1. InitRequestStorePropagator - 初始化请求存储上下文
 */

import { getNodeAutoInstrumentations } from '@opentelemetry/auto-instrumentations-node';
import {
  CompositePropagator,
  W3CBaggagePropagator,
  W3CTraceContextPropagator,
} from '@opentelemetry/core';
import { OTLPTraceExporter } from '@opentelemetry/exporter-trace-otlp-http';
import { resourceFromAttributes } from '@opentelemetry/resources';
import { NodeSDK } from '@opentelemetry/sdk-node';
import { ATTR_SERVICE_NAME } from '@opentelemetry/semantic-conventions';
import {
  ATTR_DEPLOYMENT_ENVIRONMENT_NAME,
  ATTR_SERVICE_NAMESPACE,
} from '@opentelemetry/semantic-conventions/incubating';

import {
  getEnvironmentName,
  getServiceName,
  getServiceNamespace,
  InitRequestStorePropagator,
} from './otel';

const sdk = new NodeSDK({
  resource: resourceFromAttributes({
    [ATTR_SERVICE_NAME]: getServiceName(),
    [ATTR_SERVICE_NAMESPACE]: getServiceNamespace(),
    [ATTR_DEPLOYMENT_ENVIRONMENT_NAME]: getEnvironmentName(),
  }),
  instrumentations: [
    getNodeAutoInstrumentations({
      '@opentelemetry/instrumentation-undici': {
        enabled: true,
        responseHook: (span, requestInfo) => {
          // For undici, extract hostname and port from request URL
          let hostname = '';
          let port;

          if (requestInfo?.request?.origin) {
            try {
              const url = new URL(requestInfo.request.origin);
              hostname = url.hostname;
              port = url.port ? parseInt(url.port, 10) : url.protocol === 'https:' ? 443 : 80;
            } catch {
              // Fallback: ignore if URL parsing fails
            }
          }

          if (hostname) {
            const peerService =
              port && port !== 80 && port !== 443 ? `${hostname}:${port}` : hostname;

            span.setAttributes({
              'peer.service': peerService,
            });
          }
        },
      },
      '@opentelemetry/instrumentation-http': {
        enabled: true,
        responseHook: (span, response) => {
          // For HTTP, try to extract hostname and port from response or request
          let hostname = '';
          let port;

          // Check if response has socket and _httpMessage for accessing request details
          const req =
            (response as { req?: unknown })?.req ||
            (response as { _httpMessage?: unknown })?._httpMessage;
          if (req && typeof req === 'object') {
            // Extract from headers or URL
            const httpReq = req as {
              getHeader?: (name: string) => string;
              host?: string;
              protocol?: string;
            };
            const host = httpReq.getHeader?.('host') || httpReq.host;
            if (host) {
              const [extractedHostname, extractedPort] = host.split(':');
              hostname = extractedHostname;
              port = extractedPort
                ? parseInt(extractedPort, 10)
                : httpReq.protocol === 'https:'
                  ? 443
                  : 80;
            }
          }

          if (hostname) {
            const peerService =
              port && port !== 80 && port !== 443 ? `${hostname}:${port}` : hostname;

            span.setAttributes({
              'peer.service': peerService,
            });
          }
        },
      },
      '@opentelemetry/instrumentation-aws-sdk': {
        enabled: true,
      },
      '@opentelemetry/instrumentation-aws-lambda': {
        enabled: true,
      },
      '@opentelemetry/instrumentation-ioredis': {
        enabled: true,
      },
      '@opentelemetry/instrumentation-pino': {
        enabled: false,
      },
      '@opentelemetry/instrumentation-fs': {
        enabled: false,
      },
    }),
  ],
  // Set up composite propagators with both custom propagators
  textMapPropagator: new CompositePropagator({
    propagators: [
      new W3CTraceContextPropagator(),
      new InitRequestStorePropagator(),
      new W3CBaggagePropagator(),
    ],
  }),
  traceExporter: new OTLPTraceExporter({
    url: 'https://otlp.nr-data.net/v1/traces',
    headers: {
      'api-key': process.env.NEW_RELIC_LICENSE_KEY || '',
    },
  }),
});

export default sdk;

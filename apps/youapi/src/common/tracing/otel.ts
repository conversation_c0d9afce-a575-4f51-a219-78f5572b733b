/**
 * OpenTelemetry 相关配置和工具
 * 从 youapp/src/lib/common/otel.ts 迁移而来
 * 适配到 NestJS 框架
 */

import * as otel from '@opentelemetry/api';
import { W3CTraceContextPropagator } from '@opentelemetry/core';
import { ATTR_SERVICE_NAME } from '@opentelemetry/semantic-conventions';

export const REQUEST_STORE_KEY = otel.createContextKey('request-store');

export const FAKE_TRACE_PARENT_KEY = otel.createContextKey('fake-trace-parent');

export interface RichSpan extends otel.Span {
  resource: {
    _attributes: {
      [ATTR_SERVICE_NAME]: string;
    };
  };
}

export enum TracerEnum {
  YOUAPI = 'youapi',
  YOUWEB = 'youweb',
}

export function getServiceName(): string {
  return TracerEnum.YOUAPI;
}

export function getEnvironmentName(): string {
  const env =
    process.env.NODE_ENV === 'development' ? 'local' : (process.env.YOUMIND_ENV ?? 'local');
  return env;
}

export function getServiceVersion(): string {
  return process.env.npm_package_version || process.env.SERVICE_VERSION || '1.0.0';
}

export function getServiceNamespace(): string {
  return 'youmind';
}

export enum OTELKeyEnum {
  /*
   * semantic-conventions 包里似乎找不到标准的 traceId 和 userId 命名
   * 先按 OTEL 规范来定，同时要能够被 New Relic 识别：
   * https://opentelemetry.io/docs/specs/semconv/general/attribute-naming/
   */
  TRACE_ID = 'trace.id',
  USER_ID = 'user.id',
  SPACE_ID = 'space.id',
}

/**
 * InitRequestStorePropagator - 服务端请求存储传播器
 * 从 youapp/src/lib/server/instrumentation/index.ts 迁移而来
 * 在请求开始时初始化 RequestContext Store，以便在整个请求生命周期中共享数据
 */
export class InitRequestStorePropagator extends W3CTraceContextPropagator {
  extract(context: otel.Context, carrier: unknown, getter: otel.TextMapGetter): otel.Context {
    // 在一开始就初始化好 RequestContext Store，以便在后续整个请求中都可以共享
    const ctx = super.extract(context, carrier, getter);
    return ctx.setValue(REQUEST_STORE_KEY, {});
  }
}

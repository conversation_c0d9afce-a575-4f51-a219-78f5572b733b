# Chat 模块 @ApiProperty enumName 添加报告

基于对 chat 模块所有 DTO 文件的详细分析，按照指定规则对需要添加 `enumName` 的字段进行分类整理。

## 分析规则

1. **规则一**：enum 是一个具体的 enum 引用，需要添加 enumName = enum 的名字
2. **规则二**：enum 是一个数组字面量（如 `['option1', 'option2']`），保持原样不修改
3. **规则三**：其他特殊情况记录到此文档中

## 分析的文件

- `src/modules/chat/dto/chat.dto.ts`
- `src/modules/chat/dto/completion-stream.dto.ts`
- `src/modules/chat/dto/stream-message.dto.ts`
- `src/modules/chat/dto/v1/chat-v1.dto.ts`
- `src/modules/chat/dto/v2/chat-v2.dto.ts`
- `src/modules/chat/dto/shortcut.dto.ts`

## 规则一：需要添加 enumName 的字段（具体 enum 引用）

### 1. CompletionMessageDto.mode (completion-stream.dto.ts:187)

**当前状态**:
```typescript
@ApiProperty({ description: 'Message mode', enum: MessageModeEnum })
mode: MessageModeEnum;
```

**需要修改为**:
```typescript
@ApiProperty({ description: 'Message mode', enum: MessageModeEnum, enumName: 'MessageModeEnum' })
mode: MessageModeEnum;
```

### 2. SendMessageDto.chatModel (v1/chat-v1.dto.ts:211-214)

**当前状态**:
```typescript
@ApiPropertyOptional({ description: 'AI model to use', enum: LLMs })
@IsEnum(LLMs)
@IsOptional()
chatModel?: LLMs;
```

**需要修改为**:
```typescript
@ApiPropertyOptional({ description: 'AI model to use', enum: LLMs, enumName: 'LLMs' })
@IsEnum(LLMs)
@IsOptional()
chatModel?: LLMs;
```

### 3. RegenerateMessageDto.chatModel (v1/chat-v1.dto.ts:233-236)

**当前状态**:
```typescript
@ApiPropertyOptional({ description: 'AI model to use for regeneration', enum: LLMs })
@IsEnum(LLMs)
@IsOptional()
chatModel?: LLMs;
```

**需要修改为**:
```typescript
@ApiPropertyOptional({ description: 'AI model to use for regeneration', enum: LLMs, enumName: 'LLMs' })
@IsEnum(LLMs)
@IsOptional()
chatModel?: LLMs;
```

### 4. BaseSendMessageDto.chatModel (v2/chat-v2.dto.ts:439-442)

**当前状态**:
```typescript
@ApiPropertyOptional({ description: 'AI model to use', enum: LLMs })
@IsEnum(LLMs)
@IsOptional()
chatModel?: LLMs;
```

**需要修改为**:
```typescript
@ApiPropertyOptional({ description: 'AI model to use', enum: LLMs, enumName: 'LLMs' })
@IsEnum(LLMs)
@IsOptional()
chatModel?: LLMs;
```

### 5. BaseSendMessageDto.messageMode (v2/chat-v2.dto.ts:454-457)

**当前状态**:
```typescript
@ApiPropertyOptional({ description: 'Message mode', enum: MessageModeEnum })
@IsEnum(MessageModeEnum)
@IsOptional()
messageMode?: MessageModeEnum;
```

**需要修改为**:
```typescript
@ApiPropertyOptional({ description: 'Message mode', enum: MessageModeEnum, enumName: 'MessageModeEnum' })
@IsEnum(MessageModeEnum)
@IsOptional()
messageMode?: MessageModeEnum;
```

## 规则二：数组字面量，保持原样不修改

### 1. ToolOptionsDto.useTool (chat.dto.ts:83)

**当前状态**:
```typescript
@ApiProperty({ description: 'Tool use option', enum: ['required', 'auto', 'none'] })
@IsEnum(['required', 'auto', 'none'])
useTool: 'required' | 'auto' | 'none';
```

**分析**: 使用数组字面量，按规则二保持原样不修改。

### 2. ImageGenerateToolDto.size (chat.dto.ts:92)

**当前状态**:
```typescript
@ApiPropertyOptional({ description: 'Image size', enum: ['square', 'portrait', 'landscape'] })
@IsEnum(['square', 'portrait', 'landscape'])
@IsOptional()
size?: 'square' | 'portrait' | 'landscape';
```

**分析**: 使用数组字面量，按规则二保持原样不修改。

### 3. ImageGenerateToolDto.style (chat.dto.ts:98-103)

**当前状态**:
```typescript
@ApiPropertyOptional({
  description: 'Image style',
  enum: ['ghibili', 'pixar', 'cartoon', 'pixel'],
})
@IsEnum(['ghibili', 'pixar', 'cartoon', 'pixel'])
@IsOptional()
style?: 'ghibili' | 'pixar' | 'cartoon' | 'pixel';
```

**分析**: 使用数组字面量，按规则二保持原样不修改。

### 4. ImageGenerateToolDto.quality (chat.dto.ts:105)

**当前状态**:
```typescript
@ApiPropertyOptional({ description: 'Image quality', enum: ['low', 'medium', 'high'] })
@IsEnum(['low', 'medium', 'high'])
@IsOptional()
quality?: 'low' | 'medium' | 'high';
```

**分析**: 使用数组字面量，按规则二保持原样不修改。

### 5. StreamTextDto.contentType (stream-message.dto.ts:133-140)

**当前状态**:
```typescript
@ApiPropertyOptional({
  description: 'The type of content being streamed',
  enum: ['content', 'reasoning', 'error'],
  example: 'content',
})
@IsOptional()
@IsString()
contentType?: 'content' | 'reasoning' | 'error';
```

**分析**: 使用数组字面量，按规则二保持原样不修改。

## 规则三：特殊情况记录

### 1. 单值数组枚举（已正确使用，无需修改）

以下字段使用单值数组枚举，这是正确的用法，表示该字段只能是特定的单一枚举值：

- `ContentBlockV2Dto.type: enum: [CompletionBlockTypeEnum.CONTENT]` (v2/chat-v2.dto.ts:80)
- `ReasoningBlockV2Dto.type: enum: [CompletionBlockTypeEnum.REASONING]` (v2/chat-v2.dto.ts:91)
- `ToolBlockV2Dto.type: enum: [CompletionBlockTypeEnum.TOOL]` (v2/chat-v2.dto.ts:102)
- `UserMessageV1Dto.role: enum: [MessageRoleEnum.USER]` (v1/chat-v1.dto.ts:45)
- `AssistantMessageV1Dto.role: enum: [MessageRoleEnum.ASSISTANT]` (v1/chat-v1.dto.ts:82)
- `UserMessageV2Dto.role: enum: [MessageRoleEnum.USER]` (v2/chat-v2.dto.ts:143)
- `AssistantMessageV2Dto.role: enum: [MessageRoleEnum.ASSISTANT]` (v2/chat-v2.dto.ts:203)

这些字段无需修改，因为它们正确地表示了该字段的限制值。

### 2. CompletionStreamSupportedDataType 相关字段

以下字段使用数组字面量 `['Chat', 'Message', 'CompletionBlock']`，对应的类型是 `CompletionStreamSupportedDataType`：

- `CompletionStreamInsertChunkDto.dataType` (completion-stream.dto.ts:288-294)
- `CompletionStreamReplaceChunkDto.targetType` (completion-stream.dto.ts:320-326)
- `CompletionStreamAppendStringChunkDto.targetType` (completion-stream.dto.ts:361-366)
- `CompletionStreamAppendJsonChunkDto.targetType` (completion-stream.dto.ts:402-408)

**当前状态示例**:
```typescript
@ApiProperty({
  enum: ['Chat', 'Message', 'CompletionBlock'],
  description: 'The type of data being inserted',
  example: 'Message',
})
dataType: CompletionStreamSupportedDataType;
```

**分析**: 虽然字段类型使用了 `CompletionStreamSupportedDataType` 类型定义，但在装饰器中使用的是数组字面量，按规则二应保持原样不修改。

### 3. @IsIn 验证器的字段

在分析过程中没有发现使用 `@IsIn` 验证器的字段，所有相关字段都使用 `@IsEnum` 验证器。

## 已经正确设置 enumName 的字段

以下字段已经正确设置了 `enumName` 属性，无需修改：

1. `ChatOriginDto.type` - `enumName: 'ChatOriginTypeEnum'` (chat.dto.ts:22)
2. `AtReferenceDto.entityType` - `enumName: 'MessageAtReferenceTypeEnum'` (chat.dto.ts:68)
3. `EditCommandDto.type` - `enumName: 'EditCommandTypeEnum'` (chat.dto.ts:165)
4. `EditCommandDto.direction` - `enumName: 'AdjustLengthDirectionEnum'` (chat.dto.ts:173)
5. `EditCommandDto.targetLanguage` - `enumName: 'LanguageEnum'` (chat.dto.ts:182)
6. `EditCommandDto.mode` - `enumName: 'TranslateModeEnum'` (chat.dto.ts:191)
7. 所有 `CompletionStreamModeEnum` 相关字段 (completion-stream.dto.ts)
8. 所有 `StreamDataTypeEnum` 相关字段 (stream-message.dto.ts)
9. `CompletionBlockV2Dto.type` - `enumName: 'CompletionBlockTypeEnum'` (v2/chat-v2.dto.ts:57)
10. `CompletionBlockV2Dto.status` - `enumName: 'CompletionBlockStatusEnum'` (v2/chat-v2.dto.ts:65)
11. 多个 `MessageStatusEnum` 相关字段 (v1/chat-v1.dto.ts, v2/chat-v2.dto.ts)
12. 多个 `ChatModeEnum` 和 `MessageModeEnum` 相关字段 (v2/chat-v2.dto.ts)

## 总结

**需要添加 enumName 的字段总数**: 5个

1. `CompletionMessageDto.mode` - 添加 `enumName: 'MessageModeEnum'`
2. `SendMessageDto.chatModel` (v1) - 添加 `enumName: 'LLMs'`
3. `RegenerateMessageDto.chatModel` (v1) - 添加 `enumName: 'LLMs'`
4. `BaseSendMessageDto.chatModel` (v2) - 添加 `enumName: 'LLMs'`
5. `BaseSendMessageDto.messageMode` (v2) - 添加 `enumName: 'MessageModeEnum'`

**数组字面量字段**: 9个（按规则二保持原样）

**已正确设置的字段**: 20+个

**特殊情况**: 单值数组枚举（7个）和 CompletionStreamSupportedDataType 相关字段（4个）
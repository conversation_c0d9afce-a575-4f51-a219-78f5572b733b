'use client';

import clsx from 'clsx';
import Link from 'next/link';

import './index.module.css';
import { CopyButton } from '@repo/ui/components/custom/copy-button';
import { toast } from '@repo/ui/components/ui/sonner';
import { YouMind } from '../YouMind';

interface FooterProps {
  forceLight?: boolean;
}

export const Footer = ({ forceLight = false }: FooterProps) => {
  return (
    <div
      className={clsx(
        'footer mt-[64px] flex w-screen flex-col px-[8%] pb-[90px] font-sans-title text-sm md:mt-[160px] md:text-base',
      )}
    >
      <div className="mt-4 flex w-full flex-col md:flex-row">
        <div
          className={clsx(
            'mb-8 flex-1 md:mb-0',
            forceLight ? 'text-[rgba(0,0,0,0.88)]' : 'text-foreground',
          )}
        >
          <YouMind />
          <p className="mt-4 text-caption">© 2025 MIND MOTOR PTE. LTD.</p>
        </div>

        <div className="flex flex-shrink-0 gap-16">
          {/* AI 工具栏 */}
          <div className="w-[224px]">
            <div className={clsx('mb-[16px] text-base font-semibold')}>AI Tools</div>
            <div className="flex flex-col gap-[16px] text-sm">
              <Link
                href="/youtube-transcript-generator"
                className={clsx(forceLight ? 'text-[rgba(0,0,0,0.6)]' : 'text-caption')}
              >
                YouTube Transcript
              </Link>
              <Link
                href="/youtube-summary"
                className={clsx(forceLight ? 'text-[rgba(0,0,0,0.6)]' : 'text-caption')}
              >
                Youtube Summary
              </Link>
            </div>
          </div>
        </div>

        <div className="flex flex-shrink-0 gap-16">
          {/* 产品栏 */}
          <div className="w-[110px]">
            <div className={clsx('mb-[16px] text-base font-semibold')}>Product</div>
            <div className="flex flex-col gap-[16px] text-sm">
              <Link
                href="/use-cases"
                className={clsx(forceLight ? 'text-[rgba(0,0,0,0.6)]' : 'text-caption')}
              >
                Use cases
              </Link>
              <Link
                href="/pricing"
                className={clsx(forceLight ? 'text-[rgba(0,0,0,0.6)]' : 'text-caption')}
              >
                Pricing
              </Link>
              <Link
                href="/blog"
                className={clsx(forceLight ? 'text-[rgba(0,0,0,0.6)]' : 'text-caption')}
              >
                Blog
              </Link>
              <Link
                href="/updates"
                className={clsx(forceLight ? 'text-[rgba(0,0,0,0.6)]' : 'text-caption')}
              >
                Updates
              </Link>
            </div>
          </div>

          {/* 公司栏 */}
          <div>
            <div className={clsx('mb-[16px] text-base font-semibold')}>Company</div>
            <div className="flex flex-col gap-[16px] text-sm">
              <Link
                href="javascript:void(0)"
                onClick={() => {
                  toast('<EMAIL>', {
                    label: 'Copy',
                    duration: 5000,
                    onClick: () => {
                      navigator.clipboard.writeText('<EMAIL>');
                    },
                  });
                }}
                className={clsx(forceLight ? 'text-[rgba(0,0,0,0.6)]' : 'text-caption')}
              >
                Contact us
              </Link>
              <Link
                href="/privacy"
                className={clsx(forceLight ? 'text-[rgba(0,0,0,0.6)]' : 'text-caption')}
              >
                Privacy
              </Link>
              <Link
                href="/terms"
                className={clsx(forceLight ? 'text-[rgba(0,0,0,0.6)]' : 'text-caption')}
              >
                Terms
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

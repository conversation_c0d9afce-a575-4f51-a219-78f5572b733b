import { atom, useAtom } from 'jotai';
import { useCallback, useEffect, useRef } from 'react';

interface MindMaterialViewSize {
  width: number;
  height: number;
  isSmallScreen: boolean;
}

const SMALL_SCREEN_WIDTH = 508;

export const mindMaterialViewSizeAtom = atom<MindMaterialViewSize>({
  width: 0,
  height: 0,
  isSmallScreen: false,
});

// 用于标记是否已经初始化过监听器
const isInitializedAtom = atom(false);

export const useMindMaterialSize = () => {
  const [mindMaterialSize, setMindMaterialSize] = useAtom(mindMaterialViewSizeAtom);
  const [isInitialized, setIsInitialized] = useAtom(isInitializedAtom);
  const hasSetupRef = useRef(false);

  const updateSize = useCallback(() => {
    const element = document.querySelector('#mind-material-view');
    if (element) {
      const width = element.clientWidth || 0;
      const height = element.clientHeight || 0;
      const isSmallScreen = width < SMALL_SCREEN_WIDTH;

      const newSize = {
        width,
        height,
        isSmallScreen,
      };

      // 只在尺寸真正变化时更新，避免不必要的重渲染
      setMindMaterialSize((prevSize) => {
        if (
          prevSize.width !== newSize.width ||
          prevSize.height !== newSize.height ||
          prevSize.isSmallScreen !== newSize.isSmallScreen
        ) {
          return newSize;
        }
        return prevSize;
      });
    }
  }, [setMindMaterialSize]);

  useEffect(() => {
    // 防止重复初始化
    if (hasSetupRef.current || isInitialized) {
      console.warn('useMindMaterialSizeEffect should only be called once');
      return;
    }

    hasSetupRef.current = true;
    setIsInitialized(true);

    // 初始测量
    updateSize();

    // 监听窗口大小变化
    window.addEventListener('resize', updateSize);

    // 使用 ResizeObserver 监听元素尺寸变化（如果支持）
    let resizeObserver: ResizeObserver | null = null;
    const element = document.querySelector('#mind-material-view');

    if (element && 'ResizeObserver' in window) {
      resizeObserver = new ResizeObserver(updateSize);
      resizeObserver.observe(element);
    }

    // 清理函数
    return () => {
      window.removeEventListener('resize', updateSize);
      if (resizeObserver) {
        resizeObserver.disconnect();
      }
      hasSetupRef.current = false;
      setIsInitialized(false);
    };
  }, []);

  return mindMaterialSize;
};

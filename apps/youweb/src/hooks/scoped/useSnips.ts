import type { SnipVO } from '@repo/common/types/snip/types';
import { SnipTypeEnum } from '@repo/common/types/snip/types';
import { toast } from '@repo/ui/components/ui/sonner';
import { scopedAtom } from '@repo/ui/hooks/useScopedAtom';
import { snipDetailAtom, useSnipDetailListener } from '@repo/ui-business-snip';
import { produce } from 'immer';
import { atomWithStorage } from 'jotai/utils';
import { callHTTP } from '@/utils/callHTTP';
import { isMedium, isPodcast, isWikipedia, isYouTube } from '@/utils/utils';

export { snipDetailAtom, useSnipDetailListener };

function sortSnips(snips: SnipVO[]): SnipVO[] {
  return snips.sort((a, b) => {
    return a.created_at > b.created_at ? -1 : 1;
  });
}

export const snipCacheAtom = scopedAtom<Array<SnipVO>>([]);
export const snipsAtom = scopedAtom<SnipVO[]>((get) => {
  return get(snipCacheAtom);
});

export const snipInProgressCacheAtom = scopedAtom<Record<string, SnipVO>>({});
export const snipsInProgressAtom = scopedAtom<SnipVO[]>((get) => {
  const cache = get(snipInProgressCacheAtom);
  return sortSnips(Object.values(cache));
});

export const setSnipAtom = scopedAtom(null, (_, set, snip: SnipVO) => {
  set(
    snipCacheAtom,
    produce((draft: Array<SnipVO>) => {
      const index = draft.findIndex((b) => b.id === snip.id);
      if (index !== -1) {
        draft[index] = {
          ...draft[index],
          ...snip,
        };
      } else {
        draft.unshift(snip);
      }
    }),
  );
});

export const refreshSnipsAtom = scopedAtom(null, async (get, set, ids?: string[]) => {
  const param: { ids?: string[] } = {};
  if (ids?.length) {
    param.ids = ids;
  }
  const { data, error } = await callHTTP('/api/v1/listSnips', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(param),
  });
  if (error) {
    return;
  }
  if (data) {
    const snips = data as SnipVO[];
    // 原有的更新逻辑
    if (param.ids) {
      const prevSnips = get(snipCacheAtom);
      const updatedSnips = prevSnips.map((snip) => {
        const updatedSnip = snips.find((s) => s.id === snip.id);
        return updatedSnip || snip;
      });
      set(snipCacheAtom, updatedSnips);
    } else {
      set(snipCacheAtom, snips);
    }
  }
});

export const unshiftSnipsAtom = scopedAtom(null, (get, set, snips: SnipVO[]) => {
  set(snipCacheAtom, [...snips, ...get(snipsAtom)]);
});

// 删除某一个 snip
export const deleteSnipAtom = scopedAtom(null, async (get, set, id: string) => {
  set(
    snipCacheAtom,
    get(snipsAtom).filter((snip) => snip.id !== id),
  );
  const { error } = await callHTTP('/api/v1/deleteSnip', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ id }),
  });
  if (error) {
    // 这里不能 throw Error，外层 catch 不到
    toast('Failed to delete snip');
  }
});

export interface SnipVOWithTitle extends SnipVO {
  title?: string;
}

export interface ImageSnippetVO extends SnipVOWithTitle {
  description?: string;
  extracted_text?: string;
}

// useSnipDetailListener is used to listen to the changes of snipDetailAtom
// especially in board split view

export const snipDetailLoadingAtom = scopedAtom<boolean>(false);

export const refreshSnipDetailAtom = scopedAtom(null, async (get, set) => {
  const currentBoard = get(snipDetailAtom);
  set(snipDetailLoadingAtom, true);
  const { data } = await callHTTP('/api/v1/snip/getSnip', {
    method: 'POST',
    body: JSON.stringify({ id: currentBoard!.id }),
  });
  const snip = data as SnipVO;
  if (snip) {
    // check current atom id because user may have switched to other board
    if (snip.id === get(snipDetailAtom)!.id) {
      set(snipDetailAtom, snip);
    }
  }
  set(snipDetailLoadingAtom, false);
});

export const snipTypeAtom = atomWithStorage<
  | 'unused'
  | 'all'
  | 'webpages'
  | 'images'
  | 'snippets'
  | 'youtube'
  | 'medium'
  | 'wikipedia'
  | 'podcasts'
>('snipFilterType', 'unused');
export const filteredSnipsAtom = scopedAtom<SnipVO[]>((get) => {
  const snips = get(snipsAtom);
  const type = get(snipTypeAtom);
  if (!snips.length) {
    return [];
  }
  if (type === 'unused') {
    return snips.filter((snip) => !snip.board_ids?.length);
  } else if (type === 'all') {
    return snips;
  } else if (type === 'webpages') {
    return snips.filter(
      (snip) =>
        snip.type === SnipTypeEnum.ARTICLE ||
        snip.type === SnipTypeEnum.OTHER_WEBPAGE ||
        snip.type === SnipTypeEnum.UNKNOWN_WEBPAGE,
    );
  } else if (type === 'images') {
    return snips.filter((snip) => snip.type === SnipTypeEnum.IMAGE);
  } else if (type === 'snippets') {
    return snips.filter((snip) => snip.type === SnipTypeEnum.SNIPPET);
  } else if (type === 'youtube') {
    return snips.filter((snip) => isYouTube(snip));
  } else if (type === 'medium') {
    return snips.filter((snip) => isMedium(snip));
  } else if (type === 'podcasts') {
    return snips.filter((snip) => isPodcast(snip));
  } else if (type === 'wikipedia') {
    return snips.filter((snip) => isWikipedia(snip));
  }
  return [];
});

export const mobileSimpleViewAtom = scopedAtom<boolean>(false);

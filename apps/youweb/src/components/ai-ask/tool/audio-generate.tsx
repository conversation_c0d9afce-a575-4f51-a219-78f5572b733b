import { TOOL_TYPES } from '@repo/common/consts/tool/const';
import { BoardItemTypeEnum } from '@repo/common/types/board-item/types';
import type { MessageAudioGenerateResult } from '@repo/common/types/chat/enum';
import type { CompletionToolBlock } from '@repo/common/types/chat/types';
import { CompletionBlockStatusEnum } from '@repo/common/types/completion';
import type { SnipVO, SnipVoiceVO } from '@repo/common/types/snip/app-types';
import { SimpleAudioPlayer } from '@repo/ui/components/custom/simple-audio-player';
import { ButtonWithTooltip } from '@repo/ui/components/ui/button';
import { toast } from '@repo/ui/components/ui/sonner';

import { useAtom, useAtomValue, useSetAtom } from 'jotai';
import { Download, Edit2, FileAudio2 } from 'lucide-react';
import { useState } from 'react';
import { InsertToThoughtIcon } from '@/components/icon/insert-to-thought';
import { SaveAsSnipIcon } from '@/components/icon/save-as-snip';
import { updateChatMessageCompletionBlockAtom } from '@/hooks/ask-ai/useChat';
import { panelStateAtom } from '@/hooks/useBoardState';
import { boardDetailAtom, unshiftBoardItemsAtom } from '@/hooks/useBoards';
import { thoughtEditorAtom } from '@/hooks/useThought';
import { useTranslation } from '@/hooks/useTranslation';
import { apiClient, callAPI } from '@/utils/callHTTP';
import { cn } from '@/utils/utils';
import { ToolStatus, ToolTitle } from './card';
import type { TOOL_RENDERER } from './type';

const titles = {
  [CompletionBlockStatusEnum.ING]: 'Creating audio',
  [CompletionBlockStatusEnum.EXECUTING]: 'Creating audio',
  [CompletionBlockStatusEnum.DONE]: 'Created audio',
  [CompletionBlockStatusEnum.ERROR]: 'Failed to create audio',
};

export const GenerateAudioCard = ({
  block,
  variant,
}: {
  block: CompletionToolBlock;
  variant: 'small' | 'middle';
}) => {
  const { title, audio_url, extra_info, album_url, subtitle_file } =
    (block.tool_result as MessageAudioGenerateResult) || {};

  const updateChatMessageCompletionBlock = useSetAtom(updateChatMessageCompletionBlockAtom);
  const [isEditingTitle, setIsEditingTitle] = useState(false);

  const handleTitleChange = async (title: string) => {
    if (!title) {
      return;
    }

    const newToolResult = {
      ...block.tool_result,
      title,
    };
    await callAPI(
      apiClient.chatV1Api.updateCompletionBlock({
        block_id: block.id,
        tool_result: newToolResult,
      }),
    );
    updateChatMessageCompletionBlock({
      message_id: block.message_id,
      block: {
        ...block,
        tool_result: newToolResult,
      },
    });
    setIsEditingTitle(false);
  };

  return (
    <>
      <ToolTitle
        text={renderTitle(block)}
        icon={<FileAudio2 size={16} />}
        variant={variant}
        blockStatus={block.status}
      />

      {block.status !== CompletionBlockStatusEnum.ERROR && (
        <div className="relative border group rounded-xl border-muted bg-card">
          <SimpleAudioPlayer
            track={{
              src: audio_url,
              title,
              artist: '',
              album: album_url,
            }}
            isEditingTitle={isEditingTitle}
            onTitleChange={handleTitleChange}
            className="w-full h-full"
          />
          {block.status === CompletionBlockStatusEnum.DONE && (
            <AudioGenerateToolbar
              className="absolute hidden right-2 top-8 bg-card group-hover:flex"
              audio_url={audio_url}
              title={title}
              album_url={album_url}
              subtitle_file={subtitle_file}
              audio_format={extra_info?.audio_format}
              onEditTitle={() => setIsEditingTitle(true)}
            />
          )}
        </div>
      )}
    </>
  );
};

const renderTitle = (block: CompletionToolBlock) => {
  const { snip } = (block.tool_result as MessageAudioGenerateResult) || {};
  let toolTitle = titles[block.status] || titles[CompletionBlockStatusEnum.ING];
  if (snip) {
    const { status } = snip;
    if (status === 'processing') {
      toolTitle = 'Creating snip';
    } else if (status === 'success') {
      toolTitle = 'Created snip';
    } else if (status === 'failed') {
      toolTitle = 'Failed to create snip';
    }
  }
  return (
    <>
      <span>{toolTitle}</span>
      {snip?.vo?.title && (
        <span className="ml-2 text-xs text-caption-foreground">{snip?.vo?.title}</span>
      )}
    </>
  );
};

export const GenerateAudioTool: TOOL_RENDERER = {
  type: TOOL_TYPES.AUDIO_GENERATE,
  renderer: GenerateAudioCard,
  transformToMarkdown: (block: CompletionToolBlock) => {
    const { title, audio_url } = (block.tool_result as MessageAudioGenerateResult) || {};
    return `[${title}](${audio_url})`;
  },
  getToolTitle: (block) => {
    const { snip } = (block.tool_result as MessageAudioGenerateResult) || {};
    return (
      <ToolStatus
        logo={<FileAudio2 size={16} />}
        commandName={titles[block.status]}
        commandDescription={snip?.vo?.title}
      />
    );
  },
  needRefreshBoard: true,
};

export function AudioGenerateToolbar({
  audio_url,
  title,
  album_url,
  subtitle_file,
  transcript,
  audio_format,
  className,
  prevActions,
  moreActions,
  insertEnabled = true,
  editTitleEnabled = true,
  downloadEnabled = true,
  saveEnabled = true,
  onEditTitle,
}: {
  audio_url: string;
  title: string;
  album_url?: string;
  subtitle_file?: string;
  transcript?: string;
  audio_format: string;
  className?: string;
  prevActions?: React.ReactNode;
  moreActions?: React.ReactNode;
  insertEnabled?: boolean;
  editTitleEnabled?: boolean;
  downloadEnabled?: boolean;
  saveEnabled?: boolean;
  onEditTitle?: (e: React.MouseEvent) => void;
}) {
  const [isSavingSnip, setIsSavingSnip] = useState(false);
  const [isDownloading, setIsDownloading] = useState(false);
  const thoughtEditor = useAtomValue(thoughtEditorAtom);
  const panelState = useAtomValue(panelStateAtom);
  const thoughtOpened = panelState.panelData?.entity_type === 'thought';

  const boardDetail = useAtomValue(boardDetailAtom);
  const [, unshiftBoardItems] = useAtom(unshiftBoardItemsAtom);

  const { t } = useTranslation('Library.ImagePreview');

  const handleDownloadAudio = (e: React.MouseEvent) => {
    e.stopPropagation();

    setIsDownloading(true);
    fetch(audio_url)
      .then((response) => response.blob())
      .then((blob) => {
        const blobUrl = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = blobUrl;
        a.target = '_blank';
        a.download = `${title}.${audio_format}`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(blobUrl);
        setIsDownloading(false);
      });
  };

  const handleInsert = (e: React.MouseEvent) => {
    e.stopPropagation();

    if (thoughtEditor) {
      try {
        const alt = JSON.stringify({
          title,
          album: album_url || 'https://cdn.gooo.ai/assets/music.png',
        });
        thoughtEditor.commands.insertContentByMarkdown(
          `![${encodeURIComponent(alt)}](${audio_url})`,
        );
      } catch (_err) {
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      }
    }
  };

  const handleSaveSnip = async (e: React.MouseEvent) => {
    e.stopPropagation();

    setIsSavingSnip(true);
    const boardId = boardDetail?.id;
    try {
      const hash = audio_url.split('/').pop()?.replace(`.${audio_format}`, '') || '';
      const result = await callAPI(
        apiClient.snipApi.createVoice0({
          file: {
            name: title,
            hash,
            is_public: true,
            directory: 'gen-audio',
          },
          board_id: boardId,
          hero_image_url: album_url,
        }),
      );
      if (result.error) {
        toast('Failed to save audio');
      } else if (result.data) {
        const item = result.data as unknown as SnipVoiceVO;
        unshiftBoardItems([
          {
            ...item.board_item!,
            entity: item,
            entity_type: BoardItemTypeEnum.SNIP,
          },
        ]);
        toast('Saved audio');

        // patch transcript block
        if (subtitle_file || transcript) {
          await callAPI(
            apiClient.snipApi.patchVoiceTranscript({
              snip_id: (result.data as unknown as SnipVO).id,
              subtitle_file_url: subtitle_file,
              transcript,
            }),
          );
        }
      }
    } catch (_err) {
      toast('Failed to save audio');
    } finally {
      setIsSavingSnip(false);
    }
  };

  const handleEditTitle = (e: React.MouseEvent) => {
    onEditTitle?.(e);
  };

  return (
    <div className={cn('flex items-center justify-center px-2', className)}>
      {prevActions}
      {thoughtOpened && insertEnabled && (
        <ButtonWithTooltip
          tooltip={t('insert')}
          variant="icon"
          size="sm"
          className="text-muted-foreground"
          onClick={handleInsert}
        >
          <InsertToThoughtIcon />
        </ButtonWithTooltip>
      )}
      {saveEnabled && (
        <ButtonWithTooltip
          disabled={isSavingSnip}
          tooltip={t('save')}
          variant="icon"
          size="sm"
          className="text-muted-foreground"
          onClick={handleSaveSnip}
        >
          <SaveAsSnipIcon />
        </ButtonWithTooltip>
      )}
      {editTitleEnabled && (
        <ButtonWithTooltip
          tooltip={t('edit')}
          variant="icon"
          size="sm"
          className="text-muted-foreground"
          onClick={handleEditTitle}
        >
          <Edit2 />
        </ButtonWithTooltip>
      )}
      {downloadEnabled && (
        <ButtonWithTooltip
          disabled={isDownloading}
          tooltip={t('download')}
          variant="icon"
          size="sm"
          className="text-muted-foreground"
          onClick={handleDownloadAudio}
        >
          <Download />
        </ButtonWithTooltip>
      )}

      {moreActions}
    </div>
  );
}

import { Line } from '@nivo/line';
import { YouMindLogo, YouMindTextLogo } from '@repo/ui/components/custom/logo';
import { Subscription } from '@repo/ui/components/custom/subscription';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@repo/ui/components/ui/dialog';
import { Battery, ChevronUp } from 'lucide-react';
import { useEffect, useRef, useState } from 'react';
import {
  SubscriptionBillingIntervalEnum,
  SubscriptionProductTierEnum,
} from '@/typings/subscription';
import { cn } from '@/utils/utils';
import { Progress } from './ui/progress';

export const CreditsUsage: React.FC<CreditsUsageProps> = ({ credits, usage }) => {
  const [collapsed, setCollapsed] = useState(true);
  const chartRef = useRef<HTMLDivElement>(null);
  const [chartWidth, setChartWidth] = useState(0);

  useEffect(() => {
    if (chartRef.current) {
      setChartWidth(chartRef.current.offsetWidth);
    }
  }, [chartRef]);
  return (
    <div className="px-2 mb-2" ref={chartRef}>
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Dialog>
            <DialogTrigger>
              <Battery size="16" />
            </DialogTrigger>
            <DialogContent className="max-w-[100vw] h-full rounded-none flex flex-col gap-0 overflow-y-auto">
              <DialogHeader>
                <DialogTitle className="flex items-center mb-4">
                  <YouMindLogo size={16} />
                  <YouMindTextLogo className="ml-[6px]" size={16} />
                </DialogTitle>
              </DialogHeader>
              <div className="headline1 text-center mb-6">Upgrade your plan</div>
              <Subscription
                productTier={SubscriptionProductTierEnum.unknownDefaultOpenApi}
                billingInterval={SubscriptionBillingIntervalEnum.monthly}
              />
            </DialogContent>
          </Dialog>
          <span>Credits usage</span>
          <span className="text-caption footnote">Refresh in 21 days</span>
        </div>
        <div
          className="flex items-center text-caption footnote cursor-pointer"
          onClick={() => setCollapsed(!collapsed)}
        >
          <span>{credits}</span>/<span>{usage}</span>
          <ChevronUp size="16" className={cn(collapsed ? 'rotate-180' : '')} />
        </div>
      </div>
      <Progress className="h-1" value={(usage / credits) * 100} />
      {!collapsed && (
        <div className="w-full">
          <Line
            animate
            curve="monotoneX"
            data={[
              {
                color: 'hsl(33, 70%, 50%)',
                data: [
                  {
                    color: 'hsl(205, 70%, 50%)',
                    x: 'HN',
                    y: 59,
                  },
                  {
                    color: 'hsl(113, 70%, 50%)',
                    x: 'CA',
                    y: 59,
                  },
                  {
                    color: 'hsl(179, 70%, 50%)',
                    x: 'CR',
                    y: 50,
                  },
                  {
                    color: 'hsl(187, 70%, 50%)',
                    x: 'BD',
                    y: 20,
                  },
                  {
                    color: 'hsl(207, 70%, 50%)',
                    x: 'IT',
                    y: 44,
                  },
                  {
                    color: 'hsl(30, 70%, 50%)',
                    x: 'IE',
                    y: 40,
                  },
                  {
                    color: 'hsl(129, 70%, 50%)',
                    x: 'NF',
                    y: 25,
                  },
                  {
                    color: 'hsl(161, 70%, 50%)',
                    x: 'PW',
                    y: 16,
                  },
                  {
                    color: 'hsl(237, 70%, 50%)',
                    x: 'CV',
                    y: 21,
                  },
                  {
                    color: 'hsl(44, 70%, 50%)',
                    x: 'LV',
                    y: 10,
                  },
                  {
                    color: 'hsl(202, 70%, 50%)',
                    x: 'BM',
                    y: 52,
                  },
                  {
                    color: 'hsl(198, 70%, 50%)',
                    x: 'PL',
                    y: 5,
                  },
                  {
                    color: 'hsl(108, 70%, 50%)',
                    x: 'BB',
                    y: 16,
                  },
                  {
                    color: 'hsl(78, 70%, 50%)',
                    x: 'JP',
                    y: 27,
                  },
                  {
                    color: 'hsl(275, 70%, 50%)',
                    x: 'EH',
                    y: 34,
                  },
                  {
                    color: 'hsl(320, 70%, 50%)',
                    x: 'FR',
                    y: 7,
                  },
                  {
                    color: 'hsl(147, 70%, 50%)',
                    x: 'DE',
                    y: 16,
                  },
                  {
                    color: 'hsl(297, 70%, 50%)',
                    x: 'GS',
                    y: 12,
                  },
                ],
                id: 'whisky',
              },
              {
                color: 'hsl(123, 70%, 50%)',
                data: [
                  {
                    color: 'hsl(330, 70%, 50%)',
                    x: 'HN',
                    y: 21,
                  },
                  {
                    color: 'hsl(61, 70%, 50%)',
                    x: 'CA',
                    y: 12,
                  },
                  {
                    color: 'hsl(199, 70%, 50%)',
                    x: 'CR',
                    y: 19,
                  },
                  {
                    color: 'hsl(240, 70%, 50%)',
                    x: 'BD',
                    y: 20,
                  },
                  {
                    color: 'hsl(113, 70%, 50%)',
                    x: 'IT',
                    y: 14,
                  },
                  {
                    color: 'hsl(105, 70%, 50%)',
                    x: 'IE',
                    y: 56,
                  },
                  {
                    color: 'hsl(34, 70%, 50%)',
                    x: 'NF',
                    y: 8,
                  },
                  {
                    color: 'hsl(127, 70%, 50%)',
                    x: 'PW',
                    y: 15,
                  },
                  {
                    color: 'hsl(103, 70%, 50%)',
                    x: 'CV',
                    y: 3,
                  },
                  {
                    color: 'hsl(101, 70%, 50%)',
                    x: 'LV',
                    y: 4,
                  },
                  {
                    color: 'hsl(107, 70%, 50%)',
                    x: 'BM',
                    y: 24,
                  },
                  {
                    color: 'hsl(337, 70%, 50%)',
                    x: 'PL',
                    y: 24,
                  },
                  {
                    color: 'hsl(146, 70%, 50%)',
                    x: 'BB',
                    y: 56,
                  },
                  {
                    color: 'hsl(212, 70%, 50%)',
                    x: 'JP',
                    y: 54,
                  },
                  {
                    color: 'hsl(5, 70%, 50%)',
                    x: 'EH',
                    y: 18,
                  },
                  {
                    color: 'hsl(270, 70%, 50%)',
                    x: 'FR',
                    y: 7,
                  },
                  {
                    color: 'hsl(266, 70%, 50%)',
                    x: 'DE',
                    y: 48,
                  },
                  {
                    color: 'hsl(285, 70%, 50%)',
                    x: 'GS',
                    y: 56,
                  },
                ],
                id: 'rhum',
              },
              {
                color: 'hsl(264, 70%, 50%)',
                data: [
                  {
                    color: 'hsl(74, 70%, 50%)',
                    x: 'HN',
                    y: 59,
                  },
                  {
                    color: 'hsl(157, 70%, 50%)',
                    x: 'CA',
                    y: 13,
                  },
                  {
                    color: 'hsl(59, 70%, 50%)',
                    x: 'CR',
                    y: 43,
                  },
                  {
                    color: 'hsl(24, 70%, 50%)',
                    x: 'BD',
                    y: 41,
                  },
                  {
                    color: 'hsl(254, 70%, 50%)',
                    x: 'IT',
                    y: 0,
                  },
                  {
                    color: 'hsl(330, 70%, 50%)',
                    x: 'IE',
                    y: 58,
                  },
                  {
                    color: 'hsl(323, 70%, 50%)',
                    x: 'NF',
                    y: 15,
                  },
                  {
                    color: 'hsl(132, 70%, 50%)',
                    x: 'PW',
                    y: 58,
                  },
                  {
                    color: 'hsl(207, 70%, 50%)',
                    x: 'CV',
                    y: 11,
                  },
                  {
                    color: 'hsl(91, 70%, 50%)',
                    x: 'LV',
                    y: 21,
                  },
                  {
                    color: 'hsl(213, 70%, 50%)',
                    x: 'BM',
                    y: 44,
                  },
                  {
                    color: 'hsl(343, 70%, 50%)',
                    x: 'PL',
                    y: 16,
                  },
                  {
                    color: 'hsl(81, 70%, 50%)',
                    x: 'BB',
                    y: 4,
                  },
                  {
                    color: 'hsl(36, 70%, 50%)',
                    x: 'JP',
                    y: 51,
                  },
                  {
                    color: 'hsl(53, 70%, 50%)',
                    x: 'EH',
                    y: 55,
                  },
                  {
                    color: 'hsl(189, 70%, 50%)',
                    x: 'FR',
                    y: 39,
                  },
                  {
                    color: 'hsl(96, 70%, 50%)',
                    x: 'DE',
                    y: 19,
                  },
                  {
                    color: 'hsl(226, 70%, 50%)',
                    x: 'GS',
                    y: 36,
                  },
                ],
                id: 'gin',
              },
              {
                color: 'hsl(175, 70%, 50%)',
                data: [
                  {
                    color: 'hsl(256, 70%, 50%)',
                    x: 'HN',
                    y: 22,
                  },
                  {
                    color: 'hsl(245, 70%, 50%)',
                    x: 'CA',
                    y: 51,
                  },
                  {
                    color: 'hsl(142, 70%, 50%)',
                    x: 'CR',
                    y: 19,
                  },
                  {
                    color: 'hsl(245, 70%, 50%)',
                    x: 'BD',
                    y: 52,
                  },
                  {
                    color: 'hsl(33, 70%, 50%)',
                    x: 'IT',
                    y: 58,
                  },
                  {
                    color: 'hsl(222, 70%, 50%)',
                    x: 'IE',
                    y: 35,
                  },
                  {
                    color: 'hsl(3, 70%, 50%)',
                    x: 'NF',
                    y: 21,
                  },
                  {
                    color: 'hsl(332, 70%, 50%)',
                    x: 'PW',
                    y: 23,
                  },
                  {
                    color: 'hsl(41, 70%, 50%)',
                    x: 'CV',
                    y: 31,
                  },
                  {
                    color: 'hsl(83, 70%, 50%)',
                    x: 'LV',
                    y: 50,
                  },
                  {
                    color: 'hsl(63, 70%, 50%)',
                    x: 'BM',
                    y: 40,
                  },
                  {
                    color: 'hsl(113, 70%, 50%)',
                    x: 'PL',
                    y: 8,
                  },
                  {
                    color: 'hsl(296, 70%, 50%)',
                    x: 'BB',
                    y: 17,
                  },
                  {
                    color: 'hsl(212, 70%, 50%)',
                    x: 'JP',
                    y: 30,
                  },
                  {
                    color: 'hsl(274, 70%, 50%)',
                    x: 'EH',
                    y: 4,
                  },
                  {
                    color: 'hsl(267, 70%, 50%)',
                    x: 'FR',
                    y: 52,
                  },
                  {
                    color: 'hsl(243, 70%, 50%)',
                    x: 'DE',
                    y: 47,
                  },
                  {
                    color: 'hsl(110, 70%, 50%)',
                    x: 'GS',
                    y: 38,
                  },
                ],
                id: 'vodka',
              },
              {
                color: 'hsl(178, 70%, 50%)',
                data: [
                  {
                    color: 'hsl(347, 70%, 50%)',
                    x: 'HN',
                    y: 52,
                  },
                  {
                    color: 'hsl(48, 70%, 50%)',
                    x: 'CA',
                    y: 41,
                  },
                  {
                    color: 'hsl(347, 70%, 50%)',
                    x: 'CR',
                    y: 48,
                  },
                  {
                    color: 'hsl(138, 70%, 50%)',
                    x: 'BD',
                    y: 18,
                  },
                  {
                    color: 'hsl(280, 70%, 50%)',
                    x: 'IT',
                    y: 3,
                  },
                  {
                    color: 'hsl(171, 70%, 50%)',
                    x: 'IE',
                    y: 5,
                  },
                  {
                    color: 'hsl(306, 70%, 50%)',
                    x: 'NF',
                    y: 14,
                  },
                  {
                    color: 'hsl(275, 70%, 50%)',
                    x: 'PW',
                    y: 40,
                  },
                  {
                    color: 'hsl(37, 70%, 50%)',
                    x: 'CV',
                    y: 0,
                  },
                  {
                    color: 'hsl(112, 70%, 50%)',
                    x: 'LV',
                    y: 14,
                  },
                  {
                    color: 'hsl(144, 70%, 50%)',
                    x: 'BM',
                    y: 60,
                  },
                  {
                    color: 'hsl(168, 70%, 50%)',
                    x: 'PL',
                    y: 36,
                  },
                  {
                    color: 'hsl(344, 70%, 50%)',
                    x: 'BB',
                    y: 16,
                  },
                  {
                    color: 'hsl(81, 70%, 50%)',
                    x: 'JP',
                    y: 12,
                  },
                  {
                    color: 'hsl(205, 70%, 50%)',
                    x: 'EH',
                    y: 40,
                  },
                  {
                    color: 'hsl(185, 70%, 50%)',
                    x: 'FR',
                    y: 3,
                  },
                  {
                    color: 'hsl(331, 70%, 50%)',
                    x: 'DE',
                    y: 52,
                  },
                  {
                    color: 'hsl(296, 70%, 50%)',
                    x: 'GS',
                    y: 60,
                  },
                ],
                id: 'cognac',
              },
            ]}
            enableSlices="x"
            enableTouchCrosshair
            height={400}
            initialHiddenIds={['cognac']}
            legends={[
              {
                anchor: 'top-left',
                direction: 'row',
                itemHeight: 20,
                itemWidth: 80,
                toggleSerie: true,
                translateY: -30,
              },
            ]}
            margin={{
              bottom: 20,
              left: 40,
              right: 20,
              top: 60,
            }}
            width={chartWidth - 16}
            yScale={{
              stacked: true,
              type: 'linear',
            }}
          />
        </div>
      )}
    </div>
  );
};

interface CreditsUsageProps {
  credits: number;
  usage: number;
}

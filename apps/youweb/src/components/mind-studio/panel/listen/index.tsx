import { BoardItemTypeEnum } from '@repo/common/types/board-item/types';
import { LLMs } from '@repo/common/types/chat/enum';
import { MINIMAX_EMOTIONS, MINIMAX_VOICES, OPENAI_VOICES } from '@repo/common/types/listen';
import { SnipImageVO, SnipVO } from '@repo/common/types/snip/app-types';
import { SnipTypeEnum } from '@repo/common/types/snip/types';
import { Avatar, AvatarFallback, AvatarImage } from '@repo/ui/components/ui/avatar';
import { Button, ButtonWithTooltip } from '@repo/ui/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@repo/ui/components/ui/dropdown-menu';
import { Input } from '@repo/ui/components/ui/input';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@repo/ui/components/ui/tooltip';
import { useAtomValue } from 'jotai';
import { ChevronDown, Loader2, LoaderCircle, PlayIcon, TrashIcon } from 'lucide-react';
import { useCallback, useEffect, useState } from 'react';
import { AudioGenerateToolbar } from '@/components/ai-ask/tool/audio-generate';
import { DevOnly } from '@/components/DevOnly';
import { Voice } from '@/components/icon/action/voice';
import { GenerateAudioIcon } from '@/components/icon/generate-audio';
import { StarIcon } from '@/components/icon/snip';
import { formatTime } from '@/components/simple-audio-player';
import { getActiveBoardTreeItemAtom } from '@/hooks/useBoardMenuTree';
import usePolling from '@/hooks/usePolling';
import { useTranslation } from '@/hooks/useTranslation';
import { BoardTreeItem } from '@/typings/board-item';
import { PlaylistItem, PlaylistItemStatusEnum } from '@/typings/playlist-item';
import {
  getImageAndTitleFromBoardItem,
  IMAGE_ICON,
  WEBSITE_ICON,
} from '@/utils/board/getImageAndTitleFromBoardItem';
import { cn } from '@/utils/utils';
import { PlayerState, PlayModeEnum, useBoardPlaylistItems } from './hooks/useBoardPlaylistItems';
import { PlaylistPlayer } from './player';

const models = [
  LLMs.SPEECH_01_HD,
  LLMs.SPEECH_02_HD,
  LLMs.TTS_1,
  LLMs.TTS_1_HD,
  LLMs.GPT_4O_MINI_TTS,
] as const;

const modelVoiceMap = {
  [LLMs.SPEECH_01_HD]: MINIMAX_VOICES,
  [LLMs.SPEECH_02_HD]: MINIMAX_VOICES,
  [LLMs.TTS_1]: OPENAI_VOICES,
  [LLMs.TTS_1_HD]: OPENAI_VOICES,
  [LLMs.GPT_4O_MINI_TTS]: OPENAI_VOICES,
};

const modelEmotionMap = {
  [LLMs.SPEECH_01_HD]: MINIMAX_EMOTIONS,
  [LLMs.SPEECH_02_HD]: MINIMAX_EMOTIONS,
  [LLMs.TTS_1]: undefined,
  [LLMs.TTS_1_HD]: undefined,
  [LLMs.GPT_4O_MINI_TTS]: undefined,
};

export interface ListenPanelProps extends React.HTMLAttributes<HTMLDivElement> {}

export default function ListenPanel({ className }: ListenPanelProps) {
  const { t } = useTranslation('Action.Listen');

  const item = useAtomValue(getActiveBoardTreeItemAtom);
  const { title, imageUrl } = item
    ? getImageAndTitleFromBoardItem(item as BoardTreeItem)
    : { title: '', imageUrl: '' };

  // 目前仅支持 Article Snip 和 Thought 生成音频
  const generateEnabled =
    (item?.entity_type === BoardItemTypeEnum.SNIP &&
      (item?.entity as SnipVO).type === SnipTypeEnum.ARTICLE) ||
    item?.entity_type === BoardItemTypeEnum.THOUGHT;

  const {
    createPlaylistItem,
    deletePlaylistItem,
    updatePlaylistItemTitle,
    refreshBoardPlaylistItems,
    audioPlayerState,
    currentPlaylistItem,
    currentPlaylistIndex,
    playMode,
    playByIndex,
    playNext,
    playPrevious,
    setPlayMode,
    hasNext,
    hasPrevious,
    playlistItems,
  } = useBoardPlaylistItems();

  const [editingId, setEditingId] = useState<string | null>(null);
  const [isEditingTitleLoading, setIsEditingTitleLoading] = useState(false);

  const [isDeleting, setIsDeleting] = useState(false);
  const [isGenerating, setIsGenerating] = useState(false);
  const [selectedModel, setSelectedModel] = useState<(typeof models)[number]>(LLMs.SPEECH_02_HD);
  const [selectedVoice, setSelectedVoice] = useState<string>('Wise_Woman');
  const [selectedEmotion, setSelectedEmotion] = useState<string>('neutral');

  const fetchingIds =
    playlistItems
      .filter((entity) => entity.status === PlaylistItemStatusEnum.generating)
      .map((playlistItem) => playlistItem.id) || [];

  const fetchData = useCallback(async () => {
    if (fetchingIds.length) {
      await refreshBoardPlaylistItems(false);
    }
  }, [undefined, refreshBoardPlaylistItems, fetchingIds.length]);

  const { stopPolling, startPolling } = usePolling(fetchData, 3000);

  const [autoPlay, setAutoPlay] = useState(false);

  useEffect(() => {
    if (fetchingIds.length) {
      startPolling();
    } else {
      stopPolling();
    }

    return () => {
      stopPolling();
    };
  }, [startPolling, stopPolling, fetchingIds.length]);

  const handleGenerate = async () => {
    setIsGenerating(true);
    await createPlaylistItem(item!, selectedModel, selectedVoice, selectedEmotion);
    setIsGenerating(false);
  };

  const handleModeChange = () => {
    // 循环切换播放模式
    const modes = [
      PlayModeEnum.SEQUENTIAL,
      PlayModeEnum.REPEAT_ALL,
      PlayModeEnum.REPEAT_ONE,
      PlayModeEnum.RANDOM,
    ];
    const currentModeIndex = modes.indexOf(playMode);
    const nextModeIndex = (currentModeIndex + 1) % modes.length;
    setPlayMode(modes[nextModeIndex]);
  };

  const handleDelete = async (id: string) => {
    setIsDeleting(true);
    try {
      await deletePlaylistItem(id);
    } catch (error) {
      console.error(error);
    } finally {
      setIsDeleting(false);
    }
  };

  const renderWelcome = () => {
    return (
      <>
        <div className="flex items-center justify-center">
          <Logo />
        </div>
        <p className="text-center body-strong">{t('description')}</p>
        <div className="flex flex-col items-center justify-center px-5 py-4 mt-10 shadow-sm rounded-xl bg-card">
          {generateEnabled ? (
            <div className="flex items-center">
              <img
                src={imageUrl}
                alt="icon"
                className="mr-2 h-4 w-4 flex-shrink-0 rounded-[4px] object-cover"
                onError={(e) => {
                  if ((item?.entity as SnipImageVO).type === SnipTypeEnum.IMAGE) {
                    e.currentTarget.src = IMAGE_ICON;
                  } else {
                    e.currentTarget.src = WEBSITE_ICON;
                  }
                }}
              />
              <p className="body line-clamp-1">{title}</p>
            </div>
          ) : (
            <p className="text-center body">{t('generateLimit')}</p>
          )}
          <Button
            disabled={!generateEnabled || isGenerating}
            className="body-strong group relative mt-4 h-8 w-full overflow-hidden rounded-full border border-border bg-brand p-[1.5px] text-card hover:bg-brand/90 hover:text-card"
            onClick={handleGenerate}
          >
            <div className="relative flex items-center justify-center w-full rounded-full h-7 bg-brand">
              <StarIcon animate={false} size={16} className="mr-1" />
              {t('generate')}
            </div>
          </Button>
        </div>
      </>
    );
  };

  const renderPlaylistItem = (playlistItem: PlaylistItem, index: number) => {
    const isCurrentlyPlaying = currentPlaylistIndex === index;
    const isSuccess = playlistItem.status === PlaylistItemStatusEnum.success;
    const isFailed = playlistItem.status === PlaylistItemStatusEnum.failed;
    return (
      <div
        key={playlistItem.id}
        className={cn(
          'group/avatar group flex cursor-pointer items-center rounded-[10px] py-2 pl-2 transition-colors hover:bg-muted',
        )}
        onClick={() => {
          if (index !== currentPlaylistIndex) {
            setAutoPlay(true);
          }
          playByIndex(index);
        }}
      >
        <div className="flex items-center flex-1 h-full">
          {playlistItem.status === PlaylistItemStatusEnum.generating && (
            <>
              <div className="flex items-center justify-center w-16 h-16 mr-4 rounded-lg bg-muted text-caption">
                <LoaderCircle size={24} className="animate-spin" />
              </div>
              <div className="flex flex-col flex-1">
                <p className="body-strong mb-[6px]">Generating...</p>
                <p className="footnote text-caption">{t('generating')}</p>
              </div>
            </>
          )}
          {isSuccess && (
            <>
              <Avatar className="relative w-16 h-16 mr-4 rounded-lg">
                <AvatarImage
                  src={playlistItem.album_cover_url || 'https://cdn.gooo.ai/assets/music.png'}
                  alt="track album"
                  className="rounded-lg"
                />
                <AvatarFallback className="rounded-md"></AvatarFallback>

                {isCurrentlyPlaying ? (
                  <div className="absolute inset-0 flex items-center justify-center rounded-lg bg-black/50">
                    <Voice
                      animate={audioPlayerState.playerState === PlayerState.PLAYING}
                      size={20}
                      className="text-white"
                    />
                  </div>
                ) : (
                  <div className="absolute inset-0 flex items-center justify-center transition-opacity duration-200 rounded-lg opacity-0 bg-black/50 group-hover/avatar:opacity-100">
                    <PlayIcon size={20} fill="white" className="text-white" />
                  </div>
                )}
              </Avatar>

              <div className="flex-1 min-w-0">
                {editingId === playlistItem.id ? (
                  <Input
                    size={12}
                    autoFocus
                    className="h-6 w-full rounded-[8px] border bg-card px-[7px] text-sm text-foreground"
                    defaultValue={playlistItem.title}
                    onBlur={async (e) => {
                      const newTitle = e.target.value;
                      if (newTitle === playlistItem.title) {
                        setEditingId(null);
                        return;
                      }

                      setIsEditingTitleLoading(true);
                      setEditingId(null);
                      try {
                        await updatePlaylistItemTitle(playlistItem.id, newTitle);
                      } catch (error) {
                        console.error(error);
                      } finally {
                        setIsEditingTitleLoading(false);
                      }
                    }}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter' && !e.nativeEvent.isComposing) {
                        e.preventDefault();
                        e.currentTarget.blur();
                      }
                    }}
                  />
                ) : (
                  <div className="mb-1 body-strong text-ellipsis-line-1">
                    {playlistItem.title}
                    {isEditingTitleLoading && (
                      <Loader2 className="inline-block w-3 h-3 ml-1 animate-spin" size={8} />
                    )}
                  </div>
                )}

                <div className="footnote text-caption">{formatTime(playlistItem.duration)}</div>
              </div>
            </>
          )}
          {isFailed && (
            <>
              <div className="flex items-center justify-center w-16 h-16 mr-4 rounded-lg bg-muted text-caption"></div>
              <div className="flex flex-col flex-1">
                <p className="body-strong mb-[6px]">Failed to generate audio</p>
                <p className="footnote text-caption">Please try again later</p>
              </div>
            </>
          )}
        </div>

        <AudioGenerateToolbar
          className="hidden group-hover:flex"
          audio_url={playlistItem.play_url}
          title={playlistItem.title}
          album_url={playlistItem.album_cover_url}
          transcript={playlistItem.transcript}
          audio_format={'mp3'}
          downloadEnabled={isSuccess}
          saveEnabled={isSuccess}
          insertEnabled={isSuccess}
          editTitleEnabled={isSuccess}
          onEditTitle={(e) => {
            e.stopPropagation();
            e.preventDefault();
            setEditingId(playlistItem.id);
          }}
          moreActions={
            <ButtonWithTooltip
              disabled={isDeleting}
              tooltip={t('delete')}
              variant="icon"
              size="sm"
              className="text-muted-foreground"
              onClick={(e) => {
                e.stopPropagation();
                handleDelete(playlistItem.id);
              }}
            >
              <TrashIcon />
            </ButtonWithTooltip>
          }
        />
      </div>
    );
  };

  const renderGenerateButton = () => {
    return (
      <div className="flex items-center gap-1">
        <DevOnly>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm" className="w-[120px]">
                <span className="mr-1">{selectedModel}</span>
                <ChevronDown />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-32">
              {models.map((model) => (
                <DropdownMenuItem
                  key={selectedModel}
                  onClick={() => {
                    setSelectedModel(model);
                    setSelectedVoice(modelVoiceMap[model][0]);
                    if (modelEmotionMap[model]) {
                      setSelectedEmotion(modelEmotionMap[model][0]);
                    }
                  }}
                  className={cn('cursor-pointer', selectedModel === model && 'bg-muted')}
                >
                  {model}
                </DropdownMenuItem>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm" className="w-[120px]">
                <span className="mr-1">{selectedVoice}</span>
                <ChevronDown />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-32">
              {modelVoiceMap[selectedModel].map((voice) => (
                <DropdownMenuItem
                  key={voice}
                  onClick={() => setSelectedVoice(voice)}
                  className={cn('cursor-pointer', selectedVoice === voice && 'bg-muted')}
                >
                  {voice}
                </DropdownMenuItem>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>
          {modelEmotionMap[selectedModel] && (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" size="sm" className="footnote w-[80px]">
                  <span className="mr-1">{selectedEmotion}</span>
                  <ChevronDown />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-32">
                {modelEmotionMap[selectedModel].map((emotion) => (
                  <DropdownMenuItem
                    key={emotion}
                    onClick={() => setSelectedEmotion(emotion)}
                    className={cn('cursor-pointer', selectedEmotion === emotion && 'bg-muted')}
                  >
                    {emotion}
                  </DropdownMenuItem>
                ))}
              </DropdownMenuContent>
            </DropdownMenu>
          )}
        </DevOnly>

        <Button
          variant="outline"
          size="sm"
          className="w-[100px] rounded-full"
          disabled={!item || !generateEnabled || isGenerating}
          onClick={handleGenerate}
        >
          {isGenerating ? (
            <LoaderCircle className="mr-1 animate-spin" />
          ) : (
            <GenerateAudioIcon className="mr-1" />
          )}
          {t('generate')}
        </Button>
      </div>
    );
  };

  return (
    <div className={cn('flex h-full flex-col', className)}>
      {playlistItems.length > 0 ? (
        <>
          <div className="flex items-center justify-between mb-3">
            <p className="body-strong text-caption">Playlist</p>
            {generateEnabled ? (
              renderGenerateButton()
            ) : (
              <TooltipProvider delayDuration={100}>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <div className="inline-block">{renderGenerateButton()}</div>
                  </TooltipTrigger>
                  <TooltipContent>{t('generateLimit')}</TooltipContent>
                </Tooltip>
              </TooltipProvider>
            )}
          </div>
          <div className="ml-[-8px] flex min-h-0 flex-1 flex-col gap-y-1 overflow-y-auto">
            {playlistItems.map((playlistItem, index) => renderPlaylistItem(playlistItem, index))}
          </div>
          {currentPlaylistItem && (
            <div className="relative mt-2">
              <PlaylistPlayer
                className="shadow-md rounded-xl bg-card"
                controls={{
                  onPrevious: playPrevious,
                  onNext: playNext,
                  onModeChange: handleModeChange,
                  hasPrevious,
                  hasNext,
                  playMode,
                }}
                onPlayerReady={(sound) => {
                  if (autoPlay) {
                    sound.play();
                    setAutoPlay(false);
                  }
                }}
              />
            </div>
          )}
        </>
      ) : (
        renderWelcome()
      )}
    </div>
  );
}

function Logo({ animate = true }: { animate?: boolean }) {
  return (
    <svg
      width="209"
      height="209"
      viewBox="0 0 209 209"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g filter="url(#filter0_f_803_26739)">
        <ellipse
          cx="104.368"
          cy="104.5"
          rx="47.3684"
          ry="47.5"
          fill="url(#paint0_linear_803_26739)"
        />
      </g>
      <g filter="url(#filter1_f_803_26739)">
        <ellipse
          cx="119.526"
          cy="106.4"
          rx="45.4737"
          ry="45.6"
          fill="url(#paint1_linear_803_26739)"
        />
      </g>
      <g
        style={{
          mixBlendMode: 'plus-lighter',
        }}
        filter="url(#filter2_d_803_26739)"
      >
        <path
          d="M112.894 89.2998C113.941 89.2998 114.79 90.148 114.79 91.1943V117.805C114.79 118.851 113.941 119.7 112.894 119.7C111.848 119.7 111 118.851 111 117.805V91.1943C111 90.1482 111.848 89.3 112.894 89.2998ZM103.421 94.0498C104.467 94.0498 105.316 94.898 105.316 95.9443V113.055C105.316 114.101 104.467 114.95 103.421 114.95C102.374 114.95 101.526 114.101 101.526 113.055V95.9443C101.526 94.8982 102.375 94.05 103.421 94.0498ZM122.368 94.0498C123.414 94.0498 124.263 94.898 124.263 95.9443V113.055C124.263 114.101 123.414 114.95 122.368 114.95C121.322 114.95 120.473 114.101 120.473 113.055V95.9443C120.474 94.8982 121.322 94.05 122.368 94.0498ZM93.947 99.75C94.9934 99.75 95.8424 100.598 95.8425 101.645V107.355C95.8424 108.402 94.9934 109.25 93.947 109.25C92.9009 109.25 92.0526 108.402 92.0525 107.355V101.645C92.0526 100.598 92.9009 99.7502 93.947 99.75ZM131.842 99.75C132.888 99.75 133.737 100.598 133.737 101.645V107.355C133.737 108.402 132.888 109.25 131.842 109.25C130.795 109.25 129.947 108.402 129.947 107.355V101.645C129.947 100.598 130.795 99.7502 131.842 99.75ZM84.4734 101.65C85.5198 101.65 86.3679 102.498 86.3679 103.545V105.455C86.3679 106.502 85.5198 107.35 84.4734 107.35C83.427 107.349 82.5789 106.501 82.5789 105.455V103.545C82.5789 102.499 83.427 101.651 84.4734 101.65ZM141.316 101.65C142.362 101.651 143.211 102.499 143.211 103.545V105.455C143.211 106.501 142.362 107.349 141.316 107.35C140.27 107.35 139.422 106.502 139.422 105.455V103.545C139.422 102.498 140.27 101.65 141.316 101.65ZM74.9998 102.6C76.0462 102.6 76.8943 103.454 76.8943 104.5C76.8943 105.546 76.0462 106.4 74.9998 106.4C73.9534 106.4 73.1053 105.546 73.1052 104.5C73.1052 103.454 73.9534 102.6 74.9998 102.6ZM150.79 102.6C151.836 102.6 152.684 103.454 152.684 104.5C152.684 105.546 151.836 106.4 150.79 106.4C149.743 106.4 148.895 105.546 148.895 104.5C148.895 103.454 149.743 102.6 150.79 102.6Z"
          fill="url(#paint2_linear_803_26739)"
          fillOpacity="0.88"
          shapeRendering="crispEdges"
        />
      </g>
      <path
        d="M128.148 84.1611C128.774 83.8759 129.275 83.3732 129.559 82.7458L130.895 79.8L132.23 82.7458C132.514 83.3732 133.016 83.8759 133.641 84.1611L136.579 85.5L133.641 86.839C133.016 87.1242 132.514 87.6269 132.23 88.2543L130.895 91.2L129.559 88.2543C129.275 87.6269 128.774 87.1242 128.148 86.839L125.21 85.5L128.148 84.1611Z"
        fill="white"
      >
        {animate && (
          <animate
            attributeType="XML"
            attributeName="fill-opacity"
            values="0.6;0;0.6"
            keyTimes="0;0.5;1"
            dur="1.2s"
            repeatCount="indefinite"
          ></animate>
        )}
      </path>
      <defs>
        <filter
          id="filter0_f_803_26739"
          x="0.157894"
          y="0.157894"
          width="208.421"
          height="208.684"
          filterUnits="userSpaceOnUse"
          colorInterpolationFilters="sRGB"
        >
          <feFlood floodOpacity="0" result="BackgroundImageFix" />
          <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape" />
          <feGaussianBlur stdDeviation="28.4211" result="effect1_foregroundBlur_803_26739" />
        </filter>
        <filter
          id="filter1_f_803_26739"
          x="36.158"
          y="22.9053"
          width="166.737"
          height="166.989"
          filterUnits="userSpaceOnUse"
          colorInterpolationFilters="sRGB"
        >
          <feFlood floodOpacity="0" result="BackgroundImageFix" />
          <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape" />
          <feGaussianBlur stdDeviation="18.9474" result="effect1_foregroundBlur_803_26739" />
        </filter>
        <filter
          id="filter2_d_803_26739"
          x="67.421"
          y="84.563"
          width="90.9475"
          height="41.7688"
          filterUnits="userSpaceOnUse"
          colorInterpolationFilters="sRGB"
        >
          <feFlood floodOpacity="0" result="BackgroundImageFix" />
          <feColorMatrix
            in="SourceAlpha"
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
            result="hardAlpha"
          />
          <feOffset dy="0.947368" />
          <feGaussianBlur stdDeviation="2.84211" />
          <feComposite in2="hardAlpha" operator="out" />
          <feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.2 0" />
          <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_803_26739" />
          <feBlend
            mode="normal"
            in="SourceGraphic"
            in2="effect1_dropShadow_803_26739"
            result="shape"
          />
        </filter>
        <linearGradient
          id="paint0_linear_803_26739"
          x1="104.368"
          y1="57"
          x2="104.368"
          y2="152"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#FF4949" />
          <stop offset="1" stopColor="#4A26D9" />
        </linearGradient>
        <linearGradient
          id="paint1_linear_803_26739"
          x1="119.526"
          y1="60.8"
          x2="119.526"
          y2="152"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="white" />
          <stop offset="1" stopColor="#7421FB" />
        </linearGradient>
        <linearGradient
          id="paint2_linear_803_26739"
          x1="73.1052"
          y1="107.35"
          x2="153.632"
          y2="107.35"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="white" stopOpacity="0.06" />
          <stop offset="0.212814" stopColor="white" stopOpacity="0.4" />
          <stop offset="0.501625" stopColor="white" stopOpacity="0.8" />
          <stop offset="0.78112" stopColor="white" stopOpacity="0.4" />
          <stop offset="1" stopColor="white" stopOpacity="0.06" />
        </linearGradient>
      </defs>
    </svg>
  );
}

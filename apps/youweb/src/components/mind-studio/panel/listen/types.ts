import type { Howl } from 'howler';
import type { BoardTreeItem } from '@/typings/board-item';
import type { PlaylistItem } from '@/typings/playlist-item';

// 播放模式枚举
export enum PlayModeEnum {
  SEQUENTIAL = 'sequential', // 顺序播放
  RANDOM = 'random', // 随机播放
  REPEAT_ONE = 'repeat_one', // 单曲循环
  REPEAT_ALL = 'repeat_all', // 列表循环
}

// 播放器状态枚举
export enum PlayerState {
  PREPARE,
  READY,
  ENDED,
  PAUSE,
  PLAYING,
}

// 音频播放器状态接口
export interface AudioPlayerState {
  sound: Howl | null;
  playerState: PlayerState;
  progressValue: number;
  currentPos: number;
  duration: number;
}

// 播放列表状态接口
export interface PlaylistState {
  items: PlaylistItem[];
  currentIndex: number;
  playMode: PlayModeEnum;
  audioPlayerState: AudioPlayerState;
}

// 播放控制接口
export interface PlayControlActions {
  play: () => void;
  pause: () => void;
  stop: () => void;
  toggle: () => void;
  next: () => void;
  previous: () => void;
  playByIndex: (index: number) => void;
  playById: (id: string) => void;
  setPlayMode: (mode: PlayModeEnum) => void;
  seek: (percentage: number) => void;
}

// 播放列表管理接口
export interface PlaylistActions {
  refresh: (reset?: boolean) => Promise<void>;
  create: (
    item: BoardTreeItem | null | undefined,
    model?: string,
    voice?: string,
    emotion?: string,
  ) => Promise<void>;
  delete: (id: string) => Promise<void>;
  updateTitle: (id: string, title: string) => Promise<void>;
}

// 播放器回调接口
export interface PlayerCallbacks {
  onPlayerReady?: (sound: Howl) => void;
  onPlayerLoadFailed?: () => void;
  onPlayerPaused?: () => void;
  onPlayerPlaying?: () => void;
  onPlayerEnded?: () => void;
  onPlayerTimeUpdate?: (time: number) => void;
}

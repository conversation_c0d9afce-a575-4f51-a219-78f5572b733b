import { atom, useAtom } from 'jotai';
import { isArray } from 'lodash-es';
import { useCallback, useMemo } from 'react';
import { BoardItemTypeEnum } from '@/components/ask-ai-editor/const';
import { boardDetailAtom } from '@/hooks/useBoards';
import type { BoardTreeItem } from '@/typings/board-item';
import { PlaylistItemSourceEntityTypeEnum } from '@/typings/playlist-item';
import { apiClient, callAPI } from '@/utils/callHTTP';
import { DEFAULT_AUDIO_PLAYER_STATE, DEFAULT_PLAYLIST_STATE } from '../constants';
import {
  AudioPlayerState,
  PlayControlActions,
  PlayerState,
  PlaylistActions,
  PlaylistState,
  PlayModeEnum,
} from '../types';
import {
  getNextIndex,
  getPreviousIndex,
  hasNext as hasNextUtil,
  hasPrevious as hasPreviousUtil,
} from '../utils/playModeUtils';

// 全局状态原子
export const boardPlaylistItemsMapAtom = atom<Record<string, PlaylistState>>({});
const loadingAtom = atom(false);

export const useBoardPlaylistItems = () => {
  const [boardPlaylistItemsMap, setBoardPlaylistItemsMap] = useAtom(boardPlaylistItemsMapAtom);
  const [board] = useAtom(boardDetailAtom);
  const [loading, setLoading] = useAtom(loadingAtom);

  const boardId = board!.id;

  // 当前播放板块状态
  const currentBoardState = useMemo(
    () => boardPlaylistItemsMap[boardId] || DEFAULT_PLAYLIST_STATE,
    [boardPlaylistItemsMap, boardId],
  );

  // 计算派生状态
  const playlistItems = currentBoardState.items;
  const currentPlaylistIndex = currentBoardState.currentIndex;
  const playMode = currentBoardState.playMode;
  const audioPlayerState = currentBoardState.audioPlayerState;

  const currentPlaylistItem = useMemo(() => {
    if (currentPlaylistIndex >= 0 && currentPlaylistIndex < playlistItems.length) {
      return playlistItems[currentPlaylistIndex];
    }
    return null;
  }, [playlistItems, currentPlaylistIndex]);

  // 更新播放板块状态的通用方法
  const updateBoardState = useCallback(
    (updater: (prev: PlaylistState) => PlaylistState) => {
      setBoardPlaylistItemsMap((prev) => ({
        ...prev,
        [boardId]: updater(prev[boardId] || DEFAULT_PLAYLIST_STATE),
      }));
    },
    [boardId, setBoardPlaylistItemsMap],
  );

  // 播放列表管理方法
  const playlistActions: PlaylistActions = {
    refresh: async (reset = true) => {
      if (!board || loading) return;

      setLoading(true);
      try {
        const { data, error } = await callAPI(
          apiClient.playlistItemApi.listPlaylistItems({
            board_id: boardId,
          }),
        );

        if (error || !isArray(data)) return;

        const sortedItems = data.sort((a, b) => b.created_at.getTime() - a.created_at.getTime());

        updateBoardState((prev) => ({
          ...prev,
          items: sortedItems,
          ...(reset && {
            currentIndex: -1,
            playMode: PlayModeEnum.SEQUENTIAL,
            audioPlayerState: DEFAULT_AUDIO_PLAYER_STATE,
          }),
        }));
      } finally {
        setLoading(false);
      }
    },

    delete: async (id: string) => {
      const response = await callAPI(
        apiClient.playlistItemApi.deletePlaylistItem({
          id,
        }),
      );

      if (!response.error) {
        updateBoardState((prev) => ({
          ...prev,
          items: prev.items.filter((item) => item.id !== id),
        }));
      }
    },

    create: async (
      item: BoardTreeItem | null | undefined,
      model?: string,
      voice?: string,
      emotion?: string,
    ) => {
      if (!item) return;

      const map: Record<BoardItemTypeEnum, PlaylistItemSourceEntityTypeEnum> = {
        [BoardItemTypeEnum.SNIP]: PlaylistItemSourceEntityTypeEnum.snip,
        [BoardItemTypeEnum.THOUGHT]: PlaylistItemSourceEntityTypeEnum.thought,
        [BoardItemTypeEnum.CHAT]: PlaylistItemSourceEntityTypeEnum.unknownDefaultOpenApi,
        [BoardItemTypeEnum.BOARD_GROUP]: PlaylistItemSourceEntityTypeEnum.unknownDefaultOpenApi,
      };

      const response = await callAPI(
        apiClient.playlistItemApi.createPlaylistItem({
          entity_type: map[item.entity_type],
          entity_id: item.entity.id,
          board_id: boardId,
          model,
          voice,
          emotion,
        }),
      );

      if (!response.error && response.data) {
        updateBoardState((prev) => ({
          ...prev,
          items: [response.data, ...prev.items],
        }));
      }
    },

    updateTitle: async (id: string, title: string) => {
      const response = await callAPI(
        apiClient.playlistItemApi.updatePlaylistItemTitle({
          id,
          title,
        }),
      );

      if (!response.error && response.data) {
        updateBoardState((prev) => ({
          ...prev,
          items: prev.items.map((item) => (item.id === id ? response.data : item)),
        }));
      }
    },
  };

  // 播放控制方法
  const playControlActions: PlayControlActions = {
    play: () => {
      const { sound } = audioPlayerState;
      sound?.play();
    },

    pause: () => {
      const { sound } = audioPlayerState;
      sound?.pause();
    },

    stop: () => {
      const { sound } = audioPlayerState;
      if (sound) {
        sound.stop();
        sound.unload();
      }
    },

    toggle: () => {
      const { sound } = audioPlayerState;
      if (sound) {
        if (sound.playing()) {
          sound.pause();
        } else {
          sound.play();
        }
      }
    },

    next: () => {
      if (playlistItems.length === 0) return;

      const nextIndex = getNextIndex(currentPlaylistIndex, playlistItems.length, playMode);

      updateBoardState((prev) => ({
        ...prev,
        currentIndex: nextIndex,
      }));
    },

    previous: () => {
      if (playlistItems.length === 0) return;

      const prevIndex = getPreviousIndex(currentPlaylistIndex, playlistItems.length, playMode);

      updateBoardState((prev) => ({
        ...prev,
        currentIndex: prevIndex,
      }));
    },

    playByIndex: (index: number) => {
      if (index >= 0 && index < playlistItems.length) {
        // 如果点击的是当前正在播放的音频，则 toggle 播放状态
        if (index === currentPlaylistIndex) {
          const { sound } = audioPlayerState;
          if (sound) {
            if (sound.playing()) {
              sound.pause();
            } else {
              sound.play();
            }
          }
        } else {
          // 如果点击的是其他音频，则切换到该音频
          updateBoardState((prev) => ({
            ...prev,
            currentIndex: index,
          }));
        }
      }
    },

    playById: (id: string) => {
      const index = playlistItems.findIndex((item) => item.id === id);
      if (index !== -1) {
        updateBoardState((prev) => ({
          ...prev,
          currentIndex: index,
        }));
      }
    },

    setPlayMode: (mode: PlayModeEnum) => {
      updateBoardState((prev) => ({
        ...prev,
        playMode: mode,
      }));
    },

    seek: (_percentage: number) => {
      // 这个方法现在由 useAudioPlayer 处理
    },
  };

  // 设置音频播放器状态
  const setAudioPlayerState = useCallback(
    (updater: AudioPlayerState | ((prev: AudioPlayerState) => AudioPlayerState)) => {
      updateBoardState((prev) => ({
        ...prev,
        audioPlayerState: typeof updater === 'function' ? updater(prev.audioPlayerState) : updater,
      }));
    },
    [updateBoardState],
  );

  // 检查播放能力
  const hasNext = hasNextUtil(currentPlaylistIndex, playlistItems.length, playMode);
  const hasPrevious = hasPreviousUtil(currentPlaylistIndex, playlistItems.length, playMode);

  return {
    // 状态
    playlistItems,
    currentPlaylistItem,
    currentPlaylistIndex,
    playMode,
    audioPlayerState,
    loading,
    hasNext,
    hasPrevious,

    // 播放列表管理
    ...playlistActions,

    // 播放控制
    ...playControlActions,

    // 音频状态管理
    setAudioPlayerState,

    // 向后兼容的方法别名
    refreshBoardPlaylistItems: playlistActions.refresh,
    deletePlaylistItem: playlistActions.delete,
    createPlaylistItem: playlistActions.create,
    updatePlaylistItemTitle: playlistActions.updateTitle,
    playNext: playControlActions.next,
    playPrevious: playControlActions.previous,
    playSound: playControlActions.play,
    pauseSound: playControlActions.pause,
    stopSound: playControlActions.stop,
    toggleSound: playControlActions.toggle,
  };
};

// 导出类型和枚举以供其他文件使用
export { PlayModeEnum, PlayerState, type AudioPlayerState };

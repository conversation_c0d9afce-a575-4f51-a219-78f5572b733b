import { BoardItemTypeEnum } from '@repo/common/types/board-item';
import { removeMarkdownLinks } from '@youmindinc/youcommon';
import { AILanguageEnumKeys, useYouMindTranslate } from '@youmindinc/youmind-translate';
import { atom, useAtomValue, useSetAtom } from 'jotai';
import { useEffect, useRef } from 'react';
import { panelStateAtom } from '@/hooks/useBoardState';
import { getLanguageEnum } from '@/utils/language';
import { translationPersistence } from '@/utils/translation-persistence';

export const alwaysTranslateAtom = atom<boolean>(false);

export const translateAvailableAtom = atom<boolean>(false);

/**
 * 各种 SaaS 定制的逻辑
 */
export const useBoardTranslation = () => {};

export const useBoardTranslationEffect = () => {
  const { setDetectedLanguage, instance } = useYouMindTranslate();

  const panelState = useAtomValue(panelStateAtom);

  const alwaysTranslate = useAtomValue(alwaysTranslateAtom);
  const setTranslateAvailable = useSetAtom(translateAvailableAtom);

  // 每次 panel 变化
  useEffect(() => {
    // 1. 探测一次语言
    // eslint-disable-next-line prettier/prettier, @typescript-eslint/no-explicit-any
    const entity = panelState.panelData?.entity as any;
    if (!entity) {
      return;
    }

    if (panelState.panelData?.entity_type !== BoardItemTypeEnum.SNIP) {
      // 不是 Snip，不支持翻译
      setTranslateAvailable(false);
      return;
    }
    setTranslateAvailable(true);

    // let language =
    //   entity?.content?.language || entity?.transcript?.contents?.[0]?.language;

    const language = getLanguageEnum(
      removeMarkdownLinks(entity.content?.plain || entity.description?.plain || entity.title || ''),
    );

    setDetectedLanguage(language || null);

    // 2. 看看 alwaysTranslate
    if (!alwaysTranslate) {
      instance.clearTranslations();
    }
  }, [panelState]);
};

/**
 * Hook for managing translation state persistence with localStorage
 */
export const useTranslationPersistence = (
  snipId: string | undefined,
  targetLanguage?: string,
  sourceLanguage?: string,
  onLanguageRestore?: (language: string) => void,
) => {
  const { handleTranslateTrigger, isTranslated, handleTargetLanguageChange } =
    useYouMindTranslate();
  const hasRestoredRef = useRef(false);

  // Restore translation state on component mount (only once per snip)
  useEffect(() => {
    if (!snipId) {
      hasRestoredRef.current = false;
      return;
    }

    if (hasRestoredRef.current) return;

    const savedState = translationPersistence.restore(snipId);
    if (savedState?.isEnabled && !isTranslated) {
      // Restore translation if it was previously enabled
      // Also restore the target language if it was saved and different from source
      if (savedState.targetLanguage && handleTargetLanguageChange) {
        handleTargetLanguageChange(savedState.targetLanguage as AILanguageEnumKeys, false);
        onLanguageRestore?.(savedState.targetLanguage);
        setTimeout(() => {
          handleTranslateTrigger();
        });
      } else {
        handleTranslateTrigger();
      }
    }
    hasRestoredRef.current = true;
  }, [
    snipId,
    isTranslated,
    handleTranslateTrigger,
    handleTargetLanguageChange,
    sourceLanguage,
    onLanguageRestore,
  ]);

  // Save translation state when it changes
  useEffect(() => {
    if (!snipId || !hasRestoredRef.current) return;

    if (isTranslated) {
      // Only save when translation is enabled
      translationPersistence.save(snipId, true, targetLanguage);
    } else {
      // Clear the cache when translation is disabled
      translationPersistence.clear(snipId);
    }
  }, [snipId, isTranslated, targetLanguage]);
};

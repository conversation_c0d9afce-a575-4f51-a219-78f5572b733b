import { Button } from '@repo/ui/components/ui/button';
import {
  Popover,
  PopoverClose,
  PopoverContent,
  PopoverTrigger,
} from '@repo/ui/components/ui/popover';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@repo/ui/components/ui/tooltip';
import { useTrackActions } from '@repo/ui/lib/posthog/useTrackActions';
import { isDev, isPreview } from '@youmindinc/youcommon';
import { TranslateDeveloperPanel, useYouMindTranslate } from '@youmindinc/youmind-translate';
import { useAtomValue } from 'jotai';
import { ArrowRight, Bug, X } from 'lucide-react';
import AICard from '@/components/ai-card';
import { cn } from '@/utils/utils';
import { translateAvailableAtom } from '../hooks/useBoardTranslation';
import { TranslateLanguageSelector } from './language-selector';

/**
 * 翻译功能演示组件
 * 展示不同的翻译方式和应用场景
 */
export const TranslationPanel: React.FC = () => {
  const translateAvailable = useAtomValue(translateAvailableAtom);

  const {
    handleTranslateTrigger,
    handleTargetLanguageChange,
    handleSourceLanguageChange,
    isTranslating,
    isTranslated,
    error,
    targetLanguage,
    sourceLanguage,
    targetElement,
  } = useYouMindTranslate();

  const { trackButtonClick } = useTrackActions();

  const isButtonDisabled =
    isTranslating || (!isTranslated && !targetElement) || !translateAvailable;

  const renderDeveloperPanel = () => {
    if (isPreview() || isDev()) {
      return (
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Popover>
                <PopoverTrigger
                  className="flex items-center pl-4 text-muted-foreground hover:text-foreground"
                  asChild
                >
                  <Button
                    variant="outline"
                    className="h-8 w-8 rounded-full border-muted bg-transparent p-0"
                  >
                    <Bug size={16} />
                  </Button>
                </PopoverTrigger>

                <PopoverContent className="h-[50vh] w-[320px] rounded-[20px] border-none p-0">
                  <AICard className="h-full w-full">
                    <div className="no-scrollbar h-full w-full overflow-y-auto p-5 pt-4">
                      <div className="flex items-center justify-between">
                        <div className="text-base font-medium">Developer options</div>
                        <PopoverClose>
                          <Button variant="ghost" size="icon" className="h-7 w-7 rounded-full">
                            <X size={16} />
                          </Button>
                        </PopoverClose>
                      </div>
                      <TranslateDeveloperPanel />
                    </div>
                  </AICard>
                </PopoverContent>
              </Popover>
            </TooltipTrigger>
            <TooltipContent side="bottom">Visible in dev and preview</TooltipContent>
          </Tooltip>
        </TooltipProvider>
      );
    }
    return null;
  };

  return (
    <div className="flex flex-col gap-3">
      <div className="flex w-full items-center justify-between gap-1">
        <TranslateLanguageSelector
          type="source"
          value={sourceLanguage}
          onChange={handleSourceLanguageChange}
        />
        <div className="text-muted-foreground">
          <ArrowRight size={14} />
        </div>
        <TranslateLanguageSelector
          type="target"
          value={targetLanguage}
          onChange={handleTargetLanguageChange}
        />
      </div>

      <div className="flex w-full items-center justify-between gap-2">
        {/* <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="outline"
                className="h-10 w-10 rounded-full border-muted bg-transparent p-0"
                onClick={handleModeChange}
              >
                {translationMode === "below" ? <BelowSvg /> : <ReplaceSvg />}
              </Button>
            </TooltipTrigger>
            <TooltipContent side="bottom">
              {translationMode === "below"
                ? "Inline bilingual"
                : "Translation overlay"}
            </TooltipContent>
          </Tooltip>
        </TooltipProvider> */}

        {renderDeveloperPanel()}

        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                className={cn(
                  'h-8 flex-1 rounded-full text-sm font-normal',
                  isTranslated && 'bg-transparent',
                  isButtonDisabled && 'cursor-not-allowed opacity-50 hover:opacity-50',
                )}
                onClick={() => {
                  handleTranslateTrigger();
                  // 上报埋点
                  trackButtonClick('translate_click', {
                    target_language: targetLanguage,
                    source_language: sourceLanguage,
                  });
                }}
                variant={isTranslated ? 'outline' : 'default'}
              >
                {isTranslated ? 'Show original' : 'Translate'}
              </Button>
            </TooltipTrigger>
            <TooltipContent side="bottom" className={cn(!isButtonDisabled && 'hidden')}>
              {'Current content does not support translation'}
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      </div>

      {/* <div className="flex w-full items-center justify-between gap-2">
        <div className="text-sm">Always translate this board</div>
        <Switch
          checked={alwaysTranslate}
          onCheckedChange={(checked) => {
            setAlwaysTranslate(checked);
            // 上报埋点
            trackButtonClick("translate_always_translate_click", {
              target_state: checked,
            });
          }}
        />
      </div> */}

      {error && (
        <div className="translate-error mt-2 text-sm text-red-500">Error: {error.message}</div>
      )}
    </div>
  );
};

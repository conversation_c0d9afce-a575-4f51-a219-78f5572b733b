{"id": "01982b8f-f40c-7d5e-8dac-340ecca3c4c0", "space_id": "0198126c-8f3b-71ff-b00a-01be60ee877b", "creator_id": "846d9375-794c-4504-842f-9a6dcc05880e", "created_at": {}, "updated_at": {}, "title": "React 18 基础教程：现代前端开发的核心框架", "title_type": "manual", "content": {"raw": "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"}, "visibility": "private"}
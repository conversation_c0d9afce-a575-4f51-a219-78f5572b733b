import { DIFF_CHANGE_TYPE, DiffController, diffTransformUtils } from '@repo/editor-common';
import { getDiffBlockManage } from '@repo/ui-business-editor';
import newContent from './new-markdown-content.md';
import debugContent from './thought-dev.json';

const diffController = new DiffController();

const debugDiff = () => {
  const oldJSONWithoutDiff = diffTransformUtils
    .extractContentFromBase64({
      base64Content: debugContent.content.raw,
      extractType: DIFF_CHANGE_TYPE.REMOVED,
    })
    .toJSON();

  const diff = diffController.genDiff({
    oldJSONContent: oldJSONWithoutDiff,
    newMarkdownContent: newContent,
  });

  if (diff) {
    (window as any).youmindEditor.commands.setContent(diff);

    setTimeout(() => {
      const diffBlockManage = getDiffBlockManage((window as any).youmindEditor);
      if (diffBlockManage) {
        diffBlockManage.reRenderDiffInfo();
      }
    }, 1000);
  }
};

(window as any).debugDiff = debugDiff;

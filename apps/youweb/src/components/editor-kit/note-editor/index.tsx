import './note-editor.less';
import { DIFF_CHANGE_TYPE, DiffTransformUtils, markdownSerializer } from '@repo/editor-common';
import { cn } from '@repo/ui/lib/utils';
import type { EditorProps } from '@tiptap/pm/view';
import { type Content, type Editor, EditorContent, useEditor } from '@tiptap/react';
import { forwardRef, memo, useImperativeHandle, useRef } from 'react';
import { getNoteEditorExtension } from './extension';

export interface NoteEditorRef {
  getContent: () => {
    raw: Content | undefined;
    plain: string;
  };
  focus: () => void;
  getHTML: () => string | undefined;
}

interface NoteEditorProps {
  id: string;
  onCreate?: (editor: Editor) => void;
  onBlur?: (editor: Editor) => void;
  editorProps?: EditorProps;
  content?: Content;
  className?: string;
  editable?: boolean;
}

const NoteEditorCore = forwardRef<NoteEditorRef, NoteEditorProps>((props, ref) => {
  const { content, editorProps, className, editable = true, id } = props;
  const editorRef = useRef<Editor | null>(null);
  const diffTransformUtils = new DiffTransformUtils();

  const editor = useEditor(
    {
      extensions: getNoteEditorExtension(),
      immediatelyRender: true,
      shouldRerenderOnTransaction: false,
      content,
      editable,
      editorProps: {
        ...editorProps,
        attributes: {
          spellcheck: 'false',
          ...editorProps?.attributes,
        },
        handleKeyDown: (_, event) => {
          // 一定要拦截 ctrl + s 保存
          if (event.key === 's' && (event.metaKey || event.ctrlKey)) {
            event.preventDefault();
          }
          editorProps?.handleKeyDown?.(_, event);
        },
      },
      onBlur: () => {
        props.onBlur?.(editor);
      },
      onCreate: () => {
        editorRef.current = editor;
        props.onCreate?.(editor);
      },
    },
    [id],
  );

  useImperativeHandle(
    ref,
    () => ({
      getContent: () => {
        const json = editorRef.current?.getJSON();

        const docWithoutDiffInfo = diffTransformUtils.extractContent(
          editorRef.current!.state.doc,
          DIFF_CHANGE_TYPE.ADDED,
        );
        const contentMarkdown = markdownSerializer?.serialize(docWithoutDiffInfo) || '';
        return {
          raw: json!,
          plain: contentMarkdown,
        };
      },
      focus: () => {
        return editorRef.current?.commands.focus('end');
      },
      getHTML: () => {
        return editorRef.current?.getHTML();
      },
    }),
    [],
  );

  return (
    <div className={cn('note-editor-container', className)}>
      <EditorContent editor={editor} />
    </div>
  );
});

export const NoteEditor = memo(NoteEditorCore, (prevProps, nextProps) => {
  return prevProps.id === nextProps.id;
});

NoteEditorCore.displayName = 'NoteEditorCore';
NoteEditor.displayName = 'NoteEditor';

import type {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Divider,
  Heading,
  Image,
  Italic,
  Level,
  Link,
  OrderedList,
  TaskList,
  Underline,
} from '@repo/editor-common';
import type { Strikethrough } from 'lucide-react';
import type { InjectNativeMethodName, RegisterToNativeMethodName } from './const';

export type RegisterToNativeMethodEnableOptions = Partial<
  Record<RegisterToNativeMethodName, boolean>
>;

export type InjectNativeMethodEnableOptions = Partial<Record<InjectNativeMethodName, boolean>>;

export interface UndoOptions {
  enabled: boolean;
}

export type RedoOptions = UndoOptions;

export interface ShowToastOptions {
  message: string;
}

export enum SelectionType {
  CURSOR = 'cursor',
  SELECTION = 'selection',
}

export interface OnSelectionChangeOptions {
  selectionType: SelectionType;
  selectionInfo: {
    markInfoList: MarkInfo[];
    nodeInfoList: NodeInfo[];
  };
}

export interface OpenLinkOptions {
  url: string;
}

export type SetNodeMarkOptions =
  | SetBoldOptions
  | SetItalicOptions
  | SetUnderlineOptions
  | SetStrikethroughOptions
  | SetLinkOptions;

export interface SetBoldOptions {
  type: typeof Bold.name;
}

export interface SetItalicOptions {
  type: typeof Italic.name;
}

export interface SetUnderlineOptions {
  type: typeof Underline.name;
}

export interface SetStrikethroughOptions {
  type: typeof Strikethrough.name;
}

export interface SetLinkOptions {
  type: typeof Link.name;
  attrs: {
    url: string;
  };
}

export interface NodeInfo {
  type: string;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  attrs?: Record<string, any>;
}

export interface MarkInfo {
  type: string;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  attrs?: Record<string, any>;
}

export type InsertNodeOptions =
  | InsertHeadingNodeOptions
  | InsertImageNodeOptions
  | InsertBlockquoteNodeOptions
  | InsertHorizontalRuleNodeOptions
  | InsertOrderedListNodeOptions
  | InsertBulletListNodeOptions
  | InsertTaskListNodeOptions
  | InsertCodeBlockNodeOptions;

export interface InsertBlockquoteNodeOptions {
  type: typeof Blockquote.name;
  pos?: number;
}

export interface InsertHorizontalRuleNodeOptions {
  type: typeof Divider.name;
  pos?: number;
}

export interface InsertHeadingNodeOptions {
  type: typeof Heading.name;
  attrs: {
    level: Level;
  };
  pos?: number;
}

export interface InsertImageNodeOptions {
  type: typeof Image.name;
  attrs: {
    url: string | string[];
  };
  pos?: number;
}

export interface InsertCodeBlockNodeOptions {
  type: typeof CodeBlock.name;
  pos?: number;
}

export interface InsertOrderedListNodeOptions {
  type: typeof OrderedList.name;
  pos?: number;
}

export interface InsertBulletListNodeOptions {
  type: typeof BulletList.name;
  pos?: number;
}

export interface InsertTaskListNodeOptions {
  type: typeof TaskList.name;
  pos?: number;
}

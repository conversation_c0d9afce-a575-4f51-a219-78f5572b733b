import type { Editor } from '@tiptap/react';
import { methodRegistry } from '@youmindinc/jsbridge';
import type { Doc } from 'yjs';
import { PatchThought } from '@/typings/thought';
import { InjectNativeMethodName } from './const';
import type {
  InjectNativeMethodEnableOptions,
  OnSelectionChangeOptions,
  OpenLinkOptions,
  RedoOptions,
  RegisterToNativeMethodEnableOptions,
  ShowToastOptions,
  UndoOptions,
} from './type';

interface NativeMessageControllerOptions {
  editor: Editor;
  ydoc: Doc;
  registerToNativeMethodEnableOptions?: RegisterToNativeMethodEnableOptions;
  injectToNativeMethodEnableOptions?: InjectNativeMethodEnableOptions;
}

export class NativeMethodController {
  private readonly editor: Editor;

  private readonly ydoc: Doc;

  private readonly injectMethodPromise: Promise<void>;

  private _editorIsReadyNativeMethod: (() => Promise<void>) | null = null;

  private _updateEditorDataNativeMethod: ((data: PatchThought) => Promise<void>) | null = null;

  private _setUndoOptionsNativeMethod: ((options: UndoOptions) => Promise<void>) | null = null;

  private _setRedoOptionsNativeMethod: ((options: RedoOptions) => Promise<void>) | null = null;

  private _showToastNativeMethod: ((options: ShowToastOptions) => Promise<void>) | null = null;

  private injectToNativeMethodEnableOptions: InjectNativeMethodEnableOptions | undefined;

  private registerToNativeMethodEnableOptions: RegisterToNativeMethodEnableOptions | undefined;

  private _onSelectionChangeNativeMethod:
    | ((options: OnSelectionChangeOptions) => Promise<void>)
    | null = null;

  private _onFocusNativeMethod: (() => Promise<void>) | null = null;

  private _onBlurNativeMethod: (() => Promise<void>) | null = null;

  private _openLinkNativeMethod: ((options: OpenLinkOptions) => Promise<void>) | null = null;

  constructor(options: NativeMessageControllerOptions) {
    this.editor = options.editor;

    this.ydoc = options.ydoc;

    this.injectToNativeMethodEnableOptions = options.injectToNativeMethodEnableOptions;

    this.registerToNativeMethodEnableOptions = options.registerToNativeMethodEnableOptions;

    this.injectMethodPromise = this.initMethod();
  }

  async initMethod() {
    try {
      const methodsToImport = [];

      if (this.injectToNativeMethodEnableOptions?.[InjectNativeMethodName.OPEN_LINK]) {
        methodsToImport.push(
          methodRegistry.importMethod<void>(InjectNativeMethodName.OPEN_LINK).then((method) => {
            this._openLinkNativeMethod = method;
          }),
        );
      }
      if (this.injectToNativeMethodEnableOptions?.[InjectNativeMethodName.ON_FOCUS]) {
        methodsToImport.push(
          methodRegistry.importMethod<void>(InjectNativeMethodName.ON_FOCUS).then((method) => {
            this._onFocusNativeMethod = method;
          }),
        );
      }

      if (this.injectToNativeMethodEnableOptions?.[InjectNativeMethodName.ON_BLUR]) {
        methodsToImport.push(
          methodRegistry.importMethod<void>(InjectNativeMethodName.ON_BLUR).then((method) => {
            this._onBlurNativeMethod = method;
          }),
        );
      }

      if (this.injectToNativeMethodEnableOptions?.[InjectNativeMethodName.EDITOR_IS_READY]) {
        methodsToImport.push(
          methodRegistry
            .importMethod<void>(InjectNativeMethodName.EDITOR_IS_READY)
            .then((method) => {
              this._editorIsReadyNativeMethod = method;
            }),
        );
      }

      if (this.injectToNativeMethodEnableOptions?.[InjectNativeMethodName.UPDATE_EDITOR_DATA]) {
        methodsToImport.push(
          methodRegistry
            .importMethod<void>(InjectNativeMethodName.UPDATE_EDITOR_DATA)
            .then((method) => {
              this._updateEditorDataNativeMethod = method;
            }),
        );
      }

      if (this.injectToNativeMethodEnableOptions?.[InjectNativeMethodName.SET_UNDO_OPTIONS]) {
        methodsToImport.push(
          methodRegistry
            .importMethod<void>(InjectNativeMethodName.SET_UNDO_OPTIONS)
            .then((method) => {
              this._setUndoOptionsNativeMethod = method;
            }),
        );
      }

      if (this.injectToNativeMethodEnableOptions?.[InjectNativeMethodName.SET_REDO_OPTIONS]) {
        methodsToImport.push(
          methodRegistry
            .importMethod<void>(InjectNativeMethodName.SET_REDO_OPTIONS)
            .then((method) => {
              this._setRedoOptionsNativeMethod = method;
            }),
        );
      }

      if (this.injectToNativeMethodEnableOptions?.[InjectNativeMethodName.SHOW_TOAST]) {
        methodsToImport.push(
          methodRegistry.importMethod<void>(InjectNativeMethodName.SHOW_TOAST).then((method) => {
            this._showToastNativeMethod = method;
          }),
        );
      }

      if (this.injectToNativeMethodEnableOptions?.[InjectNativeMethodName.ON_SELECTION_CHANGE]) {
        methodsToImport.push(
          methodRegistry
            .importMethod<void>(InjectNativeMethodName.ON_SELECTION_CHANGE)
            .then((method) => {
              this._onSelectionChangeNativeMethod = method;
            }),
        );
      }

      await Promise.all(methodsToImport);
    } catch (error) {
      console.error('initMethod error', error);
    }
  }

  async updateEditorData(data: PatchThought) {
    console.log('updateEditorData', data);
    await this.injectMethodPromise;
    if (!this._updateEditorDataNativeMethod) {
      console.error('updateEditorData native method not found');
      return;
    }
    await this._updateEditorDataNativeMethod(data);
  }

  async editorIsReady() {
    await this.injectMethodPromise;
    console.log('editorIsReady');
    if (!this._editorIsReadyNativeMethod) {
      console.error('editorIsReady native method not found');
      return;
    }
    await this._editorIsReadyNativeMethod();
  }

  async setUndoOptions(options: UndoOptions) {
    await this.injectMethodPromise;
    console.log('setUndoOptions', options);
    if (!this._setUndoOptionsNativeMethod) {
      console.error('setUndoOptions native method not found');
      return;
    }
    await this._setUndoOptionsNativeMethod(options);
  }

  async setRedoOptions(options: RedoOptions) {
    await this.injectMethodPromise;
    console.log('setRedoOptions', options);
    if (!this._setRedoOptionsNativeMethod) {
      console.error('setRedoOptions native method not found');
      return;
    }
    await this._setRedoOptionsNativeMethod(options);
  }

  async showToast(options: ShowToastOptions) {
    await this.injectMethodPromise;
    console.log('showToast', options);
    if (!this._showToastNativeMethod) {
      console.error('showToast native method not found');
      return;
    }
    await this._showToastNativeMethod(options);
  }

  async onSelectionChange(options: OnSelectionChangeOptions) {
    await this.injectMethodPromise;
    console.log('onSelectionChange', options);
    if (!this._onSelectionChangeNativeMethod) {
      console.error('onSelectionChange native method not found');
      return;
    }
    await this._onSelectionChangeNativeMethod(options);
  }

  async onBlur() {
    await this.injectMethodPromise;
    console.log('onBlur');
    if (!this._onBlurNativeMethod) {
      console.error('onBlur native method not found');
      return;
    }
    await this._onBlurNativeMethod();
  }

  async onFocus() {
    await this.injectMethodPromise;
    console.log('onFocus');
    if (!this._onFocusNativeMethod) {
      console.error('onFocus native method not found');
      return;
    }
    await this._onFocusNativeMethod();
  }

  async openLink(options: OpenLinkOptions) {
    await this.injectMethodPromise;
    console.log('openLink', options);
    if (!this._openLinkNativeMethod) {
      console.error('openLink native method not found');
      return;
    }
    await this._openLinkNativeMethod(options);
  }
}

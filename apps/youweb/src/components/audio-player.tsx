/**
 * @see https://codepen.io/serversideup/pen/Vwrgoea
 * @see https://medium.com/@mohammadreza.tatlari8/a-simple-music-player-with-howler-and-react-74b47e892be1
 */

import { LoadingSpinner } from '@repo/ui/components/custom/loading-spinner';
import { Backward15sIcon } from '@repo/ui/components/icons/backward15s';
import { Forward15sIcon } from '@repo/ui/components/icons/forward15s';
import { Avatar, AvatarFallback, AvatarImage } from '@repo/ui/components/ui/avatar';
import { Button, ButtonWithTooltip } from '@repo/ui/components/ui/button';
import { Card } from '@repo/ui/components/ui/card';
import { Slider } from '@repo/ui/components/ui/slider';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@repo/ui/components/ui/tooltip';
import { AnimatePresence, motion } from 'framer-motion';
import { Howl } from 'howler';
// import WaveSurfer from "wavesurfer.js";
import { PanelBottomClose, PanelBottomOpen, PauseIcon, PlayIcon } from 'lucide-react';
import {
  type HTMLAttributes,
  type KeyboardEvent,
  useCallback,
  useEffect,
  useRef,
  useState,
} from 'react';
import Marquee from 'react-fast-marquee';
import { useInterval } from 'usehooks-ts';
import { convertTimestampToSeconds } from '@/hooks/scoped/useYouTubePlayer';
import { useTranslation } from '@/hooks/useTranslation';
import { cn } from '@/utils/utils';

export interface AudioPlayerState {
  sound: Howl | null;
  playerState: PlayerState;
  progressValue: number;
  currentPos: number;
  duration: number;
  volume: number;
  isMute: boolean;
  rate: number;
  isFold: boolean;
}

export enum PlayerState {
  PREPARE,
  READY,
  ENDED,
  PAUSE,
  PLAYING,
}

export const RATE_OPTIONS = [0.5, 1, 1.5, 2];

const AUDIO_POLL_INTERVAL = 15;
const SEEK_FORWARD_SECONDS = 15;
const SEEK_BACKWARD_SECONDS = 15;

export interface AudioContext {
  sound: Howl | null;
}

interface Track {
  src: string;
  title?: string;
  artist?: string;
  album?: string;
}

export interface AudioPlayerProps extends HTMLAttributes<HTMLDivElement> {
  track: Track;
  autoplay?: boolean;
  variant?: 'default' | 'mini';
  seekTo?: number;
  onPlayerReady?: (sound: Howl) => void;
  onPlayerLoadFailed?: () => void;
  onPlayerPaused?: () => void;
  onPlayerPlaying?: () => void;
  onPlayerTimeUpdate?: (time: number) => void;
  onSubtitleLocate?: (time: number) => void;
  renderCustomControls?: () => React.ReactNode;
}

export const AudioPlayer: React.FC<AudioPlayerProps> = ({
  track,
  autoplay,
  variant = 'default',
  seekTo = 0,
  onPlayerLoadFailed,
  onPlayerReady,
  onPlayerTimeUpdate,
  onPlayerPaused,
  onPlayerPlaying,
  onSubtitleLocate,
  renderCustomControls,
  className,
}) => {
  const { t } = useTranslation('AudioPlayer');
  const ref = useRef<HTMLDivElement>(null);

  const { title, src, artist, album } = track;
  const [audioPlayerState, setAudioPlayerState] = useState<AudioPlayerState>({
    sound: null,
    playerState: PlayerState.PREPARE,
    progressValue: 0,
    currentPos: 0,
    duration: 0,
    volume: 0,
    isMute: false,
    isFold: true,
    rate: 1,
  });
  const { sound, playerState, duration, progressValue, currentPos, isMute, isFold, rate } =
    audioPlayerState;

  // 使用 ref 来存储当前的回调函数，避免重复创建
  const callbacksRef = useRef({
    onPlayerLoadFailed,
    onPlayerReady,
    onPlayerPaused,
    onPlayerPlaying,
    onPlayerTimeUpdate,
    onSubtitleLocate,
  });

  // 使用 ref 来存储当前的 sound 实例，避免循环依赖
  const soundRef = useRef<Howl | null>(null);

  // 更新回调函数引用
  useEffect(() => {
    callbacksRef.current = {
      onPlayerLoadFailed,
      onPlayerReady,
      onPlayerPaused,
      onPlayerPlaying,
      onPlayerTimeUpdate,
      onSubtitleLocate,
    };
  }, [
    onPlayerLoadFailed,
    onPlayerReady,
    onPlayerPaused,
    onPlayerPlaying,
    onPlayerTimeUpdate,
    onSubtitleLocate,
  ]);

  const isPaused = useCallback(() => {
    return (
      playerState === PlayerState.READY ||
      playerState === PlayerState.PAUSE ||
      playerState === PlayerState.ENDED
    );
  }, [playerState]);

  const playbackPlay = useCallback(() => {
    if (sound) {
      sound.play();
      callbacksRef.current.onPlayerPlaying?.();
    }
  }, [sound]);

  const playbackPause = useCallback(() => {
    if (sound && sound.playing()) {
      sound.pause();
      callbacksRef.current.onPlayerPaused?.();
    }
  }, [sound]);

  const seek = useCallback(
    (value: number[]) => {
      if (sound && duration > 0) {
        const per = value[0] / 100;
        const seekTime = duration * per || 0;
        sound.seek(seekTime);

        setAudioPlayerState((state) => ({
          ...state,
          progressValue: value[0],
          currentPos: Math.round(seekTime),
        }));
      }
    },
    [sound, duration],
  );

  const step = useCallback(() => {
    if (sound && sound.playing()) {
      const seekTime = sound.seek() || 0;
      const percentage = (seekTime / sound.duration()) * 100 || 0;

      setAudioPlayerState((state) => ({
        ...state,
        progressValue: percentage,
        playerState: PlayerState.PLAYING,
        currentPos: Math.round(seekTime),
      }));

      callbacksRef.current.onPlayerTimeUpdate?.(Math.round(seekTime));
    }
  }, [sound]);

  const _changeVolume = useCallback(
    (volume: number) => {
      if (sound) {
        sound.volume(Math.round(volume) / 100);

        setAudioPlayerState((state) => ({
          ...state,
          volume,
          isMute: volume === 0,
        }));
      }
    },
    [sound],
  );

  const volumeUp = useCallback(() => {
    setAudioPlayerState((state) => {
      let volume = state.volume;
      volume += 5;
      if (volume > 100) {
        volume = 100;
      }
      state.sound?.volume(Math.round(volume) / 100);
      return {
        ...state,
        volume,
        isMute: false,
      };
    });
  }, []);

  const volumeDown = useCallback(() => {
    setAudioPlayerState((state) => {
      let volume = state.volume;
      let prevIsMute = state.isMute;
      volume -= 5;
      if (volume < 0) {
        volume = 0;
        prevIsMute = true;
      }
      state.sound?.volume(Math.round(volume) / 100);
      return {
        ...state,
        volume,
        isMute: prevIsMute,
      };
    });
  }, []);

  const seekForward = useCallback(() => {
    if (sound) {
      const currentPos = sound.seek();
      const duration = sound.duration();
      const forward = SEEK_FORWARD_SECONDS;
      const seeked = Math.min(currentPos + forward, duration);
      sound.seek(seeked);
      const percentage = (seeked / sound.duration()) * 100 || 0;

      setAudioPlayerState((state) => ({
        ...state,
        progressValue: Math.round(percentage),
        currentPos: Math.round(seeked),
      }));
    }
  }, [sound]);

  const seekBackward = useCallback(() => {
    if (sound) {
      const currentPos = sound.seek();
      const backward = SEEK_BACKWARD_SECONDS;

      const seeked = Math.max(0, currentPos - backward);
      sound.seek(seeked);

      const percentage = (seeked / sound.duration()) * 100 || 0;

      setAudioPlayerState((state) => ({
        ...state,
        progressValue: Math.round(percentage),
        currentPos: Math.round(seeked),
      }));
    }
  }, [sound]);

  const toggleFold = useCallback(() => {
    setAudioPlayerState((state) => ({
      ...state,
      isFold: !state.isFold,
    }));
  }, []);

  const setRate = useCallback(
    (rate: number) => {
      if (sound) {
        sound.rate(rate);
        setAudioPlayerState((state) => ({
          ...state,
          rate,
        }));
      }
    },
    [sound],
  );

  const createSound = useCallback(
    (play_url: string) => {
      // 清理之前的音频实例
      if (soundRef.current) {
        soundRef.current.unload();
      }

      const s = new Howl({
        src: [play_url],
        format: ['mp3'],
        autoplay: false,
        loop: false,
        // @see https://github.com/goldfire/howler.js?tab=readme-ov-file#streaming-audio-for-live-audio-or-large-files
        html5: true,
        onloaderror: () => {
          setAudioPlayerState({
            sound: null,
            playerState: PlayerState.PREPARE,
            progressValue: 0,
            currentPos: 0,
            duration: 0,
            volume: 0,
            isMute: false,
            isFold: false,
            rate: 1,
          });
          soundRef.current = null;
          callbacksRef.current.onPlayerLoadFailed?.();
        },
      });

      if (s.state() === 'loaded') {
        soundRef.current = s;
        setAudioPlayerState((state) => ({
          ...state,
          sound: s,
          playerState: PlayerState.READY,
          duration: Math.round(s.duration()),
          volume: s.volume(),
        }));
      }

      s.once('load', () => {
        soundRef.current = s;
        setAudioPlayerState((state) => ({
          ...state,
          sound: s,
          playerState: PlayerState.READY,
          duration: Math.round(s.duration()),
          volume: s.volume(),
        }));
        callbacksRef.current.onPlayerReady?.(s);

        if (autoplay) {
          s.play();
        }
      });

      s.on('end', () => {
        setAudioPlayerState((state) => ({
          ...state,
          playerState: PlayerState.ENDED,
          progressValue: 100,
          currentPos: state.duration,
        }));
      });

      s.on('play', () => {
        setAudioPlayerState((state) => ({
          ...state,
          playerState: PlayerState.PLAYING,
        }));
      });

      s.on('pause', () => {
        setAudioPlayerState((state) => ({
          ...state,
          playerState: PlayerState.PAUSE,
        }));
      });

      s.on('stop', () => {
        setAudioPlayerState((state) => ({
          ...state,
          playerState: PlayerState.ENDED,
          progressValue: 0,
          currentPos: 0,
        }));
      });
    },
    [autoplay],
  );

  const onKeyDown = useCallback(
    (event: KeyboardEvent) => {
      const { isMute, sound, playerState } = audioPlayerState;
      const code = event.code;

      if (code === 'Tab') {
        return;
      }

      event.preventDefault();
      event.stopPropagation();
      const focusedEle = document.activeElement;

      switch (code) {
        case 'Space':
        case 'Return': {
          if (focusedEle?.getAttribute('name') === 'volume') {
            if (isMute) {
              sound?.mute(false);
            } else {
              sound?.mute(true);
            }
            setAudioPlayerState((state) => ({
              ...state,
              isMute: !state.isMute,
            }));
            break;
          }
          if (playerState === PlayerState.PLAYING) {
            sound?.pause();
            setAudioPlayerState((state) => ({
              ...state,
              playerState: PlayerState.PAUSE,
            }));
          } else if (
            playerState === PlayerState.READY ||
            playerState === PlayerState.PAUSE ||
            playerState === PlayerState.ENDED
          ) {
            sound?.play();
            setAudioPlayerState((state) => ({
              ...state,
              playerState: PlayerState.PLAYING,
            }));
          }
          break;
        }
        case 'KeyM':
          if (isMute) {
            sound?.mute(false);
          } else {
            sound?.mute(true);
          }
          setAudioPlayerState((state) => ({
            ...state,
            isMute: !state.isMute,
          }));
          break;
        case 'ArrowUp':
          volumeUp();
          break;
        case 'ArrowDown':
          volumeDown();
          break;
        case 'ArrowRight':
          seekForward();
          break;
        case 'ArrowLeft':
          seekBackward();
          break;
        case 'Tab':
          break;
        default:
          break;
      }
    },
    [audioPlayerState, volumeUp, volumeDown, seekForward, seekBackward],
  );

  // 初始化音频
  useEffect(() => {
    if (src) {
      createSound(src);
    }
  }, [src, createSound]);

  // 处理 seekTo 参数
  useEffect(() => {
    if (seekTo > 0 && duration > 0) {
      seek([(seekTo * 100) / duration]);
    }
  }, [seekTo, duration, seek]);

  // 组件卸载时清理音频
  useEffect(() => {
    return () => {
      if (soundRef.current) {
        soundRef.current.stop();
        soundRef.current.unload();
      }
    };
  }, []);

  useEffect(() => {
    // 监听 youmind_seek_bilibili_iframe
    window.addEventListener('message', (event) => {
      if (event.data.type === 'youmind_seek_audio_player') {
        const timestamp = event.data.timestamp;
        const seconds = convertTimestampToSeconds(timestamp);
        seek([(seconds * 100) / duration]);
      }
    });
  }, [duration, seek]);

  // useEffect(() => {
  //   let waveSurfer: WaveSurfer | null;
  //   if (sound && waveformRef.current) {
  //     waveSurfer = WaveSurfer.create({
  //       container: waveformRef.current,
  //       waveColor: "rgba(0, 0, 0, 0.1)",
  //       url: track.src,
  //       height: 20,
  //       audioRate: rate,
  //       interact: false,
  //       cursorWidth: 0,
  //       normalize: true,
  //       // barWidth: 10,
  //       // barRadius: 4,
  //     });
  //   }

  //   return () => {
  //     waveSurfer?.destroy();
  //   };
  // }, [sound, rate, waveformRef]);

  useInterval(step, playerState === PlayerState.PLAYING ? AUDIO_POLL_INTERVAL : null);

  return (
    <Card
      className={cn('flex border-none bg-transparent shadow-none outline-none', className)}
      onKeyDown={onKeyDown}
      ref={ref}
    >
      {variant === 'default' && (
        <div
          className={cn(
            'flex flex-row items-center px-4 py-[18px]',
            isFold ? 'min-w-0 pb-3 pt-2' : 'w-auto md:w-[200px]',
          )}
        >
          <Avatar className={cn('h-10 w-10 rounded-md', isFold && 'h-9 w-9')}>
            <AvatarImage
              src={album || 'https://cdn.gooo.ai/assets/music.png'}
              alt="track album"
              className="rounded-md"
            />
            <AvatarFallback className="rounded-md"></AvatarFallback>
          </Avatar>
          <div
            className={cn(
              'ml-2 min-w-0 flex-1 flex-col justify-center',
              !isFold && 'hidden md:flex',
            )}
          >
            {playerState === PlayerState.PLAYING ? (
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger>
                    <Marquee
                      gradient={true}
                      gradientWidth={20}
                      className="body-strong text-foreground"
                      speed={20}
                      play={playerState === PlayerState.PLAYING}
                    >
                      {title}
                    </Marquee>
                  </TooltipTrigger>
                  <TooltipContent>{title}</TooltipContent>
                </Tooltip>
              </TooltipProvider>
            ) : (
              <div className="body-strong truncate">{title}</div>
            )}
            {!isFold && (
              <p className="caption overflow-hidden text-ellipsis whitespace-nowrap text-caption-foreground">
                {artist}
              </p>
            )}
          </div>
        </div>
      )}
      <div className="flex flex-1 items-center justify-end pr-2">
        {playerState === PlayerState.PREPARE ? (
          <div className="flex h-[80px] w-full items-center justify-center">
            <LoadingSpinner />
          </div>
        ) : (
          <>
            <AnimatePresence>
              {!isFold && (
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  // exit={{ opacity: 0 }}
                  className={cn(
                    'flex flex-1 flex-col py-3',
                    variant === 'mini' && 'flex-col-reverse px-4',
                  )}
                >
                  <div className="flex items-center justify-center gap-x-4">
                    <Button
                      variant="icon"
                      className="h-8 w-12 text-muted-foreground"
                      onClick={() =>
                        setRate(
                          RATE_OPTIONS[(RATE_OPTIONS.indexOf(rate) + 1) % RATE_OPTIONS.length],
                        )
                      }
                    >
                      <span className="text-sm">{rate}</span>
                      <span className="relative top-[1px] text-xs">X</span>
                    </Button>

                    <ButtonWithTooltip
                      tooltip={t('skipBack')}
                      variant="icon"
                      size="lg"
                      className="rounded-full text-muted-foreground"
                      onClick={seekBackward}
                    >
                      <Backward15sIcon />
                    </ButtonWithTooltip>

                    <ButtonWithTooltip
                      tooltip={isPaused() ? t('play') : t('pause')}
                      size="icon"
                      variant="default"
                      className="h-8 w-8"
                      disabled={!sound}
                      onClick={isPaused() ? playbackPlay : playbackPause}
                    >
                      {!isPaused() ? (
                        <PauseIcon size={20} fill="hsl(var(--background))" />
                      ) : (
                        <PlayIcon size={20} fill="hsl(var(--background))" />
                      )}
                    </ButtonWithTooltip>

                    <ButtonWithTooltip
                      tooltip={t('skipForward')}
                      variant="icon"
                      size="lg"
                      className="rounded-full text-muted-foreground"
                      onClick={seekForward}
                    >
                      <Forward15sIcon />
                    </ButtonWithTooltip>

                    <div className="flex w-12 items-center justify-center">
                      {renderCustomControls?.()}
                    </div>
                  </div>
                  <div className="caption flex flex-1 flex-row text-caption-foreground">
                    <span className="w-[32px] text-center">{formatTime(currentPos)}</span>
                    <div className="relative mx-2 flex flex-1 items-center">
                      <div
                        // ref={waveformRef}
                        className="absolute top-[-2px] w-full"
                      ></div>
                      <Slider
                        defaultValue={[0]}
                        max={100}
                        step={0.01}
                        value={[progressValue]}
                        onValueChange={seek}
                      />
                    </div>
                    <span className="w-[32px] text-center">
                      {formatTime(duration - currentPos)}
                    </span>
                  </div>
                </motion.div>
              )}
            </AnimatePresence>

            {variant === 'default' && (
              <div
                className={cn(
                  'flex flex-row items-center gap-x-4 pl-[18px]',
                  !isFold && 'pr-3 md:pr-0',
                )}
              >
                <ButtonWithTooltip
                  tooltip={t(isFold ? 'expand' : 'collapse')}
                  variant="icon"
                  size="default"
                  className="rounded-full text-muted-foreground"
                  onClick={toggleFold}
                  name="fold"
                >
                  {isFold ? <PanelBottomOpen /> : <PanelBottomClose />}
                </ButtonWithTooltip>

                <AnimatePresence>
                  {isFold && (
                    <motion.div initial={{ opacity: 0 }} animate={{ opacity: 1 }}>
                      <ButtonWithTooltip
                        tooltip={isPaused() ? t('play') : t('pause')}
                        size="icon"
                        variant="default"
                        className="h-8 w-8"
                        disabled={!sound}
                        onClick={isPaused() ? playbackPlay : playbackPause}
                      >
                        {!isPaused() ? (
                          <PauseIcon size={20} fill="hsl(var(--background))" />
                        ) : (
                          <PlayIcon size={20} fill="hsl(var(--background))" />
                        )}
                      </ButtonWithTooltip>
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>
            )}
          </>
        )}
      </div>
    </Card>
  );
};

AudioPlayer.displayName = 'AudioPlayer';

function formatTime(secs: number) {
  const minutes = Math.floor(secs / 60) || 0;
  const seconds = secs - minutes * 60 || 0;
  return `${minutes}:${seconds < 10 ? '0' : ''}${seconds}`;
}

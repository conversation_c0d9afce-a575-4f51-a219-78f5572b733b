import { BoardItemTypeEnum } from '@repo/common/types/board-item/types';
import type { ChatDetail } from '@repo/common/types/chat/types';
import type { SnipVO } from '@repo/common/types/snip/app-types';
import type { ThoughtVO } from '@repo/common/types/thought/types';
import { DialogTrigger } from '@repo/ui/components/ui/dialog';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@repo/ui/components/ui/dropdown-menu';
import { useTrackActions } from '@repo/ui/lib/posthog/useTrackActions';
import { useAtom } from 'jotai';
import { Loader2 } from 'lucide-react';
import { useState } from 'react';
import { useBoardItemTree } from '@/hooks/useBoardItemTree';
import { createBoardThoughtAtom } from '@/hooks/useBoardState';
import { addBoardItemsAndOpenAtom, unshiftBoardItemsAtom } from '@/hooks/useBoards';
import type { Board } from '@/typings/board';
import type { BoardItem } from '@/typings/board-item';
import { cn } from '@/utils/utils';
import { NewGroup } from '../icon/new-group';
import { NewSnip } from '../icon/new-snip';
import { NewThought } from '../icon/new-thought';
import { SnipCreatorDialog } from '../snip/snip-creator-dialog';

interface Props {
  children: React.ReactNode;
  board: Board;
  showNewGroup?: boolean;
  align?: 'start' | 'end' | 'center';
  className?: string;
  autoChangePanelData?: boolean;
  onAdded?: (items: BoardItem[]) => void;
}

export const BoardAdderDialog: React.FC<Props> = (props) => {
  const {
    children,
    board,
    showNewGroup = false,
    align = 'start',
    className,
    onAdded,
    autoChangePanelData = true,
  } = props;

  const [open, setOpen] = useState(false);

  const { startCreatingNewGroup } = useBoardItemTree();

  const [, createBoardThought] = useAtom(createBoardThoughtAtom);
  const [, unshiftBoardItems] = useAtom(unshiftBoardItemsAtom);
  const [, addBoardItemsAndOpen] = useAtom(addBoardItemsAndOpenAtom);

  const [loading, setLoading] = useState(false);

  const { trackButtonClick } = useTrackActions();

  const afterMaterialCreated = (
    type: BoardItemTypeEnum,
    items: SnipVO[] | ThoughtVO[] | (ChatDetail & { board_item: BoardItem })[],
  ) => {
    setOpen(false);
    setLoading(false);
    if (items.length) {
      const boardItems = items.map((item) => ({
        ...item.board_item!,
        entity: item,
        entity_type: type,
      }));
      if (autoChangePanelData) {
        addBoardItemsAndOpen(boardItems);
      } else {
        unshiftBoardItems(boardItems);
      }
      onAdded?.(boardItems);
    }
  };

  const handleNewThought = async () => {
    setLoading(true);
    const data = await createBoardThought(board.id);
    if (data) {
      afterMaterialCreated(BoardItemTypeEnum.THOUGHT, [data]);
    }
  };

  return (
    <DropdownMenu open={open} onOpenChange={setOpen} modal={false}>
      <DropdownMenuTrigger asChild>{children}</DropdownMenuTrigger>
      <DropdownMenuContent
        className={cn('relative w-[220px] rounded-xl border-none p-2', className)}
        align={align}
        forceMount
      >
        {loading && (
          <>
            <Loader2
              size={16}
              className="absolute z-30 right-2 top-2 animate-spin text-muted-foreground"
            />
            <div className="absolute inset-0 z-20 bg-background/40" />
          </>
        )}

        <DropdownMenuItem
          className="cursor-pointer"
          onClick={(e) => {
            e.preventDefault();
            handleNewThought();
            // 上报埋点
            trackButtonClick('board_adder_menu_thought_click');
          }}
        >
          <NewThought size={16} />
          Thought
        </DropdownMenuItem>

        <SnipCreatorDialog
          boardId={board.id}
          onStartSaving={() => {
            setOpen(false);
          }}
          onFinish={() => {
            setOpen(false);
            setLoading(false);
          }}
          autoChangePanelData={autoChangePanelData}
        >
          <DropdownMenuItem
            onClick={(e) => {
              e.preventDefault();
              // 上报埋点
              trackButtonClick('board_adder_menu_snip_click');
            }}
            className="p-0 cursor-pointer"
          >
            <DialogTrigger className="flex items-center w-full p-2">
              <NewSnip size={16} className="mr-2" />
              Snip
            </DialogTrigger>
          </DropdownMenuItem>
        </SnipCreatorDialog>

        {showNewGroup && (
          <DropdownMenuItem
            className="cursor-pointer"
            onClick={(_e) => {
              startCreatingNewGroup();
              // 上报埋点
              trackButtonClick('board_adder_menu_group_click');
            }}
          >
            <NewGroup size={16} />
            Group
          </DropdownMenuItem>
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

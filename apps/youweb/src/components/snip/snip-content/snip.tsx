'use client';

import { createConfig, Sni<PERSON><PERSON><PERSON><PERSON>, SnipProvider } from '@repo/ui-business-snip';
import { atom, useAtom } from 'jotai';
import { useLayoutEffect, useMemo } from 'react';
import { useSearchParams } from 'react-router-dom';
import { ImagePreviewWithBusinessLogic } from '@/components/image-preview/with-business-logic';
import {
  refreshSnipDetailAtom,
  snipDetailAtom,
  snipDetailLoadingAtom,
} from '@/hooks/scoped/useSnips';
import { useDeleteUrlParams } from '@/hooks/useDeleteUrlParams';
import { useMindMaterialSize } from '@/hooks/useMindMaterialSize';
import { apiClient, callHTTPStream } from '@/utils/callHTTP';

const snipScrollPositionAtom = atom<Record<string, number>>({});

export default function Snip({
  readonly,
  // [FIXME] 当添加文章后传入
  showOpenSource = true,
}: {
  readonly?: boolean;
  showOpenSource?: boolean;
}) {
  const [snip] = useAtom(snipDetailAtom);
  const [snipScrollPosition, setSnipScrollPosition] = useAtom(snipScrollPositionAtom);
  const [searchParams] = useSearchParams();
  const { deleteParams } = useDeleteUrlParams();
  const [, refreshSnipDetail] = useAtom(refreshSnipDetailAtom);
  const [snipDetailLoading] = useAtom(snipDetailLoadingAtom);
  const { isSmallScreen } = useMindMaterialSize();
  // 必须要用 layout，在 dom 更新前，先拿到当前的滚动位置，否则 dom 一旦更新，拿到的就是新 snip 的位置了，不对了
  useLayoutEffect(() => {
    if (snip?.id) {
      const scrollPosition = snipScrollPosition[snip.id];
      if (scrollPosition) {
        setTimeout(() => {
          document.getElementById(`mind-material-view-scroll-container`)?.scrollTo({
            top: scrollPosition,
          });
        }, 0);
      }
    }
    return () => {
      if (snip?.id) {
        const scrollElement = document.getElementById(`mind-material-view-scroll-container`);
        if (scrollElement) {
          const scrollTop = scrollElement.scrollTop;
          setSnipScrollPosition((prev) => {
            const newState = { ...prev, [snip.id]: scrollTop };
            return newState;
          });
        }
      }
    };
  }, [snip?.id]);

  const config = useMemo(() => {
    return createConfig({
      apiClient: apiClient,
      events: {
        onNeedRefreshSnip: async () => {
          await refreshSnipDetail();
        },
      },
      callHTTPStream: callHTTPStream,
      services: {
        deleteUrlParams: (params) => {
          deleteParams(params);
        },
      },
      readonly,
      searchParams,
      loading: snipDetailLoading,
      onlineVideo: {
        options: {
          showViewButton: !isSmallScreen,
        },
      },
      components: {
        ImagePreview: ImagePreviewWithBusinessLogic,
      },
      article: {
        options: {
          showOpenSource: showOpenSource,
        },
      },
    });
  }, [readonly, isSmallScreen, searchParams, showOpenSource, snipDetailLoading]);

  return (
    <SnipProvider config={config}>
      <SnipContainer />
    </SnipProvider>
  );
}

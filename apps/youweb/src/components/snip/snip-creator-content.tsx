import { BoardItemTypeEnum } from '@repo/common/types/board-item/types';
import type { SnipVO } from '@repo/common/types/snip/app-types';
import { Button } from '@repo/ui/components/ui/button';
import { toast } from '@repo/ui/components/ui/sonner';
import { useTrackActions } from '@repo/ui/lib/posthog/useTrackActions';
import { useSetAtom } from 'jotai';
import { X } from 'lucide-react';
import type React from 'react';
import { useEffect, useRef, useState } from 'react';
import { uuidv7 } from 'uuidv7';
import validator from 'validator';
import { FileInput, FileUploader } from '@/components/file-upload';
import { addBoardItemsAndOpenAtom, unshiftBoardItemsAtom } from '@/hooks/useBoards';
import { useSnipSaving } from '@/hooks/useSnipSaving';
import { useTranslation } from '@/hooks/useTranslation';
import { useUploadFiles } from '@/hooks/useUploadFiles';
import type { BoardItem } from '@/typings/board-item';
import {
  AUDIO_ICON,
  SMALL_PDF_ICON,
  SNIPPET_ICON,
  VIDEO_ICON,
} from '@/utils/board/getImageAndTitleFromBoardItem';
import { callHTTP } from '@/utils/callHTTP';
import { convertJikeWebUrlToAppUrl } from '@/utils/jike';
import { cn } from '@/utils/utils';
import { Textarea } from '../ui/textarea';

export interface ISnipCreatorProps {
  type: 'url' | 'file' | 'both';
  fileUploaderContent?: React.ReactNode;
  webpageInputLabel?: React.ReactNode;
  hideAddButton?: boolean;
  className?: string;
  boardId?: string;
  boardGroupId?: string;
  boardGroupItemId?: string;
  autoChangePanelData?: boolean;
  onFileChanged?: (files: File[] | null) => void;
  onFinish?: (type: 'url' | 'file', snips: SnipVO[]) => void;
  onStartSaving?: () => void;
  onLoadingChange?: (loading: boolean) => void;
  onOneFileUploaded?: (snip: SnipVO) => void;
}

const MAX_FILES = 20;
const MAX_FILE_SIZE = 1024 * 1024 * 100;
const MAX_URLS = 20;

export const dropZoneConfig = {
  maxFiles: MAX_FILES,
  maxSize: MAX_FILE_SIZE,
  multiple: true,
  accept: {
    'audio/mpeg': ['.mp3', '.m4a', '.ogg', '.wav'],
    'image/*': ['.png', '.gif', '.jpeg', '.jpg'],
    'application/pdf': ['.pdf'],
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx'],
    'application/vnd.openxmlformats-officedocument.presentationml.presentation': ['.pptx'],
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': ['.xlsx'],
    'text/plain': ['.txt'],
    'text/markdown': ['.md', '.markdown'],
    'application/x-subrip': ['.srt'],
  },
};

export function SnipCreatorContent(props: ISnipCreatorProps) {
  const {
    boardId,
    boardGroupId,
    boardGroupItemId,
    onFileChanged,
    onFinish,
    hideAddButton = false,
    fileUploaderContent,
    webpageInputLabel,
    className,
    onLoadingChange,
    onOneFileUploaded,
    onStartSaving,
    autoChangePanelData = false,
  } = props;

  const unshiftBoardItems = useSetAtom(unshiftBoardItemsAtom);
  const addBoardItemsAndOpen = useSetAtom(addBoardItemsAndOpenAtom);

  const { t } = useTranslation('Library.SnipList');
  const [loading, setLoading] = useState(false);
  const [urlContent, setURLContent] = useState('');
  const [urlContentValid, setURLContentValid] = useState(false);
  const [invalidUrls, setInvalidUrls] = useState<string[]>([]);
  const [selectedFiles, setSelectedFiles] = useState<File[] | null>(null);
  const inputRef = useRef<HTMLTextAreaElement>(null);

  const { trackButtonClick } = useTrackActions();

  const { addSavingUrlSnipByKey, handleOneSnipSavingSuccessByKey, handleOneSnipSavingFailedByKey } =
    useSnipSaving();

  useEffect(() => {
    setTimeout(() => {
      if (inputRef.current) {
        inputRef.current.focus();
      }
    }, 100);
  }, []);

  useEffect(() => {
    onLoadingChange?.(loading);
  }, [loading]);

  const { uploadFiles } = useUploadFiles();

  const handleFinish = (type: 'url' | 'file', snips: SnipVO[]) => {
    const boardItems = snips.map((item) => {
      let entityType = BoardItemTypeEnum.SNIP;
      if ((item as unknown as BoardItem).entity_type === BoardItemTypeEnum.THOUGHT) {
        entityType = BoardItemTypeEnum.THOUGHT;
      }
      return {
        ...item.board_item!,
        parentId: boardGroupItemId,
        parent_board_group_id: boardGroupId,
        entity: item,
        entity_type: entityType,
      };
    });
    if (autoChangePanelData) {
      addBoardItemsAndOpen(boardItems);
    } else {
      unshiftBoardItems(boardItems);
    }
    onFinish?.(type, snips);
  };

  const handleCreateSnips = async () => {
    let pastedURLs = urlContent
      .split('\n')
      .map((url) => url.trim())
      .filter((url) => !!url);
    // 去重
    pastedURLs = [...new Set(pastedURLs)];

    // 限制URL数量
    if (pastedURLs.length > MAX_URLS) {
      pastedURLs = pastedURLs.slice(0, MAX_URLS);
    }

    // 将 Jike 的 web 链接转换为 app 链接
    pastedURLs = pastedURLs.map((url) => convertJikeWebUrlToAppUrl(url));

    setLoading(true);
    try {
      onStartSaving?.();

      // 先处理文件上传
      if (selectedFiles?.length) {
        try {
          uploadFiles(selectedFiles, {
            boardId,
            boardGroupId,
            onFinish: (snips) => handleFinish('file', snips),
            onOneFileUploaded,
          });
          onFileChanged?.(selectedFiles);
          setSelectedFiles(null);
        } catch (error) {
          toast((error as Error).message);
        }
      }

      // 再处理URL上传
      if (pastedURLs.length > 0) {
        const urlKeyMap = new Map<string, string>();
        pastedURLs.forEach((url) => {
          urlKeyMap.set(url, uuidv7());
        });
        // 立即添加 savingSnips
        pastedURLs.forEach((url) => {
          addSavingUrlSnipByKey(urlKeyMap.get(url)!, url);
        });

        const urlResults = await Promise.all(
          pastedURLs.map((url) =>
            callHTTP('/api/v1/tryCreateSnipByUrl', {
              method: 'POST',
              body: {
                url,
                board_id: boardId,
                parent_board_group_id: boardGroupId,
              },
              silent: true,
            }).then((result) => {
              if (!result.error) {
                handleOneSnipSavingSuccessByKey(urlKeyMap.get(url)!, result.data);
              } else {
                handleOneSnipSavingFailedByKey(urlKeyMap.get(url)!, result.error.message);
              }
              return result;
            }),
          ),
        );

        const urlSnips = urlResults.map((result) => result?.data).filter((s): s is SnipVO => !!s);
        if (urlSnips.length) {
          handleFinish('url', urlSnips);
        }
      }
    } catch (_error) {
      toast(t('fetchFailed'));
    } finally {
      setLoading(false);
    }
  };

  const handleFileChange = (files: File[] | null) => {
    // 如果已经有URL内容，清空它
    if (files && urlContent.trim()) {
      setURLContent('');
      setURLContentValid(false);
      setInvalidUrls([]);
    }

    setSelectedFiles(files);

    // 如果有文件被选择，立即触发上传
    if (files && files.length > 0) {
      // 防止重复上传
      if (!loading) {
        setLoading(true);
        try {
          onStartSaving?.();
          uploadFiles(files, {
            boardId,
            boardGroupId,
            onFinish: (snips) => {
              handleFinish('file', snips);
              setSelectedFiles(null);
            },
            onOneFileUploaded,
          });
          onFileChanged?.(files);
        } catch (error) {
          toast((error as Error).message);
        } finally {
          setLoading(false);
        }
      }
    }
  };

  const handlePaste = async (event: React.ClipboardEvent) => {
    // 阻止事件冒泡，防止多次触发
    event.stopPropagation();

    // 如果正在上传或正在处理URL，则不处理粘贴事件
    if (loading || (urlContent.trim().length > 0 && urlContentValid)) return;

    const items = event.clipboardData?.items;
    if (!items) return;

    // 过滤出支持的文件类型
    const supportedItems = Array.from(items).filter((item) => {
      // 检查是否是文件类型
      if (item.kind !== 'file') return false;

      const type = item.type.toLowerCase();
      // 匹配支持的MIME类型
      return (
        type.startsWith('image/') ||
        type.startsWith('audio/') ||
        type.includes('pdf') ||
        type.startsWith('text/') ||
        type.includes('subrip')
      );
    });

    if (supportedItems.length === 0) return;

    // 检查文件数量限制
    if (selectedFiles && selectedFiles.length + supportedItems.length > MAX_FILES) {
      toast(t('maxUrlsExceeded', { max: MAX_FILES }));
      return;
    }

    const newFiles = supportedItems
      .map((item) => {
        const file = item.getAsFile();
        if (!file) return null;

        // 验证文件大小
        if (file.size > MAX_FILE_SIZE) {
          toast(t('fileSizeTooLarge'));
          return null;
        }

        return file;
      })
      .filter((file): file is File => file !== null);

    if (newFiles.length === 0) return;

    // 合并现有文件和新粘贴的文件
    const combinedFiles = selectedFiles ? [...selectedFiles, ...newFiles] : newFiles;

    // 使用现有的handleFileChange函数处理文件上传
    handleFileChange(combinedFiles);
  };

  // 渲染文件预览
  const renderFilePreview = () => {
    if (!selectedFiles || selectedFiles.length === 0) return null;

    const getImageUrl = (file: File) => {
      if (file.type.startsWith('image')) {
        return URL.createObjectURL(file);
      }
      if (file.type.includes('pdf')) {
        return SMALL_PDF_ICON;
      }
      if (file.type.includes('audio')) {
        return AUDIO_ICON;
      }
      if (file.type.includes('video')) {
        return VIDEO_ICON;
      }
      if (file.type.startsWith('text/') || file.type.includes('subrip')) {
        // For text files, use a generic text icon
        return 'https://cdn.gooo.ai/assets/text.png';
      }
      return null;
    };

    return (
      <div className="mt-2">
        <div className="flex items-center justify-between">
          <p className="body text-muted-foreground">{t('files')}</p>
          <span className="text-sm text-muted-foreground">
            {selectedFiles.length}/{MAX_FILES}
          </span>
        </div>
        <div className="scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-transparent mt-2 max-h-[200px] overflow-y-auto">
          <div className="flex flex-col w-full gap-1">
            {selectedFiles.map((file, index) => (
              <div
                key={file.name}
                className="flex items-center justify-between w-full px-3 py-2 rounded-md group hover:bg-card-snips"
              >
                <div className="flex items-center flex-1 min-w-0 gap-2 overflow-hidden">
                  <div className="flex-shrink-0 w-8 h-8 overflow-hidden rounded-md">
                    <img
                      src={getImageUrl(file) || SNIPPET_ICON}
                      alt={file.name}
                      className="object-cover w-full h-full"
                    />
                  </div>
                  <div className="flex flex-col flex-1 min-w-0 overflow-hidden">
                    <span className="text-sm font-medium line-clamp-1">{file.name}</span>
                    <span className="text-xs text-muted-foreground">
                      {file.size < 1024 * 1024
                        ? `${(file.size / 1024).toFixed(2)} KB`
                        : `${(file.size / 1024 / 1024).toFixed(2)} MB`}
                    </span>
                  </div>
                </div>
                <Button
                  iconOnly
                  type="button"
                  // className="flex-shrink-0 ml-2 transition-opacity opacity-0 text-muted-foreground hover:text-destructive group-hover:opacity-100"
                  onClick={() => {
                    const newFiles = [...selectedFiles];
                    newFiles.splice(index, 1);
                    setSelectedFiles(newFiles.length > 0 ? newFiles : null);
                  }}
                >
                  <X size={16} />
                </Button>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  };

  const renderFileUploader = () => {
    if (props.type !== 'file' && props.type !== 'both') {
      return null;
    }

    // 如果URL输入框有内容，则禁用文件上传
    const isDisabled = loading || urlContent.trim().length > 0;

    const renderFileUploaderContent = () => {
      return (
        <div
          className={cn('opacity-100', {
            'cursor-not-allowed opacity-50': isDisabled,
          })}
        >
          {fileUploaderContent || (
            <div
              className={cn(
                'border-line flex h-[200px] w-full items-center justify-center border border-dashed',
              )}
            >
              <div className="flex flex-col items-center justify-center text-center">
                <svg
                  role="img"
                  aria-label="pasteLinkPlaceholder"
                  xmlns="http://www.w3.org/2000/svg"
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="mb-2 text-muted-foreground"
                >
                  <path d="M4 14.899A7 7 0 1 1 15.71 8h1.79a4.5 4.5 0 0 1 2.5 8.242"></path>
                  <path d="M12 12v9"></path>
                  <path d="m16 16-4-4-4 4"></path>
                </svg>
                <p className="mb-1 text-sm font-medium">{t('files')}</p>
                <p className="text-xs text-muted-foreground">{t('pasteLinkPlaceholder')}</p>
              </div>
            </div>
          )}
        </div>
      );
    };

    // 当禁用时，不渲染FileUploader组件或返回禁用状态的UI
    if (isDisabled && selectedFiles === null) {
      return renderFileUploaderContent();
    }

    return (
      <FileUploader
        value={selectedFiles}
        onValueChange={isDisabled ? () => {} : handleFileChange}
        dropzoneOptions={dropZoneConfig}
      >
        <FileInput className="h-full w-full min-w-[320px]" disabled={isDisabled}>
          {renderFileUploaderContent()}
        </FileInput>
      </FileUploader>
    );
  };

  // 计算当前有效URL数量
  const currentUrlCount = urlContent
    ?.split('\n')
    ?.map((url) => url.trim())
    ?.filter((url) => !!url && validator.isURL(url)).length;

  const renderURLInput = () => {
    if (props.type !== 'url' && props.type !== 'both') {
      return null;
    }

    return (
      <>
        <div className="flex items-center justify-between mt-0">
          {webpageInputLabel || <p className="body text-muted-foreground">{t('importFromURL')}</p>}
          <span className="text-sm text-muted-foreground">
            {currentUrlCount}/{MAX_URLS}
          </span>
        </div>
        <Textarea
          placeholder={t('pasteLinkPlaceholder')}
          value={urlContent}
          disabled={loading || (!!selectedFiles && selectedFiles.length > 0)}
          ref={inputRef}
          onChange={(e) => {
            const content = e.target.value;
            setURLContent(content);

            // 如果用户开始输入URL，清空已选择的文件
            if (content.trim() && selectedFiles) {
              setSelectedFiles(null);
            }

            // 检查每一行的 URL 格式
            const urls = content.split('\n').filter((url) => !!url);
            const validUrls = urls.filter((url) => validator.isURL(url));
            const invalidUrlsList = urls.filter((url) => !!url && !validator.isURL(url));

            setInvalidUrls(invalidUrlsList);
            setURLContentValid(validUrls.length > 0 && validUrls.length <= MAX_URLS);
          }}
          className="min-h-[108px] w-full rounded-xl border-border placeholder:text-caption"
        />
        {invalidUrls.length > 0 && (
          <p className="mt-1 text-sm text-red-500">{t('invalidUrlFormat')}</p>
        )}
        {currentUrlCount > MAX_URLS && (
          <p className="mt-1 text-sm text-red-500">{t('maxUrlsExceeded', { max: MAX_URLS })}</p>
        )}
      </>
    );
  };

  return (
    <div
      className={cn('flex w-full flex-col gap-4 overflow-hidden', className)}
      onPaste={handlePaste}
    >
      {renderFileUploader()}
      {renderFilePreview()}
      {renderURLInput()}
      <Button
        iconOnly
        loading={loading}
        type="submit"
        disabled={(!urlContentValid && !selectedFiles) || loading}
        onClick={() => {
          handleCreateSnips();
          // 上报埋点
          trackButtonClick('snip_create_add_click', {
            url_count: currentUrlCount,
          });
        }}
        className={cn('title h-10 w-full rounded-full text-card', {
          hidden: hideAddButton || (selectedFiles !== null && selectedFiles.length > 0),
        })}
      >
        {t('add')}
      </Button>
    </div>
  );
}

import type { SnipOtherWebpageVO } from '@repo/common/types/snip/app-types';
import { Avatar, AvatarFallback, AvatarImage } from '@repo/ui/components/ui/avatar';
import { Card, CardContent, CardHeader } from '@repo/ui/components/ui/card';
import { Earth } from '@/components/icon';
import { TimeSince } from '@/utils/timeSince';

export default function SnipThumbnailOther({ snip }: { snip: SnipOtherWebpageVO }) {
  const {
    webpage: { site, title, url },
  } = snip;
  const header = title || site?.name;

  return (
    <Card className="relative overflow-hidden rounded-2xl border-none bg-card shadow-md transition-shadow hover:shadow-lg">
      <CardHeader className="space-y-0 p-0 px-4"></CardHeader>
      <CardContent className="px-4 py-4">
        {header && (
          <div className="mb-3 flex">
            <p className="... title overflow-hidden text-ellipsis whitespace-nowrap">{header}</p>
          </div>
        )}
        <div className="footnote flex items-center text-muted-foreground">
          {site.favicon_url?.startsWith('http') ? (
            <Avatar className="mr-1 h-4 w-4 rounded-full">
              <AvatarImage src={site.favicon_url} />
              <AvatarFallback></AvatarFallback>
            </Avatar>
          ) : (
            <Earth size={14} className="mr-1" />
          )}
          <p className="flex-1 overflow-hidden text-ellipsis whitespace-nowrap">{url}</p>
        </div>
      </CardContent>
    </Card>
  );
}

export const SnipSmallThumbnailOther = ({ snip }: { snip: SnipOtherWebpageVO }) => {
  const {
    webpage: { site, title, url },
  } = snip;
  const header = title || site?.name;

  return (
    <Card className="relative flex h-full flex-col overflow-hidden rounded-2xl border-muted bg-card shadow-sm transition-shadow hover:shadow-lg">
      <CardContent className="flex flex-1 flex-col justify-between px-2 pb-2 pt-4">
        <div>
          {site.favicon_url ? (
            <Avatar className="h-[28px] w-[28px] rounded-full">
              <AvatarImage src={site.favicon_url} />
              <AvatarFallback></AvatarFallback>
            </Avatar>
          ) : (
            <Earth size={14} className="mr-1" />
          )}
          <div className="mt-6 line-clamp-1 max-h-[110px] text-sm font-medium">{header}</div>
        </div>

        <div className="mt-1 line-clamp-2 break-all text-[10px] leading-[14px]">{url}</div>

        <div className="mt-2 text-[10px] text-caption">
          <TimeSince dateString={snip.updated_at} />
        </div>
      </CardContent>
    </Card>
  );
};

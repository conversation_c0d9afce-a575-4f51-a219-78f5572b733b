import type { SnipVideoVO } from '@repo/common/types/snip/app-types';
import { Avatar, AvatarFallback, AvatarImage } from '@repo/ui/components/ui/avatar';
import { Card, CardContent, CardHeader } from '@repo/ui/components/ui/card';
import { Skeleton } from '@repo/ui/components/ui/skeleton';
import { Blurhash } from '@/components/blurhash';
import { TimeSince } from '@/utils/timeSince';
import { cn, isSnipImageTransfering, shortenSiteName } from '@/utils/utils';

export default function SnipThumbnailOnlineVideo({ snip }: { snip: SnipVideoVO }) {
  // const format = useFormatter();
  // const updatedAt =
  //   snip.updated_at &&
  //   format.dateTime(snip.updated_at, {
  //     year: "numeric",
  //     month: "short",
  //     day: "numeric",
  //   });

  const siteName = shortenSiteName(snip);

  const isImageTransfering = isSnipImageTransfering(snip);

  return (
    <Card
      className={cn(
        'overflow-hidden rounded-2xl border-none bg-card shadow-md transition-shadow hover:shadow-lg',
      )}
    >
      <CardHeader
        className={cn('space-y-0 p-0', {
          'pt-0': !snip.hero_image_url,
        })}
      >
        {isImageTransfering ? (
          <Skeleton className="aspect-video w-full rounded-none rounded-t-xl object-cover" />
        ) : (
          snip.hero_image_url && (
            <Avatar className="aspect-video h-full w-full rounded-none rounded-t-xl object-cover">
              <AvatarImage src={snip.hero_image_url} className="rounded-none rounded-t-xl" />
              <AvatarFallback className="rounded-none rounded-t-xl">
                <Blurhash snip={snip} />
              </AvatarFallback>
            </Avatar>
          )
        )}
      </CardHeader>
      <CardContent className="px-4 pb-5 pt-3">
        <div className="mb-3 flex">
          {snip?.webpage?.site?.favicon_url && (
            <Avatar className="mr-[6px] h-5 w-5 rounded-sm">
              <AvatarImage src={snip?.webpage?.site?.favicon_url} />
              <AvatarFallback></AvatarFallback>
            </Avatar>
          )}

          <p className="... overflow-hidden text-ellipsis whitespace-nowrap text-sm">{siteName}</p>
        </div>

        {/* <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <p className="text-ellipsis-line-3 title max-h-[110px]">
                  {snip.title}
                </p>
              </TooltipTrigger>
              <TooltipContent>
                <span>{snip.title}</span>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider> */}
        <p className="text-ellipsis-line-3 title max-h-[110px]">{snip.title}</p>

        {snip?.webpage?.description && (
          <p className="text-ellipsis-line-3 caption mt-2 max-h-[60px] text-muted-foreground">
            {snip?.webpage?.description}
          </p>
        )}
        <div className="caption mt-2 flex flex-row text-caption">
          {/* <Avatar className="mr-2 h-4 w-4">
              <AvatarImage
                src={
                  snip.authors?.[0]?.picture || snip.webpage.site.favicon_url
                }
                alt="author avatar"
              />
              <AvatarFallback></AvatarFallback>
            </Avatar> */}
          <div className="flex-1 overflow-hidden">
            <div className="flex text-xs">
              {/* <span className="overflow-hidden text-ellipsis whitespace-nowrap">
                  {snip.authors?.map((author) => author.name).join(", ")}
                </span> */}

              {/* <span className="px-1">&#183;</span>
                  <span className="whitespace-nowrap">{updatedAt}</span> */}
              <TimeSince dateString={snip.created_at} />
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

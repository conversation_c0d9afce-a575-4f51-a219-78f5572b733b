import type { SnipArticleVO } from '@repo/common/types/snip/app-types';
import { Avatar, AvatarFallback, AvatarImage } from '@repo/ui/components/ui/avatar';
import { Card, CardContent } from '@repo/ui/components/ui/card';
import {
  CHATGPT_ICON,
  DEEPSEEK_ICON,
  GEMINI_ICON,
  PERPLEXITY_ICON,
} from '@/components/snip/snip-content/ai-chat-snip';
import { TimeSince } from '@/utils/timeSince';
import { isChatGPT, isDeepSeek, isGemini, isPerplexity } from '@/utils/utils';

const getAvatar = (snip: SnipArticleVO) => {
  const thisIsChatGPT = isChatGPT(snip);
  const thisIsDeepSeek = isDeepSeek(snip);
  const thisIsGemini = isGemini(snip);
  const thisIsPerplexity = isPerplexity(snip);

  if (thisIsChatGPT) {
    return CHATGPT_ICON;
  } else if (thisIsDeepSeek) {
    return DEEPSEEK_ICON;
  } else if (thisIsGemini) {
    return GEMINI_ICON;
  } else if (thisIsPerplexity) {
    return PERPLEXITY_ICON;
  }

  return '';
};

export default function SnipThumbnailAIChatSnip({ snip }: { snip: SnipArticleVO }) {
  return (
    <Card className="relative overflow-hidden rounded-2xl border-none bg-card shadow-md transition-shadow hover:shadow-lg">
      <CardContent className="px-4 pb-5 pt-3">
        <Avatar className="mb-4 h-8 w-8 rounded-full">
          <AvatarImage src={getAvatar(snip)} />
          <AvatarFallback></AvatarFallback>
        </Avatar>

        <div className="mb-2 text-sm font-medium">{snip?.webpage?.site?.name}</div>

        {snip?.title && <div className="mb-2 line-clamp-3 text-base font-medium">{snip.title}</div>}
        {snip?.webpage?.description ? (
          <div className="relative mt-2 text-xs text-muted-foreground">
            <span className="text-ellipsis-line-3">{snip?.webpage?.description}</span>
          </div>
        ) : null}
        <div className="caption mt-2 flex flex-row text-caption">
          <div className="flex-1 overflow-hidden">
            <div className="flex text-xs">
              <TimeSince dateString={snip.created_at} />
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

export const SnipSmallThumbnailAIChat = ({ snip }: { snip: SnipArticleVO }) => {
  const {
    webpage: { site, title, description },
  } = snip;
  const header = title || site?.name;

  return (
    <Card className="relative flex h-full flex-col overflow-hidden rounded-2xl border-muted bg-card shadow-sm transition-shadow hover:shadow-lg">
      <CardContent className="flex flex-1 flex-col justify-between px-2 pb-2 pt-4">
        <Avatar className="mb-2 h-8 w-8 rounded-full">
          <AvatarImage src={getAvatar(snip)} />
          <AvatarFallback></AvatarFallback>
        </Avatar>
        <div className="line-clamp-2 max-h-[110px] text-sm font-medium">{header}</div>

        <div className="mt-1 line-clamp-2 break-all text-[10px] leading-[14px]">{description}</div>

        <div className="mt-2 text-[10px] text-caption">
          <TimeSince dateString={snip.updated_at} />
        </div>
      </CardContent>
    </Card>
  );
};

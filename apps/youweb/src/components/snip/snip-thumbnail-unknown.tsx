import type { SnipOtherWebpageVO } from '@repo/common/types/snip/app-types';
import { SnipStatusEnum } from '@repo/common/types/snip/types';
import { Avatar, AvatarFallback, AvatarImage } from '@repo/ui/components/ui/avatar';
import { Card, CardContent, CardHeader } from '@repo/ui/components/ui/card';
import { Skeleton } from '@repo/ui/components/ui/skeleton';
import { Earth } from '@/components/icon';
import { useTranslation } from '@/hooks/useTranslation';
import { TimeSince } from '@/utils/timeSince';
import { cn, shortenSiteName } from '@/utils/utils';

export default function SnipThumbnailUnknown({ snip }: { snip: SnipOtherWebpageVO }) {
  const { t } = useTranslation('Library.SnipList');
  const { webpage, status } = snip;
  const siteName = shortenSiteName(snip);
  if (status === SnipStatusEnum.FETCHING) {
    return (
      <Card
        className={cn(
          'relative overflow-hidden rounded-2xl border-none bg-card shadow-md transition-shadow hover:shadow-lg',
        )}
      >
        <CardContent className="p-4">
          <div className="mb-3 flex">
            <Avatar className="mr-2 h-4 w-4">
              <AvatarImage
                src={
                  webpage?.site?.favicon_url?.startsWith('http') ? webpage?.site?.favicon_url : ''
                }
                alt="favicon"
              />
              <AvatarFallback>
                <Earth size={14} />
              </AvatarFallback>
            </Avatar>
            <div className="footnote flex-1 overflow-hidden text-ellipsis whitespace-nowrap text-muted-foreground">
              {webpage?.url}
            </div>
          </div>
          <Skeleton className="duration-800 mt-3 h-[96px] w-full rounded-sm" />
          <Skeleton className="duration-800 mt-3 h-[10px] w-full rounded-sm" />
          <Skeleton className="duration-800 mt-3 h-[10px] w-full rounded-sm" />
          <Skeleton className="duration-800 mt-3 h-[10px] w-[106px] rounded-sm" />
          <div className="mt-5 flex h-[18px] flex-row items-center">
            <LoadingBook className="top-[-10px] mr-3 scale-[.15]" />{' '}
            <span className="text-sm text-muted-foreground">{t('saving')}</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="relative overflow-hidden rounded-2xl border-none bg-card shadow-md transition-shadow hover:shadow-lg">
      <CardHeader className="space-y-0 p-0 px-4"></CardHeader>
      <CardContent className="px-4 py-5">
        <div className="... title overflow-hidden text-ellipsis whitespace-nowrap">
          {webpage?.title || siteName}
        </div>
        <div className="mt-3 flex flex-row items-center">
          {webpage?.site?.favicon_url?.startsWith('http') && (
            <Avatar className="mr-2 h-4 w-4">
              <AvatarImage src={webpage?.site?.favicon_url} alt="favicon" />
              <AvatarFallback></AvatarFallback>
            </Avatar>
          )}
          <div className="footnote flex-1 overflow-hidden text-ellipsis whitespace-nowrap text-muted-foreground">
            {webpage?.url}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

export function LoadingBook({ className }: { className?: string }) {
  return null;
}

export const SnipSmallThumbnailUnknown = ({ snip }: { snip: SnipOtherWebpageVO }) => {
  const {
    webpage: { site, title, url },
  } = snip;
  const header = title || site?.name;

  return (
    <Card className="relative flex h-full flex-col overflow-hidden rounded-2xl border-muted bg-card shadow-sm transition-shadow hover:shadow-lg">
      <CardContent className="flex flex-1 flex-col justify-between px-2 pb-2 pt-4">
        {site.favicon_url?.startsWith('http') ? (
          <Avatar className="mb-2 h-8 w-8 rounded-full">
            <AvatarImage src={site.favicon_url} />
            <AvatarFallback></AvatarFallback>
          </Avatar>
        ) : (
          <Earth size={14} className="mr-1" />
        )}
        <div className="line-clamp-2 max-h-[110px] text-sm font-medium">{header}</div>

        <div className="mt-1 line-clamp-2 break-all text-[10px] leading-[14px]">{url}</div>

        <div className="mt-2 text-[10px] text-caption">
          <TimeSince dateString={snip.updated_at} />
        </div>

        {snip.status === SnipStatusEnum.FETCHING ? (
          <div className="absolute bottom-1 right-1">
            <LoadingBook className="scale-[.15]" />
          </div>
        ) : null}
      </CardContent>
    </Card>
  );
};

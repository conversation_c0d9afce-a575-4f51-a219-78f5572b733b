import {
  CreateThoughtVersionDto,
  <PERSON><PERSON>houghtD<PERSON>,
  <PERSON>Dto,
  TitleType as <PERSON>TitleType<PERSON>num,
  ThoughtVersionDto,
  CreateThoughtVersionDtoTypeEnum as ThoughtVersionTypeEnum,
} from '@repo/api/generated-client/snake-case/index';

export interface Thought extends ThoughtDto {}

export { ThoughtTitleTypeEnum };

export interface ThoughtVersion extends ThoughtVersionDto {}

export { ThoughtVersionTypeEnum };

export interface PatchThought extends PatchThoughtDto {}

export interface CreateThoughtVersion extends CreateThoughtVersionDto {}

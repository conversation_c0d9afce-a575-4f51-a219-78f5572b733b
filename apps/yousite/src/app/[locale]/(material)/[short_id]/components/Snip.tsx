'use client';

import { useIsMobile } from '@repo/ui/hooks/useIsMobile';
import { createConfig, SnipContainer, SnipProvider, snipDetail<PERSON>tom } from '@repo/ui-business-snip';
import { SnipArticleVO, SnipVO } from '@repo/ui-business-snip/typings/snip';
import { useHydrateAtoms } from 'jotai/utils';
import { ArrowUpRightFromSquare } from 'lucide-react';
import { useSearchParams } from 'next/navigation';
import { useMemo } from 'react';
import { Link } from '@/i18n/navigation';
import { apiClientOld, callHTTPStream } from '@/lib/request/client';
import SharedHeader from './header';
// import { useHydrateAtoms } from "jotai/utils";

export function Snip({ snip }: { snip: SnipVO }) {
  useHydrateAtoms([[snipDetail<PERSON>tom, snip]]);
  // console.log('snip', snip);
  const { webpage } = snip as SnipArticleVO;
  const url = webpage?.url;
  const searchParams = useSearchParams();
  const { isMobile } = useIsMobile();

  const linkNode = url ? (
    <Link
      href={url}
      target="_blank"
      className="footnote flex flex-nowrap items-center text-caption"
    >
      <ArrowUpRightFromSquare size={14} className="mr-[2px]" />
      <span className="max-w-[320px] overflow-hidden text-ellipsis whitespace-nowrap">
        {webpage?.site?.host || webpage?.site?.name || url}
      </span>
    </Link>
  ) : null;

  const operationArea = (
    <div>
      {linkNode}
      {/* <SaveMaterial short_id={short_id} hasLoggedIn={!!user} /> */}
    </div>
  );

  const config = useMemo(() => {
    return createConfig({
      apiClient: apiClientOld,
      searchParams,
      readonly: true,
      events: {},
      callHTTPStream: callHTTPStream,
      onlineVideo: {
        options: {
          showViewButton: !isMobile,
        },
      },
    });
  }, [isMobile, searchParams]);

  return (
    <>
      <SharedHeader extra={operationArea} />
      <main className="mx-auto max-w-[800px] px-6 py-5">
        <div className="snip-content">
          <SnipProvider config={config}>
            <SnipContainer />
          </SnipProvider>
        </div>
      </main>
    </>
  );
}

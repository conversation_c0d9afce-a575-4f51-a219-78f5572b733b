/**
 * 调试时，复制的share链接记得去掉 https 的 s
 */

import { EntityTypeEnum } from '@repo/common/types/entity/enum';
import { type Metadata } from 'next';
import { notFound } from 'next/navigation';

import './style.css';
import { ThoughtDto } from '@repo/api/generated-client/snake-case/index';
import { SnipArticleVO, SnipVO } from '@repo/ui-business-snip/typings/snip';
import { createApiClientOldServerSide } from '@/lib/request/server';
import { Snip } from './components/Snip';
import { ThoughtClient } from './components/ThoughtClient';

async function getEntity(
  _short_id: string,
): Promise<{ entity_type: EntityTypeEnum; entity: ThoughtDto | SnipVO } | null> {
  const apiClient = await createApiClientOldServerSide();
  const result = await apiClient.shareApi.getSharedEntity(_short_id, {
    headers: {
      Authorization: `Bearer ${process.env.CRON_SECRET}`,
    },
  });

  // console.log('rrr', result);

  // // @ts-ignore
  // result.entity.webpage = {
  //   url: 'https://www.youtube.com/watch?v=_DN0q3aKojU',
  //   normalized_url: 'www.youtube.com/watch?v=_DN0q3aKojU',
  //   title:
  //     '🎥 𝐩𝐥𝐚𝐲𝐥𝐢𝐬𝐭ㅣMovie music that you listen to sitting in a rural fieldㅣCall me by your name, Il Postino',
  //   description:
  //     '엔딩크레딧의 여운이 담긴 플레이리스트를 들려드리고자 합니다:)\n\n〰️ 모든 배경 영상과 사진은 직접 촬영해요 🎥/📸 :)\n〰️ 장소 : 제주 가파도 4월\n\n🎥수록곡🎥\nl Postino Poeta (일포스티노 OST)\r\nMystery of Love (콜미바이유너네임 OST)\r\nCinema paradiso (시네마천국 OST)\n\rMarried Life (업 OST)\r\nA Town with an Ocean View (마녀배달부키키 OST)\n\rSummer (기쿠지로의 여름 OST)\r\nBig Country (미나리 OST)\n\n#영화playlist #엔딩크레딧 #credit',
  //   site: {
  //     name: 'YouTube',
  //     host: 'www.youtube.com',
  //     favicon_url: 'https://www.youtube.com/s/desktop/b5305900/img/logos/favicon_32x32.png',
  //   },
  // };

  // // @ts-ignore
  // result.entity.description = {
  //   format: 'reader-html',
  //   raw: '<p>엔딩크레딧의 여운이 담긴 플레이리스트를 들려드리고자 합니다:)</p><p>〰️ 모든 배경 영상과 사진은 직접 촬영해요 🎥/📸 :)</p><p>〰️ 장소 : 제주 가파도 4월</p><p>🎥수록곡🎥</p><p>l Postino Poeta (일포스티노 OST)\r</p><p>Mystery of Love (콜미바이유너네임 OST)\r</p><p>Cinema paradiso (시네마천국 OST)</p><p>\rMarried Life (업 OST)\r</p><p>A Town with an Ocean View (마녀배달부키키 OST)</p><p>\rSummer (기쿠지로의 여름 OST)\r</p><p>Big Country (미나리 OST)</p><p>#영화playlist #엔딩크레딧 #credit</p>',
  //   plain:
  //     '엔딩크레딧의 여운이 담긴 플레이리스트를 들려드리고자 합니다:)\n\n〰️ 모든 배경 영상과 사진은 직접 촬영해요 🎥/📸 :)\n〰️ 장소 : 제주 가파도 4월\n\n🎥수록곡🎥\nl Postino Poeta (일포스티노 OST)\r\nMystery of Love (콜미바이유너네임 OST)\r\nCinema paradiso (시네마천국 OST)\n\rMarried Life (업 OST)\r\nA Town with an Ocean View (마녀배달부키키 OST)\n\rSummer (기쿠지로의 여름 OST)\r\nBig Country (미나리 OST)\n\n#영화playlist #엔딩크레딧 #credit',
  //   language: 'en-US',
  // };

  return result as unknown as { entity_type: EntityTypeEnum; entity: ThoughtDto | SnipVO };
}

export default async function Page({ params }: { params: { short_id: string } }) {
  const { short_id } = await params;
  const result = await getEntity(short_id);
  if (!result) {
    notFound();
  }

  const { entity_type, entity } = result;

  // 根据实体类型渲染不同的组件
  switch (entity_type) {
    case EntityTypeEnum.THOUGHT:
      return <ThoughtClient thought={entity as ThoughtDto} />;
    case EntityTypeEnum.SNIP:
      return <Snip snip={entity as SnipVO} />;
    default:
      notFound();
  }
}

export async function generateMetadata({
  params,
}: {
  params: { short_id: string };
}): Promise<Metadata> {
  const { short_id } = await params;
  const result = await getEntity(short_id);
  if (!result) {
    notFound();
  }

  const { entity_type, entity } = result;

  let title = entity.title;
  let description = '';

  // 根据实体类型获取不同的元数据
  switch (entity_type) {
    case EntityTypeEnum.THOUGHT: {
      const thought = entity as ThoughtDto;
      title = title ?? 'Untitled Thought';
      description = thought?.content?.plain?.substring?.(0, 100) || '';
      break;
    }
    case EntityTypeEnum.SNIP: {
      const snip = entity as SnipArticleVO;
      title = title ?? 'Untitled Snip';
      description = snip?.content?.plain?.substring?.(0, 100) || '';
      break;
    }
    default:
      return {};
  }

  return {
    title,
    description,
    openGraph: {
      title,
      description,
      type: 'article',
      url: `${process.env.NEXT_PUBLIC_YOUMIND_SITE_ORIGIN}/${short_id}`,
      images: [{ url: '/cover.png' }],
      siteName: 'YouMind',
    },
    twitter: {
      card: 'summary_large_image',
      title,
      description,
      images: [{ url: '/cover.png' }],
    },
  };
}
